import enum
from datetime import datetime, timezone

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>n,
    DateTime,
    Enum,
    Float,
    Foreign<PERSON>ey,
    Integer,
    Numeric,
    String,
    Text,
    func,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship, validates

from common_models.base_model import Base, BaseUuidMixin, VisibilityMixin


class Status(enum.Enum):
    TODO = "TODO" # deprecated
    SUBMITTED = "SUBMITTED" # deprecated
    QUEUED = "QUEUED"
    IN_PROGRESS = "IN_PROGRESS"
    DONE = "DONE"
    FAILED = "FAILED"
    GITHUB_PENDING = "GITHUB_PENDING"
    GITHUB_COMPLETED = "GITHUB_COMPLETED"
    COMPLETED = "COMPLETED" # deprecated


class SubscriptionType(enum.Enum):
    FREE = "FREE"
    PRO = "PRO"
    ENTERPRISE = "ENTERPRISE"
    TEAMS = "TEAMS"


class SubscriptionStatus(enum.Enum):
    NONE = "NONE"
    ACTIVE = "ACTIVE"
    TRIALING = "TRIALING"
    CANCELLED = "CANCELLED"
    PAST_DUE = "PAST_DUE"
    CANCELLING = "CANCELLING"


class PromptStatus(enum.Enum):
    DRAFT = "DRAFT"
    SUBMITTED = "SUBMITTED"


class GithubInstallationType(enum.Enum):
    ORGANIZATION = "ORGANIZATION"
    USER = "USER"


class GithubInstallationStatus(enum.Enum):
    PENDING = "PENDING"
    ACTIVE = "ACTIVE"
    REJECTED = "REJECTED"
    UNINSTALLED = "UNINSTALLED"
    SUSPENDED = "SUSPENDED"
    FAILED = "FAILED"


class GitHubRepositoryStatus(enum.Enum):
    PENDING = "PENDING"
    ACTIVE = "ACTIVE"
    ARCHIVED = "ARCHIVED"
    DELETED = "DELETED"
    DISABLED = "DISABLED"
    ONBOARDING = "ONBOARDING"
    FAILED = "FAILED"


class ReportStatus(enum.Enum):
    QUEUED = "QUEUED"
    IN_PROGRESS = "IN_PROGRESS"
    READY = "READY"


class AttachmentStatus(enum.Enum):
    UPLOADED = "UPLOADED"
    PENDING_SCAN = "PENDING_SCAN"
    SAFE = "SAFE"
    INFECTED = "INFECTED"
    FAILED = "FAILED"


class CompanyStatus(enum.Enum):
    ACTIVE = "ACTIVE"
    SUSPENDED = "SUSPENDED"


class TeamMemberRole(enum.Enum):
    OWNER = "OWNER" # deprecated
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"
    SUPER_ADMIN = "SUPER_ADMIN"


class ProjectInitialType(enum.Enum):
    NEW_PRODUCT = "NEW_PRODUCT"
    EXISTING_PRODUCT = "EXISTING_PRODUCT"


class TechSpecJobType(enum.Enum):
    NEW_PRODUCT = "NEW_PRODUCT"
    EXISTING_PRODUCT = "EXISTING_PRODUCT"
    ADD_FEATURE = "ADD_FEATURE"
    REFACTOR_CODE = "REFACTOR_CODE"
    SYNC_TECH_SPEC = "SYNC_TECH_SPEC"
    FIX_BUGS = "FIX_BUGS"
    FIX_CVES = "FIX_CVES"
    ADD_TESTING = "ADD_TESTING"
    DOCUMENT_CODE = "DOCUMENT_CODE"
    CUSTOM = "CUSTOM"


class ProjectRunStage(enum.Enum):
    TECH_SPEC = "TECH_SPEC"
    CODEGEN = "CODEGEN"


class ProjectRunStatus(enum.Enum):
    TODO = "TODO" # deprecated
    QUEUED = "QUEUED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED" # deprecated
    FAILED = "FAILED"
    REJECTED = "REJECTED"
    CANCELLED = "CANCELLED"
    DONE = "DONE"


class ProjectRunType(enum.Enum):
    # Tech spec run types.
    NEW_PRODUCT = "NEW_PRODUCT"
    EXISTING_PRODUCT = "EXISTING_PRODUCT"
    ADD_FEATURE = "ADD_FEATURE"
    REFACTOR_CODE = "REFACTOR_CODE"
    SYNC_TECH_SPEC = "SYNC_TECH_SPEC"
    FIX_BUGS = "FIX_BUGS"
    FIX_CVES = "FIX_CVES"  # security
    ADD_TESTING = "ADD_TESTING"
    DOCUMENT_CODE = "DOCUMENT_CODE"
    CUSTOM = "CUSTOM"

    # Code gen run types.
    GRAPHING = "GRAPHING"
    CODEGEN = "CODEGEN"
    REFACTOR = "REFACTOR"


class BlitzyCommitStatus(enum.Enum):
    PENDING = "PENDING"
    MERGED = "MERGED"
    CLOSED = "CLOSED"
    DONE = "DONE"


class BlitzyCommitPRAction(enum.Enum):
    NOT_TAKEN = "NOT_TAKEN"
    CLOSED = "CLOSED"
    OPEN = "OPEN"


class VersionControlSystem(enum.Enum):
    GITHUB = "GITHUB"
    AZURE_DEVOPS = "AZURE_DEVOPS"


class AllotmentType(enum.Enum):
    MANUAL = "MANUAL"
    SYSTEM = "SYSTEM"


class DurationUnit(enum.Enum):
    DAY = "DAY"
    WEEK = "WEEK"
    MONTH = "MONTH"
    YEAR = "YEAR"


class PlatformMetrics(BaseUuidMixin, Base):
    __tablename__ = "platform_metrics"

    trial_user_code_submissions = Column(Integer, default=0)
    metrics_data = Column(JSONB)


class User(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "users"

    user_id = Column(String(100))
    first_name = Column(String(100))
    last_name = Column(String(100))
    email = Column(String(255))
    company = Column(String(255))
    avatar_blob = Column(Text)
    auth_mechanism = Column(String(50))
    is_verified = Column(Boolean, default=False)
    is_github_authenticated = Column(Boolean, default=False)
    company_id = Column(String, ForeignKey("companies.id"))
    registration_completed = Column(Boolean, default=False)

    projects = relationship("Project", back_populates="user")
    subscriber = relationship("Subscriber", back_populates="user", uselist=False)
    subscription = relationship("Subscription", back_populates="user", uselist=False)
    github_installation = relationship("GithubInstallation", back_populates="user", uselist=False)
    geolocation = relationship("GeoLocation", back_populates="user", uselist=False)
    github_repository = relationship("GitHubRepository", back_populates="user", uselist=False)
    pending_installation_requests = relationship("PendingInstallationRequest", back_populates="user", uselist=False)
    company_info = relationship("Company", back_populates="users", uselist=False)
    teammember = relationship("TeamMember", back_populates="users")
    user_config = relationship("UserConfig", back_populates="user", uselist=False)


class Subscription(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "subscriptions"

    user_id = Column(String, ForeignKey("users.id"))
    stripe_customer_id = Column(String(50))
    stripe_subscription_id = Column(String(50))
    plan_name = Column(Enum(SubscriptionType), nullable=False) # (FREE, PRO, TEAMS, ENTERPRISE)
    plan_owner_id = Column(String, nullable=False) # owning entity id -- user (for FREE, PRO), team (for TEAMS), company (for ENTERPRISE)
    plan_subscriber_id = Column(String(50)) # unique id for this plan subscriber
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))
    trial_start_date = Column(DateTime(timezone=True))
    trial_end_date = Column(DateTime(timezone=True))
    current_period_start_date = Column(DateTime(timezone=True))
    current_period_end_date = Column(DateTime(timezone=True))
    status = Column(Enum(SubscriptionStatus), nullable=False)
    remaining_runs = Column(Integer, nullable=False)
    is_trialing = Column(Boolean, default=False)
    has_trial_received = Column(Boolean, default=False)
    quota_lines_generated = Column(Integer, nullable=True)
    quota_lines_onboarded = Column(Integer, nullable=True)
    charge_lines_generated_overage = Column(Numeric, nullable=True)
    charge_lines_onboarded_overage = Column(Numeric, nullable=True)

    user = relationship("User", back_populates="subscription")
    subscribers = relationship("Subscriber", back_populates="subscription", foreign_keys="plan_subscriber_id")
    subscription_benefit = relationship("SubscriptionBenefit", back_populates="subscription")
    aggregate_metering = relationship("AggregateMetering", back_populates="subscription")

    def is_trial_active(self):
        """Check if the user is currently in a trial period."""
        now = datetime.now(timezone.utc)
        return self.trial_start_date and self.trial_end_date and self.trial_start_date <= now <= self.trial_end_date


class SubscriptionConfig(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "subscription_configs"

    plan_name = Column(Enum(SubscriptionType), nullable=False)
    quota_lines_generated_base = Column(Integer, nullable=False)
    quota_lines_onboarded_base = Column(Integer, nullable=False)
    charge_lines_generated_overage_base = Column(Numeric, nullable=False)
    charge_lines_onboarded_overage_base = Column(Numeric, nullable=False)
    duration = Column(Integer, nullable=False)
    duration_unit = Column(Enum(DurationUnit), nullable=False)
    description = Column(Text)

    subscription_benefit = relationship("SubscriptionBenefit", back_populates="subscription_config")


class SubscriptionBenefit(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "subscription_benefits"

    subscription_id = Column(String, ForeignKey("subscriptions.id"))
    subscription_config_id = Column(String, ForeignKey("subscription_configs.id"))
    quota_lines_generated_delta = Column(Integer, nullable=False)
    quota_lines_onboarded_delta = Column(Integer, nullable=False)
    charge_lines_generated_overage_delta = Column(Numeric, nullable=False)
    charge_lines_onboarded_overage_delta = Column(Numeric, nullable=False)
    allotment_type = Column(Enum(AllotmentType), nullable=False)
    allotted_by = Column(String)

    subscription = relationship("Subscription", back_populates="subscription_benefit")
    subscription_config = relationship("SubscriptionConfig", back_populates="subscription_benefit")


class Subscriber(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "subscribers"

    plan_subscriber_id = Column(String, ForeignKey("subscriptions.plan_subscriber_id"))
    user_id = Column(String, ForeignKey("users.id"))


# TODO(Chaitanya): Deprecate this class eventually.
class Job(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "jobs"

    project_id = Column(String, ForeignKey("projects.id"))
    stage_type = Column(String(50))
    status = Column(Enum(Status), nullable=False)
    job_metadata = Column(JSONB)

    project = relationship("Project", back_populates="jobs")


class ProjectRun(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "project_runs"

    project_id = Column(String, ForeignKey("projects.id"), nullable=False)
    code_gen_id = Column(String, ForeignKey("code_generations.id"))
    tech_spec_id = Column(String, ForeignKey("technical_specs.id"))

    stage = Column(Enum(ProjectRunStage))
    run_type = Column(Enum(ProjectRunType))
    status = Column(Enum(ProjectRunStatus))

    start_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))

    job_metadata = Column(JSONB)

    project = relationship("Project", back_populates="project_runs")
    code_gen = relationship("CodeGeneration", back_populates="project_runs", foreign_keys=code_gen_id)
    tech_spec = relationship("TechnicalSpec", back_populates="project_runs", foreign_keys=tech_spec_id)
    blitzy_commit = relationship("BlitzyCommit", back_populates="project_run", uselist=False)
    branch_locks = relationship("BranchLock", back_populates="project_run")
    project_run_metering = relationship("ProjectRunMetering", back_populates="project_run")
    aggregate_metering_trail = relationship("AggregateMeteringTrail", back_populates="project_run")


class Project(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "projects"

    name = Column(String(255), nullable=False)
    status = Column(Enum(Status), nullable=False)
    prompt = Column(Text)
    prompt_status = Column(Enum(PromptStatus))
    prompt_updated_at = Column(DateTime(timezone=True))
    repo_prefix = Column(String(50))
    repo_url = Column(String(1000))
    user_id = Column(String, ForeignKey("users.id"))
    code_gen_submitted = Column(Boolean, default=False)
    initial_type = Column(Enum(ProjectInitialType))
    project_valid = Column(Boolean, default=False)
    project_invalid_reason = Column(Text)
    is_disabled = Column(Boolean, default=False)
    disable_reason = Column(Text)

    user = relationship("User", back_populates="projects")
    teams = relationship("Team", secondary="teammembers", back_populates="projects")
    software_requirement = relationship("SoftwareRequirement", uselist=False, back_populates="project")
    technical_spec = relationship("TechnicalSpec", uselist=False, back_populates="project")
    code_generation = relationship("CodeGeneration", uselist=False, back_populates="project")
    jobs = relationship("Job", back_populates="project")
    github_project_repo = relationship("GitHubProjectRepo", back_populates="project")
    github_repository = relationship("GitHubRepository", back_populates="project")
    github_branch_pattern_project = relationship("GithubBranchPatternProject", back_populates="project")
    project_runs = relationship("ProjectRun", back_populates="project")
    branch_locks = relationship("BranchLock", back_populates="project")
    attachments = relationship("AttachmentMetadata", back_populates="project")


class SoftwareRequirement(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "software_requirements"

    status = Column(Enum(Status), nullable=False)
    requirements = Column(Text)
    approved_at = Column(DateTime(timezone=True))
    link = Column(String(255))
    storage_type = Column(String(50))
    job_metadata = Column(JSONB)

    project_id = Column(String, ForeignKey("projects.id"))
    job_id = Column(String, ForeignKey("jobs.id"))

    project = relationship("Project", back_populates="software_requirement", uselist=False)
    jobs = relationship("Job")


class CodeGeneration(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "code_generations"

    status = Column(Enum(Status), nullable=False)
    tech_spec_id = Column(String, ForeignKey("technical_specs.id"))
    code_repo_url = Column(String(255))
    repo_type = Column(String(50))
    approved_at = Column(DateTime(timezone=True))
    ready_at = Column(DateTime(timezone=True))
    link = Column(String(255))
    storage_type = Column(String(50))
    job_metadata = Column(JSONB)

    project_id = Column(String, ForeignKey("projects.id"))
    job_id = Column(String, ForeignKey("jobs.id"))

    project = relationship("Project", back_populates="code_generation")
    project_runs = relationship("ProjectRun", back_populates="code_gen")
    tech_spec = relationship("TechnicalSpec", foreign_keys=tech_spec_id)
    blitzy_commit = relationship("BlitzyCommit", back_populates="code_gen", uselist=False)
    jobs = relationship("Job")


class TechnicalSpec(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "technical_specs"

    prompt = Column(Text)
    status = Column(Enum(Status), nullable=False)
    approved_at = Column(DateTime(timezone=True))
    version = Column(Integer, default=0)
    job_type = Column(Enum(TechSpecJobType))
    link = Column(String(255))
    storage_type = Column(String(50))
    job_metadata = Column(JSONB)

    project_id = Column(String, ForeignKey("projects.id"))
    job_id = Column(String, ForeignKey("jobs.id"))
    pdf_report_status = Column(Enum(ReportStatus))
    project_run_id = Column(String, ForeignKey("project_runs.id"))

    project = relationship("Project", back_populates="technical_spec", uselist=False)
    jobs = relationship("Job")
    project_runs = relationship("ProjectRun", foreign_keys=[ProjectRun.tech_spec_id], back_populates="tech_spec")
    attachments = relationship("AttachmentMetadata", back_populates="technical_spec")


class GithubInstallation(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "github_installations"

    # Existing fields from your model
    installation_id = Column(String(100), nullable=False)
    installation_type = Column(Enum(GithubInstallationType), nullable=False)
    status = Column(Enum(GithubInstallationStatus), nullable=False)

    # org_name or username
    target_name = Column(String(255), nullable=False)
    # GitHub's org_id or user_id
    target_id = Column(String(100), nullable=False)

    # Tracking approval
    requires_approval = Column(Boolean, default=False)
    requested_by = Column(String, nullable=True)
    requested_at = Column(DateTime(timezone=True), nullable=True)
    approved_by = Column(String, nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    installed_at = Column(DateTime(timezone=True), nullable=False, default=func.now())
    uninstalled_at = Column(DateTime(timezone=True))

    # Repo scope.
    repository_selection = Column(String, default="all")  # 'all' or 'selected'
    selected_repositories = Column(JSONB, nullable=True)

    # New field to indicate if this installation is shared
    is_shared = Column(Boolean, default=False)

    # Type of version control system we use
    # TODO: when merged to prod, make this field required and no default
    svc_type = Column(Enum(VersionControlSystem), nullable=False, default=VersionControlSystem.GITHUB)

    # GitHub app metadata
    installation_metadata = Column(JSONB)  # Store GitHub's installation response

    # Relationship to User model
    user = relationship("User", back_populates="github_installation")
    repositories = relationship("GitHubRepository", back_populates="github_installation")

    access_grants = relationship("GitHubInstallationAccess", back_populates="installation")

    @property
    def is_active(self):
        return self.status == GithubInstallationStatus.ACTIVE

    @validates("status")
    def validate_status_change(self, key, value):
        """Update timestamps on status change"""
        if value == GithubInstallationStatus.UNINSTALLED:
            self.uninstalled_at = datetime.now(timezone.utc)
        return value


class PendingInstallationRequest(BaseUuidMixin, Base):
    __tablename__ = "pending_installation_requests"

    request_by = Column(String, nullable=False)
    installation_id = Column(String, nullable=True)
    user_id = Column(String, ForeignKey("users.id"))
    user = relationship("User", back_populates="pending_installation_requests")


class GeoLocation(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "geolocations"

    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    ip_address = Column(String(45))
    country_code = Column(String(2))
    city = Column(String(100))
    region = Column(String(100))
    latitude = Column(Float(53))
    longitude = Column(Float(53))
    timezone = Column(String(50))

    user = relationship("User", back_populates="geolocation")


class GitHubRepository(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "github_repositories"

    repo_id = Column(String(100))
    repo_name = Column(String(255), nullable=False)
    repo_full_name = Column(String(255), nullable=False)
    status = Column(Enum(GitHubRepositoryStatus), nullable=False)

    project_id = Column(String, ForeignKey("projects.id"), nullable=False)
    github_installation_id = Column(String, ForeignKey("github_installations.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"))

    is_archived = Column(Boolean, default=False)
    onboarded_at = Column(DateTime(timezone=True))

    previous_names = Column(JSONB)
    repository_metadata = Column(JSONB)

    project = relationship("Project", back_populates="github_repository")
    github_installation = relationship("GithubInstallation", back_populates="repositories")
    user = relationship("User", back_populates="github_repository")


class Company(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "companies"

    name = Column(String(100))
    domain = Column(String(100))
    status = Column(Enum(CompanyStatus))
    is_default = Column(Boolean, default=True)
    settings = Column(JSONB)

    team = relationship("Team", back_populates="company")
    teammember = relationship("TeamMember", back_populates="company")
    users = relationship("User", back_populates="company_info")


class Team(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "teams"

    company_id = Column(String, ForeignKey("companies.id"))
    name = Column(String(100))
    is_default = Column(Boolean, default=True)
    is_admin_team = Column(Boolean, default=True)
    settings = Column(JSONB)

    company = relationship("Company", back_populates="team")
    teammember = relationship("TeamMember", back_populates="team")
    projects = relationship("Project", secondary="teammembers", back_populates="teams")


class TeamMember(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "teammembers"

    team_id = Column(String, ForeignKey("teams.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    company_id = Column(String, ForeignKey("companies.id"))
    role = Column(Enum(TeamMemberRole))
    role_settings = Column(JSONB)
    joined_at = Column(DateTime(timezone=True))

    team = relationship("Team", back_populates="teammember")
    company = relationship("Company", back_populates="teammember")
    users = relationship("User", back_populates="teammember")


class UsageType(enum.Enum):
    SOURCE = "SOURCE"
    TARGET = "TARGET"


class GithubBranchPatternStatus(enum.Enum):
    ONBOARDED = "ONBOARDING"
    RENAMING = "RENAMING"
    DELETING = "DELETING"
    DELETED = "DELETED"
    CLEANUP = "CLEANUP"


class GitHubProjectRepo(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "github_project_repo"

    project_id = Column(String, ForeignKey("projects.id"))
    org_id = Column(String, nullable=False)
    org_name = Column(String, nullable=False)
    installation_id = Column(String, nullable=False)
    repo_name = Column(String, nullable=False)
    repo_id = Column(String, nullable=False)
    branch_name = Column(String, nullable=False)
    usage_type = Column(Enum(UsageType), nullable=False)
    current_commit_hash = Column(String)
    previous_commit_hash = Column(String)
    onboarding_commit_hash = Column(String)
    github_current_commit_hash = Column(String)
    azure_project_id = Column(String)
    azure_org_id = Column(String)
    last_scan_at = Column(DateTime(timezone=True))
    needs_scan = Column(Boolean, default=False)
    create_repo = Column(Boolean, default=False)
    repo_metadata = Column(JSONB)
    branch_lock = Column(Boolean, default=False)

    project = relationship("Project", back_populates="github_project_repo")


class GitHubBranchPattern(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "github_branch_patterns"

    org_name = Column(String, nullable=False)
    repo_name = Column(String, nullable=False)
    branch_name = Column(String, nullable=False)
    repo_id = Column(String)
    company_id = Column(String)
    status = Column(Enum(GithubBranchPatternStatus), nullable=False)


class GithubBranchPatternProject(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "github_branch_pattern_projects"

    pattern_id = Column(String, ForeignKey("github_branch_patterns.id"), nullable=False)
    project_id = Column(String, ForeignKey("projects.id"), nullable=False)
    usage_type = Column(Enum(UsageType), nullable=False)

    project = relationship("Project", back_populates="github_branch_pattern_project")
    pattern = relationship("GitHubBranchPattern")


class BlitzyCommit(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "blitzy_commits"

    project_run_id = Column(String, ForeignKey("project_runs.id"), nullable=False)
    code_gen_id = Column(String, ForeignKey("code_generations.id"))
    org_name = Column(String)
    repo_name = Column(String)
    repo_id = Column(String)
    branch_name = Column(String)
    repo_url = Column(String)
    version_control_system = Column(Enum(VersionControlSystem))
    blitzy_commit_hash = Column(String)
    blitzy_commit_url = Column(String)
    blitzy_branch_url = Column(String)
    status = Column(Enum(BlitzyCommitStatus), nullable=False)
    original_head_commit_hash = Column(String)
    pr_number = Column(Integer)
    pr_link = Column(String)
    pr_action = Column(Enum(BlitzyCommitPRAction))
    commit_metadata = Column(JSONB)
    resolved_at = Column(DateTime(timezone=True))

    project_run = relationship("ProjectRun", back_populates="blitzy_commit")
    code_gen = relationship("CodeGeneration", back_populates="blitzy_commit", foreign_keys=code_gen_id)


class TechSpecDocumentContext(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "tech_spec_document_contexts"

    company_id = Column(String)
    project_id = Column(String)
    team_id = Column(String)
    tech_spec_id = Column(String)
    org_name = Column(String)
    repo_name = Column(String)
    repo_id = Column(String)
    branch_id = Column(String)
    branch_name = Column(String)
    head_commit_hash = Column(String)
    context_metadata = Column(JSONB)


class CloudRunJobTrackerPhase(enum.Enum):
    TECHNICAL_SPEC = "TECHNICAL_SPEC"
    CODE_GENERATION = "CODE_GENERATION"
    NOT_AVAILABLE = "NOT_AVAILABLE"


class CloudRunJobTrackerStatus(enum.Enum):
    QUEUED = "QUEUED"
    RUNNING = "RUNNING"
    IN_PROGRESS = "IN_PROGRESS"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED",
    SUCCESS = "SUCCESS"
    DELETED = "DELETED"
    RE_TRIGGERED = "RE_TRIGGERED"
    SUCCESS_LATER = "SUCCESS_LATER"


class CloudRunJobTracker(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "cloud_run_job_trackers"

    project_id = Column(String, ForeignKey("projects.id"), index=True)
    tech_spec_id = Column(String, index=True)
    code_gen_id = Column(String, index=True)
    job_id = Column(String, index=True)
    job_phase = Column(Enum(CloudRunJobTrackerPhase), index=True)
    job_status = Column(Enum(CloudRunJobTrackerStatus), index=True)
    job_name = Column(String, index=True)
    event_data = Column(JSONB)
    job_submission_metadata = Column(JSONB)
    user_id = Column(String, ForeignKey("users.id"), index=True)
    is_triggered = Column(Boolean, default=False, index=True)
    trigger_topic = Column(String, index=True)
    original_created_at = Column(DateTime(timezone=True), index=True)

    project = relationship("Project", foreign_keys=project_id)
    user = relationship("User", foreign_keys=user_id)
    tech_spec = relationship("TechnicalSpec",
                             foreign_keys=tech_spec_id,
                             primaryjoin="CloudRunJobTracker.tech_spec_id == TechnicalSpec.id")
    code_gen = relationship("CodeGeneration",
                            foreign_keys=code_gen_id,
                            primaryjoin="CloudRunJobTracker.code_gen_id == CodeGeneration.id")


class BranchLockReason(enum.Enum):
    ONBOARDING_REPO = "ONBOARDING_REPO"
    SYNC_TECH_SPEC = "SYNC_TECH_SPEC"


class BranchLock(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "branch_locks"

    # Branch identifiers
    branch_pattern_id = Column(String, ForeignKey("github_branch_patterns.id"))
    project_repo_id = Column(String, ForeignKey("github_project_repo.id"))

    # Lock operation info
    project_run_id = Column(String, ForeignKey("project_runs.id"))
    project_id = Column(String, ForeignKey("projects.id"))
    lock_reason = Column(Enum(BranchLockReason), nullable=False)
    is_active = Column(Boolean, default=True)

    # Timestamps
    locked_at = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    released_at = Column(DateTime(timezone=True))

    # Relationships
    project = relationship("Project", back_populates="branch_locks")
    project_run = relationship("ProjectRun", back_populates="branch_locks")
    branch_pattern = relationship("GitHubBranchPattern", backref="locks")
    project_repo = relationship("GitHubProjectRepo", backref="locks")

    # Add a method to release the lock
    def release(self):
        self.is_active = False
        self.released_at = datetime.now(timezone.utc)


class AccessType(enum.Enum):
    USER = "USER"
    TEAM = "TEAM"


class AccessRole(enum.Enum):
    OWNER = "OWNER"
    ADMIN = "ADMIN"
    WRITE = "WRITE"
    READ = "READ"


class GitHubInstallationAccess(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "github_installation_access"

    integration_id = Column(String, ForeignKey("github_installations.id"), nullable=False)
    access_type = Column(Enum(AccessType), nullable=False)
    entity_id = Column(String, nullable=False)
    role = Column(Enum(AccessRole), nullable=False)
    is_owner = Column(Boolean, default=False)
    granted_by_user_id = Column(String, ForeignKey("users.id"), nullable=True)
    granted_at = Column(DateTime(timezone=True), nullable=False, default=func.now())

    # Relationships
    installation = relationship("GithubInstallation", back_populates="access_grants")
    granted_by = relationship("User", foreign_keys=granted_by_user_id)

    def get_entity(self, session):
        """
        Fetches an entity from the database based on access type.

        :param session: Database session used for querying entities.
        :return: Returns a User or Team object if found, otherwise None.
        """
        if self.access_type == AccessType.USER:
            return session.query(User).filter(User.id == self.entity_id).first()
        elif self.access_type == AccessType.TEAM:
            return session.query(Team).filter(Team.id == self.entity_id).first()
        return None


class UserConfig(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "user_configs"

    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    tech_spec_notification_enabled = Column(Boolean, default=True)
    code_gen_notification_enabled = Column(Boolean, default=True)
    platform_config = Column(JSONB)

    user = relationship("User", back_populates="user_config")


class AttachmentMetadata(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "attachment_metadata"

    project_id = Column(String, ForeignKey("projects.id"), nullable=False)
    tech_spec_id = Column(String, ForeignKey("technical_specs.id"), nullable=True)
    file_name = Column(String(255), nullable=False)
    mime_type = Column(String(100), nullable=False)
    file_size = Column(Integer, nullable=False)
    gcs_path = Column(String(500), nullable=False)
    uploaded_by_user_id = Column(String, ForeignKey("users.id"), nullable=False)
    upload_timestamp = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    user_description = Column(Text, nullable=True)
    status = Column(String(50), default=AttachmentStatus.UPLOADED.value, nullable=False)
    version = Column(String(50), default="1.0", nullable=False)

    # Relationships
    project = relationship("Project", back_populates="attachments", foreign_keys=project_id)
    technical_spec = relationship("TechnicalSpec", back_populates="attachments", foreign_keys=tech_spec_id)
    uploaded_by_user = relationship("User", foreign_keys=uploaded_by_user_id)


class AggregateMeteringType(enum.Enum):
    PROJECT = "PROJECT"
    USER = "USER"
    TEAM = "TEAM"
    COMPANY = "COMPANY"

# collects metering data from each project run
class ProjectRunMetering(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "project_run_metering"

    project_run_id = Column(String, ForeignKey("project_runs.id"), nullable=False)

    # estimated statistics (for record-keeping only)
    estimated_lines_generated = Column(Integer)
    estimated_hours_saved = Column(Float)

    # actual statistis
    files_onboarded = Column(Integer)
    lines_onboarded = Column(Integer)
    files_touched = Column(Integer)
    lines_added = Column(Integer)
    lines_edited = Column(Integer)
    lines_removed = Column(Integer)
    lines_generated = Column(Integer) # lines_added + lines_edited + lines_removed
    hours_saved = Column(Float)

    project_run = relationship("ProjectRun", back_populates="project_run_metering")


# aggregates metering data across multiple project runs -- at the project level, and user/team/company level as
# governed by the subscription type (separate row for each type of aggregation)
class AggregateMetering(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "aggregate_metering"

    entity_type = Column(Enum(AggregateMeteringType), nullable=False)
    entity_id = Column(String, nullable=False) # e.g., project_id, user_id, team_id, company_id
    subscription_id = Column(String, ForeignKey("subscriptions.id"), nullable=False)

    files_onboarded = Column(Integer)
    lines_onboarded = Column(Integer)
    files_touched = Column(Integer)
    lines_added = Column(Integer)
    lines_edited = Column(Integer)
    lines_removed = Column(Integer)
    lines_generated = Column(Integer) # lines_added + lines_edited + lines_removed
    hours_saved = Column(Float)

    subscription = relationship("Subscription", back_populates="aggregate_metering")
    aggregate_metering_trail = relationship("AggregateMeteringTrail", back_populates="aggregate_metering")

    def get_entity(self, session):
        """
        Fetches an entity from the database based on aggregate level type.

        :param session: Database session used for querying entities.
        :return: Returns a Project, User, Team or Company object if found, otherwise None.
        """
        if self.entity_type == AggregateMeteringType.PROJECT:
            return session.query(Project).filter(Project.id == self.entity_id).first()
        elif self.entity_type == AggregateMeteringType.USER:
            return session.query(User).filter(User.id == self.entity_id).first()
        elif self.entity_type == AggregateMeteringType.TEAM:
            return session.query(Team).filter(Team.id == self.entity_id).first()
        elif self.entity_type == AggregateMeteringType.COMPANY:
            return session.query(Company).filter(Company.id == self.entity_id).first()
        return None


# track what project runs contributed to a particular aggregate of metering data
class AggregateMeteringTrail(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "aggregate_metering_trail"

    aggregate_metering_id = Column(String, ForeignKey("aggregate_metering.id"), nullable=False)
    project_run_id = Column(String, ForeignKey("project_runs.id"), nullable=False)

    aggregate_metering = relationship("AggregateMetering", back_populates="aggregate_metering_trail")
    project_run = relationship("ProjectRun", back_populates="aggregate_metering_trail")


# track estimates from technical specifications that are actively being used in the code generation process as reserved quota
class ReservedEstimates(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "reserved_estimates"

    subscription_id = Column(String, ForeignKey("subscriptions.id"), nullable=False)
    project_id = Column(String, ForeignKey("projects.id"), nullable=False)
    code_gen_id = Column(String, ForeignKey("code_generations.id"), nullable=False)
    estimated_lines_generated = Column(Integer, nullable=False)

    subscription = relationship("Subscription", back_populates="reserved_estimates", foreign_keys=subscription_id)
    project = relationship("Project", back_populates="reserved_estimates", foreign_keys=project_id)
    code_gen = relationship("CodeGeneration", back_populates="reserved_estimates", foreign_keys=code_gen_id)


# different types of overages that can be incurred by a subscription
class OverageType(enum.Enum):
    LINES_GENERATED = "LINES_GENERATED"
    LINES_ONBOARDED = "LINES_ONBOARDED"


# payment status for overages incurred by a subscription
class OveragePaymentStatus(enum.Enum):
    PENDING = "PENDING"
    PAID = "PAID"


# keep an audit trail of overages incurred by a subscription
class SubscriptionOverage(VisibilityMixin, BaseUuidMixin, Base):
    __tablename__ = "subscription_overages"

    subscription_id = Column(String, ForeignKey("subscriptions.id"), nullable=False)
    overage_type = Column(Enum(OverageType), nullable=False) # e.g., LINES_GENERATED, LINES_ONBOARDED
    overage_amount = Column(Integer, nullable=False) # e.g., number of lines generated
    payment_status = Column(Enum(OveragePaymentStatus), nullable=False, default=OveragePaymentStatus.PENDING)

    subscription = relationship("Subscription", back_populates="subscription_overages", foreign_keys=subscription_id)
