# base_model.py
import json
import uuid
from datetime import datetime, timezone

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String, func
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class VisibilityMixin:
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime(timezone=True))

    def soft_delete(self):
        """
        Soft delete the record.
        """
        self.is_deleted = True
        self.deleted_at = datetime.now(timezone.utc)


class BaseModel(object):
    """A base class which will automatically generate timestamps."""
    __prepare_statements__ = None

    created_at = Column(
        DateTime(timezone=True),
        default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        onupdate=func.now(),
        default=func.now()
    )

    def to_dict(self):
        """Convert the model instance to a dictionary."""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.timestamp()
            result[column.name] = value
        return result

    def to_json(self):
        """Convert the model instance to JSON."""
        return json.dumps(self.to_dict())


class BaseUuidMixin(BaseModel):
    """
    Base class for models using UUID as primary key.
    """
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))


def format_timestamp(timestamp: datetime) -> str:
    return timestamp.astimezone(timezone.utc).isoformat() if timestamp else ''
