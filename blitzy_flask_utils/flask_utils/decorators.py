import inspect
from datetime import datetime
from enum import Enum
from functools import wraps
from typing import Any, Callable, Type

from flask import Response, jsonify, request
from pydantic import BaseModel, ValidationError

from flask_utils.base_error import BaseError


def validate_request(model: Type[BaseModel]) -> Callable:
    """
    Decorator to validate Flask request data against a Pydantic model.
    Works with both JSON and form data, including list inputs.
    Supports both sync and async functions.
    Make sure you use `payload` as argument name.
    """

    def decorator(f: Callable) -> Callable:
        def validate_and_call(*args, **kwargs):
            try:
                # Try to get JSON data first
                if request.is_json:
                    data = request.get_json()
                # Fall back to form data if no JSON
                else:
                    data = request.form.to_dict()

                # Handle list input for models that expect a list
                if isinstance(data, list):
                    # For models that expect a root_type list, use the proper constructor
                    if hasattr(model, "root_type") and model.root_type == list:
                        validated_data = model.parse_obj({"input": data})
                    else:
                        # Direct parsing for models that directly expect a list
                        validated_data = model.parse_obj({"input": data})
                else:
                    # Original behavior for dict inputs
                    validated_data = model(**data)

                return validated_data

            except ValidationError as e:
                raise BaseError(e.errors(), 400)

        # Check if the function is async and return appropriate wrapper
        if inspect.iscoroutinefunction(f):
            @wraps(f)
            async def async_decorated_function(*args, **kwargs):
                validated_data = validate_and_call(*args, **kwargs)
                return await f(*args, payload=validated_data, **kwargs)
            return async_decorated_function
        else:
            @wraps(f)
            def sync_decorated_function(*args, **kwargs):
                validated_data = validate_and_call(*args, **kwargs)
                return f(*args, payload=validated_data, **kwargs)
            return sync_decorated_function

    return decorator


def flask_pydantic_response(func):
    """
    Decorator to automatically convert Pydantic models to JSON responses.
    Handles single models, lists of models, and custom status codes.
    Supports both sync and async functions.

    Usage:
        @pydantic_response
        def get_user() -> UserModel:
            return user_model

        @pydantic_response
        async def create_user() -> Tuple[UserModel, int]:
            return user_model, 201
    """

    # Check if the function is async and return appropriate wrapper
    if inspect.iscoroutinefunction(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            return _process_result(result)
        return async_wrapper
    else:
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            return _process_result(result)
        return sync_wrapper


def _process_result(result):
    """Process the result from a route function."""
    # If it's already a Response, return as-is
    if isinstance(result, Response):
        return result

    # Handle tuple returns (data, status_code)
    if isinstance(result, tuple):
        data = result[0]
        status_code = result[1]
    else:
        data = result
        status_code = 200

    # Convert the data
    if isinstance(data, BaseModel):
        data = _serialize_pydantic(data)
    elif isinstance(data, list) and data and isinstance(data[0], BaseModel):
        data = [_serialize_pydantic(item) for item in data]

    return jsonify(data), status_code


def _serialize_pydantic(obj: Any) -> Any:
    """Serialize Pydantic model with special type handling."""

    def _serialize_value(value: Any) -> Any:
        if isinstance(value, Enum):
            return value.name  # or value.value based on your needs
        elif isinstance(value, datetime):
            return value.isoformat()
        elif isinstance(value, BaseModel):
            return _serialize_pydantic(value)
        elif isinstance(value, dict):
            return {k: _serialize_value(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [_serialize_value(item) for item in value]
        return value

    data = obj.model_dump(warnings=False)
    return {k: _serialize_value(v) for k, v in data.items()}
