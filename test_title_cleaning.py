#!/usr/bin/env python3
"""
Simple test script to verify title cleaning functionality.
"""

import sys
import os
sys.path.append('.')

# Mock the required modules to avoid import errors
import unittest.mock as mock

# Mock the external dependencies
with mock.patch.dict('sys.modules', {
    'blitzy_platform_shared.document.utils': mock.MagicMock(),
    'blitzy_utils.logger': mock.MagicMock(),
    'common_models': mock.MagicMock(),
}):
    from src.api.utils.tech_spec_parser import TechSpecParser
    from src.api.models import TechSpecSubsection

def test_title_cleaning():
    """Test that title cleaning removes markdown formatting correctly."""
    parser = TechSpecParser()
    
    test_cases = [
        ("## 1.1 EXECUTIVE SUMMARY", "1.1 EXECUTIVE SUMMARY"),
        ("### 2.1 Architecture Overview", "2.1 Architecture Overview"),
        ("#### 3.1.1 Component Details", "3.1.1 Component Details"),
        ("# Main Title", "Main Title"),
        ("No markdown formatting", "No markdown formatting"),
        ("   ## Spaced Title   ", "Spaced Title"),
        ("##### 5.1 Deep Nested", "5.1 Deep Nested"),
    ]
    
    print("Testing title cleaning functionality:")
    print("=" * 50)
    
    all_passed = True
    for input_title, expected_output in test_cases:
        actual_output = parser._clean_title(input_title)
        passed = actual_output == expected_output
        all_passed = all_passed and passed
        
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{status} | '{input_title}' -> '{actual_output}'")
        if not passed:
            print(f"      Expected: '{expected_output}'")
    
    print("=" * 50)
    if all_passed:
        print("🎉 All title cleaning tests PASSED!")
    else:
        print("❌ Some title cleaning tests FAILED!")
    
    return all_passed

def test_subsection_parsing():
    """Test that subsection parsing uses clean titles."""
    parser = TechSpecParser()
    
    # Mock content with subsections
    content = """## 1. Main Section

This is main content.

### 1.1 Subsection One

Content for subsection one.

### 1.2 Subsection Two

Content for subsection two.
"""
    
    print("\nTesting subsection parsing with clean titles:")
    print("=" * 50)
    
    # Mock the converter to access the method
    converter = parser.markdown_converter
    subsections = converter._parse_subsections(content)
    
    expected_titles = ["1.1 Subsection One", "1.2 Subsection Two"]
    
    all_passed = True
    for i, subsection in enumerate(subsections):
        expected_title = expected_titles[i] if i < len(expected_titles) else f"Unknown_{i}"
        passed = subsection.title == expected_title
        all_passed = all_passed and passed
        
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{status} | Subsection {i+1}: '{subsection.title}'")
        if not passed:
            print(f"      Expected: '{expected_title}'")
    
    print("=" * 50)
    if all_passed:
        print("🎉 All subsection parsing tests PASSED!")
    else:
        print("❌ Some subsection parsing tests FAILED!")
    
    return all_passed

if __name__ == "__main__":
    print("Running title cleaning tests...\n")
    
    test1_passed = test_title_cleaning()
    test2_passed = test_subsection_parsing()
    
    print(f"\nOverall Results:")
    print(f"Title Cleaning: {'PASS' if test1_passed else 'FAIL'}")
    print(f"Subsection Parsing: {'PASS' if test2_passed else 'FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL TESTS PASSED! Title cleaning is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! Please check the implementation.")
        sys.exit(1)
