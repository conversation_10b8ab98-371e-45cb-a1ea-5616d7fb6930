import os
import re
import random
import string
import json
import signal
import sys
import logging
import asyncio
import requests
import subprocess
from enum import Enum
from typing import Dict, Any, List, Set, Optional, Union, Tuple
from ssl import SSLError
from functools import wraps
from dataclasses import dataclass

import google.auth.transport.requests
import google.oauth2.id_token
from google.api_core import exceptions
from requests import exceptions as request_exceptions
from tenacity import (
    retry, retry_if_exception_type, stop_after_attempt, wait_fixed,
    wait_exponential, before_sleep_log,
)
from github.ContentFile import ContentFile
from github.Repository import Repository

from .consts import (
    DEFAULT_MAX_RETRIES,
    DEFAULT_SLEEP_TIME,
    DOCUMENTATION_FOLDER_PATH,
    BLITZY_FOLDER_PATH,
    TECH_SPECIFICATION_NAME,
    REPO_STRUCTURE_NAME,
    DEFAULT_NAME,
    PROMPT_FILE_NAME,
    DEFAULT_MIN_WAIT,
    DEFAULT_MAX_WAIT,
    DEFAULT_MULTIPLIER,
    CORRELATION_ID_VAR_NAME,
    REQUEST_ID_VAR_NAME,
    DEFAULT_MAX_RETRIES_SERVICES,
    DEFAULT_MIN_WAIT_SERVICES,
    DEFAULT_MAX_WAIT_SERVICES,
)
from .errors import FailedToFetchCredentials
from .logger import logger
from .service_client import ServiceClient

# Global variables to store notification parameters
_publisher = None
_notification_data = None
_project_id = None
_topic_id = None


class SubscriptionType(Enum):
    """Enum for subscription types."""
    FREE = "FREE"
    PRO = "PRO"
    ENTERPRISE = "ENTERPRISE"
    TEAMS = "TEAMS"


class BlitzyGitFile:
    def __init__(
            self,
            path: str,
            text: str
    ):
        self.path = path
        self.text = text


class DictFileHelper:
    files_dict: Dict[str, Dict[str, Any]] = {}
    pending_files_list: List[str] = []
    seen_pending_files: Set[str] = set()
    pending_info: Dict[str, Any] = {}

    def __init__(self, files: Dict[str, Dict[str, Any]]):
        self.set_dict(files)

    def set_dict(self, files):
        self.files_dict = files

    def get_dict(self):
        return self.files_dict

    def add_pending_file(self, file_path: str):
        if file_path not in self.seen_pending_files:
            self.pending_files_list.append(file_path)
            self.seen_pending_files.add(file_path)

    def add_pending_info(self, file_path: str, info: Any):
        self.pending_info[file_path] = info

    def get_pending_files(self):
        return self.pending_files_list

    def clear_pending_files(self):
        self.pending_files_list = []


class FileContentHelper:
    file: str = ""

    def __init__(self, file: str):
        self.set_file(file)

    def get_file(self):
        return self.file

    def set_file(self, file):
        self.file = file


def write_to_file(file_path: str, content: str) -> str:
    """Write the final content to file."""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(content)
        return f"{file_path} written"
    except Exception as e:
        return f"Error writing to file: {str(e)}"


def read_markdown_files(file_list: list) -> str:
    """Read multiple markdown files and concatenate their contents."""
    if not os.path.exists("out"):
        logger.warning('not found')
        return ""

    final_content = ""
    for file_name in file_list:
        file_path = f"out/{file_name}.md"
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    final_content += f.read() + "\n"
            except Exception as e:
                final_content += ""  # Append empty string if there's an error reading a file
        else:
            final_content += ""  # Append empty string if file doesn't exist

    return final_content


def generate_repo_name():
    base_name = ''.join(random.choices(string.ascii_lowercase, k=8))
    hash_part = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    return f"{base_name}-{hash_part}"


def get_committer_info() -> Tuple[str, str]:
    """
    Get committer name and email from environment variables with fallbacks to default values.

    Returns:
        tuple: A tuple containing (committer_name, committer_email)
    """
    name = os.environ.get('BLITZY_COMMITER_NAME', '').strip()
    email = os.environ.get('BLITZY_COMMITER_EMAIL', '').strip()

    if not name:
        logger.warning("BLITZY_COMMITER_NAME is empty, using default 'Blitzy Agent'")
        name = 'Blitzy Agent'

    if not email:
        logger.warning("BLITZY_COMMITER_EMAIL is empty, using default '<EMAIL>'")
        email = '<EMAIL>'

    # Basic email validation
    if '@' not in email or '.' not in email.split('@')[-1]:
        logger.error(f"Invalid email format: {email}, using default")
        email = '<EMAIL>'
    logger.debug(f"Using commit author: {name} <{email}>")
    return name, email


def get_json_content(content: str, strict=True):
    # Find the content between the first and last set of triple backticks
    start = content.find('```')
    end = content.rfind('```')

    if start != -1 and end != -1 and start != end:
        # Extract the content between the backticks
        extracted_content = content[start + 3:end].strip()

        # Check if there's a language prefix on the first line
        lines = extracted_content.split('\n')
        if lines and len(lines) > 1:
            first_line = lines[0].strip()
            if re.match(r'^[a-zA-Z0-9]+$', first_line):
                extracted_content = '\n'.join(lines[1:])

        return extracted_content.strip()
    else:
        logger.warning("No code block found")
        if strict:
            return ""
        return content


def publish_notification(publisher, notification_data, project_id, topic_id):
    topic_path = publisher.topic_path(project_id, topic_id)
    data_str = json.dumps(notification_data)
    data = data_str.encode('utf-8')

    tracking_attributes = {}
    if CORRELATION_ID_VAR_NAME in os.environ:
        tracking_attributes[CORRELATION_ID_VAR_NAME] = os.environ[CORRELATION_ID_VAR_NAME]
    if REQUEST_ID_VAR_NAME in os.environ:
        tracking_attributes[REQUEST_ID_VAR_NAME] = os.environ[REQUEST_ID_VAR_NAME]

    logger.info(f'Publishing message to topic {topic_id}: {data_str} with tracking attributes {tracking_attributes}')
    future = publisher.publish(topic_path, data, **tracking_attributes)
    logger.info(f'Result of publish to topic {topic_id}: {future.result()}')


def download_from_gcs(storage_client, bucket_name: str, filename: str, blob_name: str):
    bucket = storage_client.bucket(bucket_name)
    destination_blob_name = f"{blob_name}/{filename}"
    blob = bucket.blob(destination_blob_name)
    logger.info(f'downloading file: {destination_blob_name}')
    return blob.download_as_text()


def download_documentation_from_gcs(storage_client, bucket_name: str, folder_name: str, blob_name: str,
                                    document_name: str):
    """
    Downloads a documentation file from Google Cloud Storage (GCS).
    The method attempts to locate and download the file from the primary path
    (blitzy/documentation). If not found, it falls back to a secondary path
    (documentation) before raising an exception.

    :param storage_client: Google Cloud Storage client instance for bucket operations
    :param bucket_name: Name of the GCS bucket containing the documentation
    :param folder_name: Name of the folder within the bucket structure
    :param blob_name: Base path/prefix for the blob location
    :param document_name: Name of the documentation file to be downloaded
    """
    bucket = storage_client.bucket(bucket_name)
    destination_blob_name = f"{blob_name}/{folder_name}/blitzy/documentation/{document_name}"
    blob = bucket.blob(destination_blob_name)
    if not blob.exists():
        destination_blob_name = f"{blob_name}/{folder_name}/documentation/{document_name}"
        blob = bucket.blob(destination_blob_name)
    logger.info(f'downloading file: {destination_blob_name}')
    return blob.download_as_text()


@retry(
    retry=retry_if_exception_type(
        (
            exceptions.ServerError,
            exceptions.TooManyRequests,
            request_exceptions.Timeout,
            request_exceptions.ConnectionError,
            SSLError,
            TimeoutError
        )
    ),
    stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
    wait=wait_fixed(DEFAULT_SLEEP_TIME)
)
def upload_to_gcs(storage_client, bucket_name: str, blob_name: str, filename: str, data: str,
                  content_type='text/plain'):
    bucket = storage_client.bucket(bucket_name)
    destination_blob_name = f"{blob_name}/{filename}"
    blob = bucket.blob(destination_blob_name)
    blob.upload_from_string(data, content_type=content_type)
    logger.info(f"Uploaded to: gs://{bucket_name}/{destination_blob_name}")
    return f"gs://{bucket_name}/{destination_blob_name}"


@retry(
    retry=retry_if_exception_type(
        (
            exceptions.ServerError,
            exceptions.TooManyRequests,
            request_exceptions.Timeout,
            request_exceptions.ConnectionError,
            SSLError,
            TimeoutError
        )
    ),
    stop=stop_after_attempt(DEFAULT_MAX_RETRIES),
    wait=wait_fixed(DEFAULT_SLEEP_TIME)
)
def gcs_bucket_walk(storage_client, bucket_name, prefix=""):
    """
    Walk through a GCS bucket, yielding tuples of (path, folders, files) similar to os.walk().

    :param storage_client: Initialized Google Cloud Storage client
    :param bucket_name: Name of the GCS bucket
    :param prefix: Optional prefix to start walking from a specific directory
    :yield: Tuples of (path, folders, files)
    """
    bucket = storage_client.get_bucket(bucket_name)

    # Use set to keep track of unique folders
    folders = set()

    # List all blobs in the bucket with the given prefix
    blobs = bucket.list_blobs(prefix=prefix)

    for blob in blobs:
        # Get the relative path of the blob
        path = blob.name

        # If it's a prefix (folder), add it to the folders set
        if blob.name.endswith('/'):
            folders.add(blob.name)
        else:
            # Extract the directory path
            dir_path = '/'.join(blob.name.split('/')[:-1]) + '/'
            if dir_path:
                folders.add(dir_path)

    # Sort folders to maintain a consistent order
    sorted_folders = sorted(folders)

    # Yield results for each folder
    for folder in sorted_folders:
        path = folder
        subfolders = [f for f in sorted_folders if f.startswith(folder) and f != folder]
        files = [b.name for b in bucket.list_blobs(prefix=folder, delimiter='/') if not b.name.endswith('/')]
        yield (path, subfolders, files)


def clean_heading_text(text):
    # Remove numbers, spaces, and dots before the first alphabet
    return re.sub(r'^[\d\s.]+(?=[a-zA-Z])', '', text).lower().replace(' ', '_')


def parse_markdown_to_dict(markdown_text):
    result = {}
    heading_stack = []
    current_content = []
    base_level = None

    lines = markdown_text.strip().split('\n')

    for line in lines:
        heading_match = re.match(r'^(#{1,6})\s+(.+)$', line)

        if heading_match:
            if current_content:
                if heading_stack:
                    key = '.'.join(heading_stack)
                    result[key] = '\n'.join(current_content).strip()
                current_content = []

            level = len(heading_match.group(1))
            heading_text = clean_heading_text(heading_match.group(2))

            if base_level is None:
                base_level = level

            relative_level = level - base_level

            while heading_stack and relative_level <= len(heading_stack) - 1:
                heading_stack.pop()

            if not heading_stack or relative_level > len(heading_stack) - 1:
                heading_stack.append(heading_text)

        else:
            if line.strip():
                current_content.append(line)

    if current_content and heading_stack:
        key = '.'.join(heading_stack)
        result[key] = '\n'.join(current_content).strip()

    return result


def get_google_authorized_request_headers(url):
    auth_req = google.auth.transport.requests.Request()
    id_token = google.oauth2.id_token.fetch_id_token(auth_req, url)
    headers = {'Authorization': f'Bearer {id_token}'}
    return headers


def get_repo_parent_folder_path(
        company_id: str,
        team_id: str,
        user_id: str,
        repo_name: str,
        repo_id: str,
        branch_id: str,
        blob_name: str = "private-src",
) -> str:
    return f"{blob_name}/company_{company_id}/team_{team_id}/user_{user_id}/{repo_name}_{repo_id}/branch_{branch_id}"


def get_repo_documentation_folder_path(
        company_id: str,
        team_id: str,
        user_id: str,
        repo_name: str,
        repo_id: str,
        branch_id: str,
        blob_name: str = "private-src",
) -> str:
    parent_folder = get_repo_parent_folder_path(
        company_id=company_id,
        team_id=team_id,
        user_id=user_id,
        repo_name=repo_name,
        repo_id=repo_id,
        branch_id=branch_id,
        blob_name=blob_name
    )
    return f"{parent_folder}/{DOCUMENTATION_FOLDER_PATH}"


def get_repo_blitzy_folder_path(
        company_id: str,
        team_id: str,
        user_id: str,
        repo_name: str,
        repo_id: str,
        branch_id: str,
        blob_name: str = "private-src",
) -> str:
    parent_folder = get_repo_parent_folder_path(
        company_id=company_id,
        team_id=team_id,
        user_id=user_id,
        repo_name=repo_name,
        repo_id=repo_id,
        branch_id=branch_id,
        blob_name=blob_name
    )
    return f"{parent_folder}/{BLITZY_FOLDER_PATH}"


def get_existing_product_tech_spec_name(
        head_commit_hash: str
) -> str:
    return f"{TECH_SPECIFICATION_NAME} - {head_commit_hash}.md"


def get_existing_product_updated_tech_spec_name(
        head_commit_hash: str
) -> str:
    return f"{TECH_SPECIFICATION_NAME} - Updated - {head_commit_hash}.md"


def get_tech_spec_name(
        tech_spec_id: str
) -> str:
    return f"{TECH_SPECIFICATION_NAME}_{tech_spec_id}.md"


def get_batched_files_list_filename(
        base_filename: str,
        batch_index: int
) -> str:
    return f"{base_filename}_batch_{batch_index}"


def get_existing_product_files_list_name(
        head_commit_hash: str
) -> str:
    return f"{REPO_STRUCTURE_NAME} - {head_commit_hash}"


def get_existing_product_updated_files_list_name(
        prev_head_commit_hash: str,
        head_commit_hash: str
) -> str:
    return f"{REPO_STRUCTURE_NAME} - {prev_head_commit_hash} - {head_commit_hash}"


def handle_signal(signum: int, _: Any) -> None:
    """Handle SIGTSTP and SIGCONT signals from Cloud Run maintenance events."""
    if signum == signal.SIGTSTP:
        logger.warning("Received SIGTSTP: Maintenance event imminent - stopping execution")

        # Publish notification if all parameters are provided
        if all([_publisher, _notification_data, _project_id, _topic_id]):
            try:
                _notification_data["resume"] = True  # type: ignore
                logger.info(f"Triggering resume event for topic: {_topic_id}, data: {_notification_data}")
                publish_notification(_publisher, _notification_data, _project_id, _topic_id)
            except Exception as e:
                logger.error(f"Failed to publish notification: {str(e)}")

        logger.warning(f"Exiting process due to signal: {signum}")
        sys.exit(0)


def setup_maintenance_signal_handlers(publisher=None, notification_data=None, project_id=None, topic_id=None) -> None:
    """Set up signal handlers for Cloud Run maintenance events."""
    # Create a partial function with the extra parameters
    global _publisher, _notification_data, _project_id, _topic_id

    _publisher = publisher
    _notification_data = notification_data
    _project_id = project_id
    _topic_id = topic_id

    signal.signal(signal.SIGTSTP, handle_signal)


def generate_core_documentation_path(tech_spec_metadata: Dict[str, Any]) -> str:
    """
    Generates the core documentation path based on the metadata provided for a specific
    repository. This function extracts necessary attributes from the `tech_spec_metadata`
    dictionary and constructs a path utilizing these values. Default values are employed
    for certain entries if they are absent from the metadata. The resulting path integrates
    company, team, user, repository, and branch identifiers.

    :param tech_spec_metadata: A dictionary containing metadata for constructing the
        documentation path. Expected keys include:
        - "company_id" (optional): Identifier for the company.
        - "team_id" (optional): Identifier for the team within the company.
        - "user_id": Identifier for the user. This key is mandatory.
        - "repo_name": Name of the repository.
        - "repo_id": Unique identifier for the repository.
        - "branch_id": Identifier for the branch in the repository.

    :return: The core documentation path as a string.
    """
    company_id = tech_spec_metadata.get("company_id", DEFAULT_NAME)
    team_id = tech_spec_metadata.get("team_id", DEFAULT_NAME)
    user_id = tech_spec_metadata["user_id"]
    repo_name = tech_spec_metadata["repo_name"]
    repo_id = tech_spec_metadata["repo_id"]
    branch_id = tech_spec_metadata["branch_id"]

    prefix_path = get_repo_documentation_folder_path(company_id, team_id, user_id, repo_name, repo_id, branch_id)

    return prefix_path


def generate_technical_spec_document_path(tech_spec_metadata: Dict[str, Any], gcs_bucket: str) -> str:
    """
    Generate the technical spec document path based on the provided metadata.

    :param tech_spec_metadata: Tech spec metadata used for generating notification.
    :param gcs_bucket: GSC bucket name used for storing technical spec document.
    :return: Technical spec document path.
    """
    tech_spec_id = tech_spec_metadata["tech_spec_id"]

    prefix_path = generate_core_documentation_path(tech_spec_metadata)
    tech_spec_path = get_tech_spec_name(tech_spec_id)
    document_path = f"{prefix_path}/{tech_spec_path}"
    return document_path


def generate_input_prompt_document_path(metadata: Dict[str, Any]):
    prefix_path = generate_core_documentation_path(metadata)
    document_path = f"{prefix_path}/{PROMPT_FILE_NAME}.md"
    return document_path


def blitzy_exponential_retry():
    """
    Automatically retries failed HTTP calls, this is a
    version for JOBS ONLY, don't use it in services.
    """
    return _blitzy_exponential_retry_decorator(
        DEFAULT_MAX_RETRIES,
        DEFAULT_MULTIPLIER,
        DEFAULT_MIN_WAIT,
        DEFAULT_MAX_WAIT
    )


def blitzy_exponential_retry_service():
    """
    Automatically retries failed HTTP calls, this is a version
    for services ONLY, don't use it in jobs. It has lower waiting times.
    """
    # def wrapped(func):
    return _blitzy_exponential_retry_decorator(
        DEFAULT_MAX_RETRIES_SERVICES,
        DEFAULT_MULTIPLIER,
        DEFAULT_MIN_WAIT_SERVICES,
        DEFAULT_MAX_WAIT_SERVICES
    )
    # return wrapped


def _blitzy_exponential_retry_decorator(
        max_retries,
        multiplier,
        min_wait,
        max_wait,
):
    """
    Shared logic for retry decorator, this is prrivate don't use it
    """
    logger.info(
        f"Setting up exponential retry with max retries: {max_retries}, multiplier: {multiplier}, "
    )
    def decorator(func):
    # Check if the function is async
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                # For async functions, we need tenacity's retry_event_loop
                @retry(
                    retry=retry_if_exception_type((
                        request_exceptions.ConnectionError,
                        request_exceptions.Timeout,
                        request_exceptions.HTTPError,
                        request_exceptions.ReadTimeout,
                        FailedToFetchCredentials,
                        exceptions.TooManyRequests,
                    )),
                    stop=stop_after_attempt(max_retries),
                    wait=wait_exponential(
                        multiplier=multiplier,
                        min=min_wait,
                        max=max_wait
                    ),
                    retry_error_callback=lambda retry_state: retry_state.outcome.result(),
                    before_sleep=before_sleep_log(logger.logger, logging.WARNING),
                    reraise=True
                )
                async def _async_retry():
                    return await func(*args, **kwargs)

                return await _async_retry()

            return async_wrapper
        else:

            @wraps(func)
            @retry(
                retry=retry_if_exception_type((
                    request_exceptions.ConnectionError,
                    request_exceptions.Timeout,
                    request_exceptions.HTTPError,
                    request_exceptions.ReadTimeout,
                    FailedToFetchCredentials,
                    exceptions.TooManyRequests,
                )),
                stop=stop_after_attempt(max_retries),
                wait=wait_exponential(multiplier=multiplier, min=min_wait, max=max_wait),
                before_sleep=before_sleep_log(logger.logger, logging.WARNING),  # Log retry attempts
                reraise=True  # Re-raise the last exception if all retries fail
            )
            def sync_wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            return sync_wrapper
    return decorator


def retrieve_file_content_from_gcs(storage_client, bucket_name: str, filename: str, blob_name: str, company_id=""):
    if os.environ.get("SERVICE_URL_ADMIN"):
        logger.info(f"Using admin service to fetch {filename} from GCS")
        return download_text_file_from_gcs_using_admin_service(filename, company_id)
    else:
        logger.info(f"Using blitzy utils library function to fetch {filename} from GCS")
        return download_from_gcs(storage_client, bucket_name, filename, blob_name)


def download_text_file_from_gcs_using_admin_service(file_path, company_id):
    logger.info(f"Downloading {file_path} for company {company_id}")
    params = {
        "file_path": file_path,
        "company_id": company_id,
    }
    with ServiceClient() as client:
        response = client.get(service_name="admin", endpoint=f"/v1/storage/download", params=params)
        response.raise_for_status()
        return response.text


def upload_file_content_to_gcs(storage_client, bucket_name: str, blob_name: str, filename: str, data: str,
                               content_type='text/plain', company_id=""):
    if os.environ.get("SERVICE_URL_ADMIN"):
        logger.info(f"Using admin service to fetch {filename} from GCS")
        return upload_text_to_gcs_using_admin_service(filename, data, company_id)
    else:
        logger.info(f"Using blitzy utils library function to upload {filename} from GCS")
        return upload_to_gcs(storage_client, bucket_name, blob_name, filename, data, content_type=content_type)


def upload_text_to_gcs_using_admin_service(file_path, data, company_id, content_type="text/plain"):
    logger.info(f"Uploading {file_path} for company {company_id}")
    if company_id == DEFAULT_NAME:
        logger.debug("Detected default company name set it to empty string")
        company_id = ""
    elif company_id is None:
        logger.debug("Detected company id is None, setting it to empty string")
        company_id = ""

    payload = {
        "filePath": file_path,
        "companyId": company_id,
        "data": data,
        "contentType": content_type,
    }
    with ServiceClient() as client:
        response = client.post(service_name="admin", endpoint=f"/v1/storage/upload-string", json=payload)
        response.raise_for_status()
        output_json = response.json()
        return output_json["filePath"]


def get_subscription_plan_name_by_user_id(user_id: str) -> SubscriptionType:
    with ServiceClient() as client:
        response = client.get(service_name="admin", endpoint=f"/v1/subscription/user/{user_id}/plan")
        response.raise_for_status()
        output_json = response.json()
        plan_name = output_json["planName"]

    if hasattr(SubscriptionType, plan_name):
        return getattr(SubscriptionType, plan_name)
    else:
        raise ValueError(f"Invalid plan name: {plan_name}")


def sanitize_url_for_logging(url: str) -> str:
    """
    Remove credentials from URL for safe logging.

    Args:
        url: URL that may contain credentials

    Returns:
        URL with credentials removed
    """
    # Remove any authentication information from URL for logging
    return re.sub(r'://[^@]*@', '://', url)


def get_env_for_git() -> Dict[str, str]:
    env = os.environ.copy()
    env['GIT_TERMINAL_PROMPT'] = '0'
    env['GIT_ASKPASS'] = 'echo'
    env['GCM_INTERACTIVE'] = 'never'
    env['GIT_CREDENTIAL_HELPER'] = ''
    return env


class RawData:
    def __init__(self, headers):
        self.headers = headers


class SimpleContentFile:
    def __init__(self, data: dict):
        self.path: str = data.get("path", "")
        self.content: str = data.get("content", "")
        self.encoding: str = data.get("encoding", "utf-8")
        self.size: int = data.get("size", 0)
        self.download_url: Optional[str] = data.get("download_url")
        self.sha: Optional[str] = data.get("sha")
        self.type: str = data.get("type", "file")
        self.headers: Optional[Dict[str, str]] = data.get("headers", None)
        self._decoded_content: Optional[bytes] = None
        self._rawData: Optional[RawData] = None

        # Support for _rawData with headers for rate limiting logic
        if self.headers:
            self._rawData = RawData(self.headers)


@blitzy_exponential_retry()
def decode_file_content(content_file: ContentFile | SimpleContentFile) -> Optional[str]:
    """
    Safely decode file content from GitHub API.
    Returns None for submodule references.
    """

    try:
        # Check if this is a large file (encoding is None)
        if content_file.content == "" or content_file.encoding == "none":
            # Large file - use download_url
            logger.info(
                f"Large file detected ({content_file.size} bytes), downloading from URL"
            )
            if not content_file.download_url:
                logger.error(f"No download URL available for file {content_file.path}")
                return None
            response = requests.get(content_file.download_url)
            response.raise_for_status()

            # Try to decode as text
            try:
                return response.text
            except UnicodeDecodeError:
                # Try other encodings
                encodings = ["latin-1", "cp1252", "utf-16", "ascii"]
                for encoding in encodings:
                    try:
                        return response.content.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                logger.error(
                    f"Could not decode file {content_file.path}, all encodings failed"
                )
                return None
        else:
            # Regular file - use decoded_content (PyGithub handles the encoding)
            decoded_bytes = content_file.decoded_content  # type: ignore

            # Try UTF-8 decoding first
            try:
                return decoded_bytes.decode("utf-8")
            except UnicodeDecodeError:
                # Try other common encodings
                encodings = ["latin-1", "cp1252", "utf-16", "ascii"]
                for encoding in encodings:
                    try:
                        return decoded_bytes.decode(encoding)
                    except UnicodeDecodeError:
                        continue

                # If all text decodings fail, return as binary string representation
                # or handle binary files differently
                logger.error(
                    f"Could not decode file {content_file.path}, all encodings failed"
                )
                return None  # Return raw bytes for binary files

    except Exception as e:
        logger.error(f'Error decoding {content_file.path}: {e}')
        raise


@dataclass
class AzureRepo:
    """Azure DevOps repository info class"""
    name: str  # repo_name to match pyGitHub Repository object
    org_id: str
    org_name: str
    project_id: str
    repo_id: str
    id: str  # this is the same as repo_id, just for backward compatibility
    default_branch: str
    html_url: str


GitRepo = Union[AzureRepo, Repository]


@blitzy_exponential_retry()
def get_submodule_sha(repo_path: str, submodule_path: str, commit_hash: str, env: dict) -> Optional[str]:
    """
    Get the commit SHA that a submodule is pointing to at a specific commit using local git commands.

    Args:
        repo_path: Path to the main repository
        submodule_path: Path to the submodule within the repository
        commit_hash: Commit hash in the main repository
        env: Environment variables for git commands

    Returns:
        The submodule commit SHA or None if not found
    """
    try:
        # Use git ls-tree to get the submodule commit hash
        ls_tree_cmd = ["git", "-C", repo_path, "ls-tree", commit_hash, submodule_path]
        ls_tree_result = subprocess.run(ls_tree_cmd, capture_output=True, text=True, env=env, check=False)

        if ls_tree_result.returncode != 0:
            logger.debug(f"Could not get submodule info: {ls_tree_result.stderr}")
            return None

        # Parse the output - format is: <mode> <type> <sha> <path>
        # For submodules, type is "commit"
        output = ls_tree_result.stdout.strip()
        if output:
            parts = output.split()
            if len(parts) >= 3 and parts[1] == "commit":
                sha = parts[2]
                logger.debug(f"Found submodule {submodule_path} at commit {commit_hash} with SHA {sha}")
                return sha

        logger.debug(f"No submodule found at path {submodule_path} for commit {commit_hash}")
        return None

    except Exception as e:
        logger.error(f"Error getting submodule SHA: {e}")
        return None
