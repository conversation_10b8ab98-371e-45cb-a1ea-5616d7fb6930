import os
import requests
import subprocess
from typing import <PERSON><PERSON>, <PERSON>, Tu<PERSON>, Dict, Union

from github.Repository import Repository
from github.PullRequest import Pull<PERSON>equest
from github.GitCommit import Git<PERSON>ommit as GitHubGitCommit
from github import Gith<PERSON>Ex<PERSON>, Github

from .logger import logger
from .consts import SvcType
from .disk import get_cwd
from .common import (
    blitzy_exponential_retry,
    get_google_authorized_request_headers,
    BlitzyGitFile,
    AzureRepo,
    decode_file_content,
    SimpleContentFile,
    GitRepo,
)
from .azure import (
    create_azure_devops_repo,
    _get_azure_devops_credentials_by_repo_id,
    _get_azure_devops_repo,
    download_single_file_azure,
    download_all_git_files_to_disk_azure_devops,
    create_single_pull_request_azure_devops,
    handle_azure_branch_logic,
    create_all_pull_requests_azure_devops,
    create_azure_devops_commit_by_repo_id,
    AzureDevOpsCommit,
    get_changed_files_between_commits_azure_devops,
    get_azure_devops_head_commit_hash_by_repo_id,
    push_pull_latest_from_repository_azure_devops,
    find_closest_ancestor_branch_azure_devops,
)
from .github import (
    clone_repository_with_auth,
    get_github_repo_github,
    get_all_files_from_cloned_repo,
    download_single_file_github,
    get_token_and_installation_id,
    get_repo_info_by_id,
    handle_github_branch_logic,
    create_pull_with_retry_github,
    get_pulls_with_retry_github,
    extract_repo_info_from_url,
    parse_gitmodules,
    ssh_to_https_url,
    get_gitmodules_content,
    get_repo_from_github_with_retry,
    get_branch_github,
    create_commit_github,
    get_changed_files_between_commits_github,
    get_head_commit_hash_github,
    push_pull_latest_from_repository_github,
    find_closest_ancestor_branch_github,
)


@blitzy_exponential_retry()
def _get_service_type(user_id: str, git_project_repo_id: str) -> SvcType:
    """Get the service type (GitHub or Azure DevOps) for a repository."""
    try:
        github_handler_server = os.environ.get("SERVICE_URL_GITHUB")

        url = f"{github_handler_server}/v1/users/{user_id}/repositories/{git_project_repo_id}/svc-type"
        logger.info(f"Fetching service type from {url}")
        headers = get_google_authorized_request_headers(url)
        response = requests.get(url, headers=headers, timeout=60)

        if response.status_code == 200:
            data = response.json()
            svc_type_str = data.get("svcType")
            if not svc_type_str:
                raise Exception(
                    f"Service type not found in response for git_project_repo_id {git_project_repo_id}"
                )

            logger.info(f"Service type: {svc_type_str}")
            return SvcType(svc_type_str)
        else:
            err_msg = (
                f"Failed to fetch service type for git_project_repo_id {git_project_repo_id}. "
                f"Status code: {response.status_code}, response: {response.text}"
            )
            raise Exception(err_msg)
    except Exception as e:
        logger.error(f"Error fetching service type: {e}")
        raise


def find_closest_ancestor_branch(
    repo_name: str,
    branch_name: str,
    completed_branches: List[Dict],
    new_head_commit_hash: str,
    user_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> Optional[Dict[str, str]]:
    """
    Find the completed branch whose head commit is the closest ancestor to the new commit.
    Uses merge-base to determine the most recent common ancestor.

    Args:
        repo_name: Repository name
        branch_name: Branch name (used to determine clone location)
        completed_branches: List of completed branches with branch_id and head_commit_hash
        new_head_commit_hash: The new commit to find closest ancestor for
        user_id: Optional user ID for service detection
        git_project_repo_id: Optional project repository ID for service detection

    Returns:
        Dictionary with branch_id and head_commit_hash of closest ancestor branch, or None
    """
    # Determine service type
    if git_project_repo_id and user_id:
        svc_type = _get_service_type(user_id, git_project_repo_id)
    else:
        svc_type = SvcType.GITHUB

    logger.info(
        f"Finding closest ancestor among {len(completed_branches)} branches "
        f"for commit {new_head_commit_hash} using {svc_type.value}"
    )

    if svc_type == SvcType.AZURE_DEVOPS:
        return find_closest_ancestor_branch_azure_devops(
            git_project_repo_id=git_project_repo_id,
            completed_branches=completed_branches,
            new_head_commit_hash=new_head_commit_hash,
        )
    else:
        return find_closest_ancestor_branch_github(
            repo_name=repo_name,
            branch_name=branch_name,
            completed_branches=completed_branches,
            new_head_commit_hash=new_head_commit_hash,
        )


@blitzy_exponential_retry()
def download_repository_to_disk(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> List[BlitzyGitFile]:
    """
    Download repository using git clone instead of GitHub API.

    Args:
        repo_name: Repository name
        branch_name: Branch to clone
        user_id: User ID for authentication
        server: Server URL for authentication
        commit_hash: Specific commit to checkout
        repo_id: Repository ID

    Returns:
        List of BlitzyGitFile objects
    """

    # Get the service type for this repository
    if git_project_repo_id:
        logger.info(
            f"Getting head commit hash for {repo_name} with git_project_repo_id {git_project_repo_id}"
        )
        svc_type = _get_service_type(
            user_id=user_id, git_project_repo_id=git_project_repo_id
        )
    else:
        logger.info(
            "No git_project_repo_id provided, using default github service type"
        )
        svc_type = SvcType.GITHUB
    logger.info(
        f"Processing repository {repo_name} with service type: {svc_type.value}"
    )

    if svc_type == SvcType.AZURE_DEVOPS:
        return download_all_git_files_to_disk_azure_devops(
            repo_name=repo_name,
            branch_name=branch_name,
            commit_hash=commit_hash,
            git_project_repo_id=git_project_repo_id,
        )

    logger.info(f"Downloading {repo_name} from using git clone")

    # Get authentication token
    access_token, _ = get_token_and_installation_id(
        server=server, user_id=user_id, repo_id=repo_id
    )

    if not access_token:
        raise Exception("Failed to get GitHub access token")

    # Extract owner and repo info
    if "/" in repo_name:
        full_repo_name = repo_name
    else:
        # Need to get full repo name with owner
        repo_info = get_repo_info_by_id(repo_id)
        org_name = repo_info["orgName"]
        full_repo_name = f"{org_name}/{repo_name}"

    # Determine if this is GitHub Enterprise
    domain = None
    if "/" in full_repo_name and full_repo_name.count("/") > 1:
        # Extract domain from URL-like repo name
        parts = full_repo_name.split("/")
        if len(parts) > 2:
            domain = parts[0]
            full_repo_name = "/".join(parts[1:])

    # Get disk path for cloning
    disk_path = get_cwd(repo_name=repo_name, branch_name=branch_name)

    # Clone the repository
    success = clone_repository_with_auth(
        full_repo_name=full_repo_name,
        access_token=access_token,
        clone_path=disk_path,
        branch_name=branch_name,
        commit_hash=commit_hash,
        domain=domain,
    )

    if not success:
        raise Exception(f"Failed to clone repository {full_repo_name}")

    # Get all files from cloned repository
    git_files = get_all_files_from_cloned_repo(clone_path=disk_path)

    logger.info(f"Successfully downloaded {len(git_files)} files")
    return git_files


@blitzy_exponential_retry()
def get_github_repo(
    repo_name: str,
    user_id: str,
    server: str,
    create=True,
    repo_id=None,
    git_project_repo_id=None,
) -> Tuple[GitRepo, bool]:
    """
    Function fetches and returns repo information for github or azure repo. In case of github
    the repo type is github.Repository. In case of azure repo type is .common.AzureRepo

    If the variable git_project_repo_id is set, then we will call archie github handler to determine repo type, otherwise
    we fall back to default behavior and assume repo type is github.

    IMPORTANT!!!!! If the variable `git_project_repo_id` is set, we will ignore all
    other variables for azure and fetch them archie-github-handler. We will keep using these variables for github for now.
    """
    if git_project_repo_id:
        logger.info(
            f"Variable git_project_repo is set, will determine repo type using github-handler service"
        )
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.GITHUB:
            logger.info(
                f"Repo project {git_project_repo_id} is github repo type, will use github API to get repo info"
            )
            return get_github_repo_github(repo_name, user_id, server, create, repo_id)
        elif svc_type == SvcType.AZURE_DEVOPS:
            logger.info(
                f"Repo project {git_project_repo_id} is azure repo type, will use azure API to get repo info"
            )
            git_project_repo = _get_azure_devops_credentials_by_repo_id(
                git_project_repo_id
            )

            logger.info(
                f"Fetched github_project_repo details org_id: {git_project_repo.azure_org_id}, "
                f"azure_project_id: {git_project_repo.azure_project_id}, "
                f"repo_id: {git_project_repo.repo_id}"
            )
            remote_azure_repo = _get_azure_devops_repo(
                git_project_repo.azure_org_name,
                git_project_repo.azure_project_id,
                git_project_repo.repo_id,
                git_project_repo.access_token,
            )
            if not remote_azure_repo:
                logger.info(
                    f"Didn't find repo {repo_name} in azure devops org {git_project_repo.azure_org_name} project "
                    f"{git_project_repo.azure_project_id}, "
                    f"will create repo"
                )
                new_repo_data = create_azure_devops_repo(
                    git_project_repo.azure_org_name,
                    git_project_repo.azure_project_id,
                    repo_name,
                    git_project_repo.access_token,
                )
                repo_id = new_repo_data["id"]
                repo_url = new_repo_data["url"]
                default_branch = new_repo_data["default_branch"]
                new_repo = True
            else:
                logger.debug("Azure repo exists, will not create new repo")
                repo_id = remote_azure_repo["id"]
                repo_url = remote_azure_repo["url"]
                default_branch = remote_azure_repo["default_branch"]
                new_repo = False

            azure_repo = AzureRepo(
                name=repo_name,
                org_id=git_project_repo.azure_org_id,
                org_name=git_project_repo.azure_org_name,
                project_id=git_project_repo.azure_project_id,
                html_url=repo_url,
                repo_id=repo_id,
                id=repo_id,
                default_branch=default_branch,
            )
            return azure_repo, new_repo
        else:
            raise Exception(f"Unknown service type {svc_type}")

    else:
        logger.info(f"Variable git_project_repo_id is not set, assume github repo type")
        return get_github_repo_github(repo_name, user_id, server, create, repo_id)


def download_single_file(
    repo_name: str,
    user_id: str,
    server: str,
    file_path: str,
    commit_hash: str,
    repo_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> Optional[str]:
    """
    Download a single file from the repository at stored commit hash, with submodule support.

    Usage:
        This function is designed to work for both GitHub and Azure DevOps repositories.
        - If `git_project_repo_id` is provided, the function will use the new handler endpoint, which
          supports both GitHub and Azure DevOps.
        - If `git_project_repo_id` is not provided, the function will default to using the legacy GitHub logic.


    Args:
        repo_name: Name of the repository.
        user_id: User ID for authentication.
        server: Server URL for authentication.
        file_path: Path to the file to download.
        commit_hash: Commit hash to fetch the file from.
        repo_id: (Optional) Repository ID, used for legacy GitHub logic.
        git_project_repo_id: (Optional) Project repository ID, required for Azure DevOps and new GitHub handler.

    Returns:
        The file content as a string, or None if not found or on error.
    """
    if not git_project_repo_id:
        svc_type = SvcType.GITHUB
    else:
        svc_type = _get_service_type(user_id, git_project_repo_id)

    repo, _ = get_github_repo(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        create=False,
        repo_id=repo_id,
        git_project_repo_id=git_project_repo_id,
    )

    if svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        logger.info(
            f"Fetching Azure DevOps repo for download_single_file: "
            f"repo_name={repo_name}, user_id={user_id}, server={server}, git_project_repo_id={git_project_repo_id}"
        )
        git_project_repo = _get_azure_devops_credentials_by_repo_id(git_project_repo_id)
        content = download_single_file_azure(
            access_token=git_project_repo.access_token,
            organization=git_project_repo.azure_org_name,
            project_id=git_project_repo.azure_project_id,
            repo_id=git_project_repo.repo_id,
            path=file_path,
            commit_hash=commit_hash,
        )
        content = SimpleContentFile(content) if content else None
        content = decode_file_content(content_file=content) if content else None
    elif svc_type == SvcType.GITHUB:
        if not isinstance(repo, Repository):
            logger.error(f"Expected a GitHub repository, but got {type(repo)}")
            raise TypeError(f"Expected a GitHub repository, but got {type(repo)}")
        content = download_single_file_github(
            github_repo=repo,
            repo_name=repo_name,
            user_id=user_id,
            server=server,
            file_path=file_path,
            commit_hash=commit_hash,
            repo_id=repo_id,
        )
    else:
        logger.error(f"Unsupported service type: {svc_type}")
        raise ValueError(f"Unsupported service type: {svc_type}")
    if content is None:
        logger.error(
            f"Failed to download file {file_path} from repo {repo_name} at commit {commit_hash}"
        )
        return None
    logger.info(
        f"Successfully downloaded file {file_path} from repo {repo_name} at commit {commit_hash}"
    )
    return content


def setup_github_branch(
    repo_name: str,
    user_id: str,
    server: str,
    repo_id: str,
    branch_name: str,
    base_branch="",
    create_new_branch=True,
    delete_existing_branch=True,
    git_project_repo_id: Optional[str] = None,
) -> str:
    repo, _ = get_github_repo(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        create=create_new_branch,
        repo_id=repo_id,
        git_project_repo_id=git_project_repo_id,
    )

    if not base_branch:
        base_branch = repo.default_branch

    if git_project_repo_id and user_id:

        svc_type = _get_service_type(user_id, git_project_repo_id)

        if svc_type == SvcType.GITHUB:
            logger.info(
                f"Repo project {git_project_repo_id} is github repo type, will use github API to setup github branch"
            )

            # just to ensure error is not propogated further
            if not isinstance(repo, Repository):
                raise ValueError(
                    "Expected a PyGithub Repository object, but got AzureRepo"
                )

            try:
                result = handle_github_branch_logic(
                    repo=repo,
                    branch_name=branch_name,
                    base_branch=base_branch,
                    create_new_branch=create_new_branch,
                    delete_existing_branch=delete_existing_branch,
                )
                return result if result else ""
            except Exception as e:
                logger.error(f"Error creating GitHub branch {branch_name}: {str(e)}")
                raise

        elif svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
            logger.info(
                f"Repo project {git_project_repo_id} is github repo type, will use azure API to setup azure branch"
            )

            logger.info(
                f"Repo project {git_project_repo_id} is azure repo type, will use azure API to get repo info"
            )
            git_project_repo = _get_azure_devops_credentials_by_repo_id(
                git_project_repo_id
            )

            logger.info(
                f"Fetched github_project_repo details org_id: {git_project_repo.azure_org_id}, "
                f"azure_project_id: {git_project_repo.azure_project_id}, "
                f"repo_id: {git_project_repo.repo_id}"
            )

            try:
                result = handle_azure_branch_logic(
                    git_project_repo.access_token,
                    git_project_repo.azure_org_name,
                    repo_id=git_project_repo.repo_id,
                    project=git_project_repo.azure_project_id,
                    branch_name=branch_name,
                    base_branch=base_branch,
                    create_new_branch=create_new_branch,
                    delete_existing_branch=delete_existing_branch,
                )
                return result if result else ""

            except Exception as e:
                logger.error(f"Error creating Azure branch {branch_name}: {str(e)}")
                raise

        else:
            logger.error(
                f"Unknown service type {svc_type} for repo project {git_project_repo_id}"
            )
            raise ValueError(f"Unsupported service type: {svc_type}")

    else:
        try:
            if isinstance(repo, AzureRepo):
                raise ValueError("Passed Azure object to pyGithub fallback logic")

            result = handle_github_branch_logic(
                repo=repo,
                branch_name=branch_name,
                base_branch=base_branch,
                create_new_branch=create_new_branch,
                delete_existing_branch=delete_existing_branch,
            )
            return result if result else ""
        except Exception as e:
            logger.error(f"Error creating GitHub branch {branch_name}: {str(e)}")
            raise


@blitzy_exponential_retry()
def create_single_pull_request(
    repo: Union[Repository, str],
    head_branch: str,
    base_branch: str,
    pr_title: str,
    pr_body: str,
    user_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> Optional[Union[PullRequest, Dict]]:
    """
    Create a single pull request in the specified repository.
    Supports both GitHub and Azure DevOps repositories.

    Args:
        repo: GitHub Repository object or repo name (for Azure DevOps)
        head_branch: The branch containing changes to be merged
        base_branch: The target branch for the PR
        pr_title: Title for the pull request
        pr_body: Description for the pull request
        user_id: User ID for service type detection (Azure DevOps)
        git_project_repo_id: Repository ID for service type detection and Azure DevOps

    Returns:
        GitHub PullRequest object or Azure DevOps pull request dictionary
    """
    # Determine service type for Azure DevOps
    if git_project_repo_id and user_id:
        logger.info(f"Using {git_project_repo_id} for repo type detection")
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.AZURE_DEVOPS:
            logger.info("Detected Azure DevOps repository")
            # Use Azure DevOps implementation

            git_project_repo = _get_azure_devops_credentials_by_repo_id(
                git_project_repo_id
            )
            return create_single_pull_request_azure_devops(
                organization=git_project_repo.azure_org_name,
                project_id=git_project_repo.azure_project_id,
                repo_id=git_project_repo.repo_id,
                access_token=git_project_repo.access_token,
                head_branch=head_branch,
                base_branch=base_branch,
                pr_title=pr_title,
                pr_body=pr_body,
            )
    logger.info("Detected GitHub repository")
    # GitHub implementation (default)
    if not isinstance(repo, Repository):
        raise ValueError("For GitHub repositories, repo must be a Repository object")
    try:
        # Check if PR already exists
        existing_prs: List[PullRequest] = get_pulls_with_retry_github(
            repo=repo,
            state="open",
            head=f"{repo.owner.login}:{head_branch}",
            base=base_branch,
        )

        # Use existing PR if it exists
        for pr in existing_prs:
            logger.info(f"Using existing PR #{pr.number}: {pr.html_url}")
            return pr

        # Create new PR if none exists
        pr = create_pull_with_retry_github(
            repo=repo, title=pr_title, body=pr_body, head=head_branch, base=base_branch
        )

        logger.info(f"Created new PR #{pr.number}: {pr.html_url}")
        return pr

    except Exception as e:
        logger.error(f"Error handling pull request: {str(e)}")
        raise


@blitzy_exponential_retry()
def create_all_pull_requests(
    repo_name: str,
    repo_id: str,
    head_branch: str,
    user_id: str,
    server: str,
    base_branch="",
    pr_title=None,
    pr_body=None,
    is_new_repo=False,
    git_project_repo_id: Optional[str] = None,
) -> List[Union[PullRequest, Dict]]:
    """
    Create pull requests for the specified branch and optionally for matching branches in submodules.
    Supports both GitHub and Azure DevOps repositories.

    Args:
        repo_name: The repository name
        repo_id: The repository ID
        head_branch: The branch containing changes to be merged
        user_id: User ID for authentication (needed for submodule access)
        server: Server URL for authentication (needed for submodule access)
        base_branch: The target branch for the PR (defaults to repo's default branch)
        pr_title: Custom PR title (defaults to "Autonomous changes created by Blitzy")
        pr_body: Custom PR body (defaults to standard message)
        is_new_repo: Whether this is a new repository
        git_project_repo_id: Repository ID for service type detection and Azure DevOps

    Returns:
        List of created/existing pull request objects (GitHub) or dictionaries (Azure DevOps)
    """
    # Determine service type
    if git_project_repo_id:
        logger.info(
            f"Using repository ID {git_project_repo_id} for repo type detection"
        )
        svc_type = _get_service_type(user_id, git_project_repo_id)
        if svc_type == SvcType.AZURE_DEVOPS:
            logger.info("Detected Azure DevOps repository")
            # Use Azure DevOps implementation

            return create_all_pull_requests_azure_devops(
                git_project_repo_id=git_project_repo_id,
                head_branch=head_branch,
                base_branch=base_branch,
                pr_title=pr_title,
                pr_body=pr_body,
                is_new_repo=is_new_repo,
            )

    logger.info("Detected GitHub repository")
    # GitHub implementation (default)
    repo, _ = get_github_repo(
        repo_name=repo_name,
        user_id=user_id,
        server=server,
        create=is_new_repo,
        repo_id=repo_id,
        git_project_repo_id=git_project_repo_id,
    )

    if not isinstance(repo, Repository):
        raise ValueError("Expected a PyGithub Repository object")
    if not base_branch or is_new_repo:
        base_branch = repo.default_branch

    created_prs = []

    # Create PR for main repository
    main_pr = create_single_pull_request(
        repo=repo,
        head_branch=head_branch,
        base_branch=base_branch,
        pr_title=pr_title or "Autonomous changes created by Blitzy",
        pr_body=pr_body or "This PR contains automated updates created by Blitzy",
    )
    created_prs.append(main_pr)

    # Check for submodules with matching branch
    try:
        # Get .gitmodules file content
        try:
            gitmodules_text = get_gitmodules_content(repo=repo, ref=base_branch)
        except GithubException as e:
            logger.warning(f"No .gitmodules file found assuming no submodules: {e}")
            return created_prs

        # Parse .gitmodules file
        submodules = parse_gitmodules(gitmodules_text)

        # For each submodule, check if the branch exists and create PR if it does
        for submodule_path, submodule_info in submodules.items():
            submodule_url = submodule_info["url"]

            # Convert SSH URL to HTTPS if needed
            https_url = ssh_to_https_url(submodule_url)

            # Extract owner, repo name, and domain
            submodule_owner, submodule_repo_name, domain = extract_repo_info_from_url(
                https_url
            )

            if not submodule_owner or not submodule_repo_name:
                logger.error(
                    f"Could not extract owner and repo name from URL: {https_url}"
                )
                continue

            # Get the submodule repository
            if not isinstance(repo, Repository):
                logger.error("Provided repo is not a valid Repository object")
                raise NotImplementedError(
                    "Creating pull requests is not implemented for non-PyGithub repositories"
                )

            access_token, _ = get_token_and_installation_id(
                server, user_id, str(repo.id)
            )

            # Create appropriate Github object based on domain
            if domain and domain != "github.com":
                # GitHub Enterprise
                g = Github(
                    base_url=f"https://{domain}/api/v3", login_or_token=access_token
                )
            else:
                # Public GitHub
                g = Github(access_token)

            try:
                submodule_repo = get_repo_from_github_with_retry(
                    g, submodule_owner, submodule_repo_name
                )
                if not submodule_repo:
                    logger.info(
                        f"Could not access submodule repository {submodule_owner}/{submodule_repo_name}"
                    )
                    continue

                # Check if the same branch exists in the submodule
                try:
                    get_branch_github(branch_name=head_branch, repo=submodule_repo)
                    logger.info(
                        f"Found matching branch '{head_branch}' in submodule {submodule_path}"
                    )

                    # Create PR for the submodule
                    submodule_pr_title = f"Submodule update: {pr_title or 'Autonomous changes created by Blitzy'}"
                    submodule_pr_body = f"This PR is part of changes in the parent repository.\n\n{pr_body or 'This PR contains automated updates created by Blitzy'}"

                    if main_pr:
                        submodule_pr_body += (
                            f"\n\nRelated to parent repository PR: {main_pr.html_url}"
                        )

                    submodule_pr = create_single_pull_request(
                        repo=submodule_repo,
                        head_branch=head_branch,
                        base_branch=submodule_repo.default_branch,  # Use the submodule's default branch
                        pr_title=submodule_pr_title,
                        pr_body=submodule_pr_body,
                    )
                    created_prs.append(submodule_pr)

                    new_body = (
                        main_pr.body
                        + f"\n\nSubmodule PR created: {submodule_pr.html_url}"
                    )
                    main_pr.edit(body=new_body)

                except GithubException as branch_error:
                    if branch_error.status == 404:
                        logger.info(
                            f"Branch '{head_branch}' does not exist in submodule {submodule_path}"
                        )
                    else:
                        logger.error(
                            f"Error checking branch in submodule {submodule_path}: {branch_error}"
                        )

            except GithubException as repo_error:
                logger.error(f"Could not access submodule repository: {repo_error}")

    except Exception as e:
        logger.error(f"Error processing submodules for PR: {e}")
        raise

    return created_prs


def create_github_commit(
    repo_name: str,
    repo_id: str,
    branch_name: str,
    base_branch: str,
    file_path: str,
    head_commit_hash: str,
    user_id: str,
    content="",
    create_new_branch=True,
    delete_file=False,
    is_new_repo=False,
    server=None,
    commit_message=None,
    git_project_repo_id: Optional[str] = None,
) -> GitHubGitCommit | None | bool | AzureDevOpsCommit:
    if not user_id:
        logger.error("user_id must be provided to create a commit")
        raise ValueError("user_id is required for commit creation")
    if git_project_repo_id:
        svc_type = _get_service_type(
            user_id=user_id, git_project_repo_id=git_project_repo_id
        )
        logger.debug(
            f"Using service type {svc_type} from git_project_repo_id {git_project_repo_id}"
        )
    else:
        logger.debug("Using fallback service type (GitHub)")
        svc_type = SvcType.GITHUB
    logger.info(f"Creating git commit for service type: {svc_type.value}")

    if svc_type == SvcType.GITHUB:
        repo, _ = get_github_repo(
            repo_name=repo_name,
            user_id=user_id,
            server=server,
            create=create_new_branch,
            repo_id=repo_id,
        )
        return create_commit_github(
            branch_name=branch_name,
            base_branch=base_branch,
            file_path=file_path,
            head_commit_hash=head_commit_hash,
            content=content,
            create_new_branch=create_new_branch,
            delete_file=delete_file,
            is_new_repo=is_new_repo,
            user_id=user_id,
            repo=repo,
            server=server,
            commit_message=commit_message,
        )
    elif svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        return create_azure_devops_commit_by_repo_id(
            branch_name=branch_name,
            base_branch=base_branch,
            file_path=file_path,
            content=content,
            create_new_branch=create_new_branch,
            delete_file=delete_file,
            is_new_repo=is_new_repo,
            commit_message=commit_message,
            git_project_repo_id=git_project_repo_id,
            head_commit_hash=head_commit_hash,
        )
    else:
        raise ValueError(f"Unsupported service type: {svc_type}")


def get_changed_files_between_commits(
    repo_name: str,
    branch_name: str,
    base_commit: str,
    head_commit: str,
    user_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> List[str]:
    """
    Get all files changed between two commits using local clone, including changes in submodules.
    Automatically detects and uses the appropriate service (GitHub or Azure DevOps).

    Args:
        repo_name: Name of the repository
        branch_name: Branch name (used to determine clone location)
        base_commit: Base commit hash to compare from
        head_commit: Head commit hash to compare to
        user_id: Optional user ID for service detection
        git_project_repo_id: Optional project repository ID for service detection

    Returns:
        List of file paths that were changed between the two commits
    """
    # Determine service type
    if git_project_repo_id and user_id:
        svc_type = _get_service_type(user_id, git_project_repo_id)
    else:
        svc_type = SvcType.GITHUB

    logger.info(
        f"Getting changed files between {base_commit[:7]} and {head_commit[:7]} using local clone ({svc_type.value})"
    )

    if svc_type == SvcType.AZURE_DEVOPS:
        return get_changed_files_between_commits_azure_devops(
            repo_name=repo_name,
            branch_name=branch_name,
            base_commit=base_commit,
            head_commit=head_commit,
        )
    else:
        return get_changed_files_between_commits_github(
            repo_name=repo_name,
            branch_name=branch_name,
            base_commit=base_commit,
            head_commit=head_commit,
        )


def get_head_commit_hash(
    repo_name: str,
    user_id: str,
    server: str,
    branch_name: str = "main",
    repo_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> str:
    """
    Get the head commit hash for a given branch in a git(github or azure currently) repository.

    :param git_project_repo_id: Has to be provided for azure support.
    """

    if git_project_repo_id:
        logger.info(
            f"Getting head commit hash for {repo_name} with git_project_repo_id {git_project_repo_id}"
        )
        svc_type = _get_service_type(
            user_id=user_id, git_project_repo_id=git_project_repo_id
        )
    else:
        logger.info(
            "No git_project_repo_id provided, using default github service type"
        )
        svc_type = SvcType.GITHUB
    logger.info(
        f"Processing repository {repo_name} with service type: {svc_type.value}"
    )

    if svc_type == SvcType.GITHUB:
        github_repo, _ = get_github_repo(
            repo_name=repo_name,
            user_id=user_id,
            server=server,
            create=False,
            repo_id=repo_id,
        )
        return get_head_commit_hash_github(
            github_repo=github_repo, branch_name=branch_name
        )
    elif svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        return get_azure_devops_head_commit_hash_by_repo_id(
            git_project_repo_id=git_project_repo_id, branch_name=branch_name
        )
    else:
        raise ValueError(f"Unsupported service type: {svc_type}")


def push_pull_latest_from_repository(
    repo_name: str,
    branch_name: str,
    user_id: str,
    server: str,
    repo_id: Optional[str] = None,
    git_project_repo_id: Optional[str] = None,
) -> bool:
    """
    Pull latest changes from repository using git pull.
    Automatically detects and uses the appropriate service (GitHub or Azure DevOps).

    Args:
        repo_name: Repository name
        branch_name: Branch to pull
        user_id: User ID for authentication
        server: Server URL for authentication
        repo_id: Repository ID
        git_project_repo_id: Optional project repository ID for service detection

    Returns:
        True if successful, False otherwise
    """
    # Determine service type
    if git_project_repo_id:
        svc_type = _get_service_type(user_id, git_project_repo_id)
    else:
        svc_type = SvcType.GITHUB

    logger.info(f"Push-Pulling latest changes for {repo_name} using {svc_type.value}")

    if svc_type == SvcType.AZURE_DEVOPS and git_project_repo_id:
        return push_pull_latest_from_repository_azure_devops(
            repo_name=repo_name,
            branch_name=branch_name,
            git_project_repo_id=git_project_repo_id,
        )
    else:
        return push_pull_latest_from_repository_github(
            repo_name=repo_name,
            branch_name=branch_name,
            user_id=user_id,
            server=server,
            repo_id=repo_id,
        )
