from dataclasses import dataclass
from typing import Optional, Dict, Callable, Union, Iterator, Generator
from google.auth import default
from google.auth.transport.requests import Request
from google.oauth2 import id_token
import os
import httpx

from blitzy_utils.logger import logger
from blitzy_utils.consts import (
    DEFAULT_MAX_RETRIES_SERVICES
)


@dataclass
class ServiceConfig:
    """
    Configuration for a service endpoint.

    :param url: Service base URL
    :param timeout: Request timeout in seconds (default is 30)
    :param max_retries: Maximum number of retry attempts (default is 3)
    :param retry_status_codes: HTTP status codes to trigger retry (default is (408, 429, 500, 502, 503, 504))
    """
    url: str
    timeout: int = 30
    max_retries: int = DEFAULT_MAX_RETRIES_SERVICES
    retry_status_codes: tuple = (408, 429, 500, 502, 503, 504)


class ServiceClient:
    """Client for making authenticated requests to Cloud Run services.

    Uses httpx for both synchronous and asynchronous HTTP requests with automatic
    retry logic and proper authentication.
    """

    def __init__(self, env_prefix: str = "SERVICE_URL_"):
        """
        Initialize the client and auto-register services from environment variables.

        :param env_prefix: Prefix for environment variables containing service URLs.
                         Example: If prefix is "SERVICE_URL_", looks for env vars like
                         SERVICE_URL_SECRETS, SERVICE_URL_AUTH, etc.
        :type env_prefix: str

        :raises google.auth.exceptions.DefaultCredentialsError: If default credentials cannot be obtained
        :raises ValueError: If environment variables with the specified prefix are malformed
        """
        self._services: Dict[str, ServiceConfig] = {}
        self._session = None
        self._async_session = None
        self._credentials, self._project = default()
        self._env_prefix = env_prefix
        self._register_services_from_env()

    def _register_services_from_env(self) -> None:
        """Automatically register services from environment variables."""
        for env_var, url in os.environ.items():
            if env_var.startswith(self._env_prefix):
                service_name = env_var[len(self._env_prefix):].lower()
                self.add_service(service_name, ServiceConfig(url=url))
                logger.debug(f"Auto-registered service {service_name} from environment variable {env_var}")

    def _create_session(self) -> httpx.Client:
        """Create a httpx client with retry configuration."""
        if self._session is None:
            self._session = httpx.Client(
                timeout=30.0,
                limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
            )
        return self._session

    def _create_async_session(self) -> httpx.AsyncClient:
        """Create async httpx client with retry configuration."""
        if self._async_session is None:
            self._async_session = httpx.AsyncClient(
                timeout=30.0,
                limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
            )
        return self._async_session

    def __enter__(self):
        """Context manager entry."""
        self._create_session()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()

    def add_service(self, service_name: str, config: ServiceConfig) -> None:
        """
        Register a new service configuration.

        :param service_name: Name of the service
        :param config: Service configuration
        """
        self._services[service_name] = config
        logger.debug(f"Added service configuration for {service_name}")

    def get_auth_token(self, target_url: str) -> str:
        """Generate authentication token for a service."""
        try:
            auth_req = Request()
            token = id_token.fetch_id_token(auth_req, target_url)
            return token
        except Exception as e:
            logger.error(f"Failed to get auth token: {str(e)}")
            raise

    def _get_service_config(self, service_name: str) -> ServiceConfig:
        """Get service configuration or raise error if not found."""
        config = self._services.get(service_name)
        if not config:
            raise ValueError(
                f"Service '{service_name}' not configured. Check environment variable {self._env_prefix}{service_name.upper()}")
        return config

    def _ensure_session(self):
        """Ensure sync session exists."""
        if self._session is None:
            self._create_session()

    def _ensure_async_session(self):
        """Ensure async session exists."""
        if self._async_session is None:
            self._create_async_session()

    def _make_request(
            self,
            method: str,
            service_name: str,
            endpoint: str = "",
            json: Optional[Dict] = None,
            data: Optional[Union[str, bytes, Iterator[bytes], Generator[bytes, None, None]]] = None,
            params: Optional[Dict] = None,
            headers: Optional[Dict] = None,
            timeout: Optional[int] = None,
            stream: bool = False
    ) -> httpx.Response:
        """
        Make an authenticated request to the service.

        :param method: HTTP method (GET, POST, PUT, DELETE)
        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data (mutually exclusive with data)
        :param data: Raw data (string, bytes, or generator/iterator) for the request body
        :param params: Query parameters
        :param headers: Additional headers
        :param timeout: Optional timeout override for this specific request
        :param stream: Whether to stream the response (for large downloads)
        """
        # Validate that we don't have both json and data
        if json is not None and data is not None:
            raise ValueError("Cannot specify both 'json' and 'data' parameters")

        self._ensure_session()
        config = self._get_service_config(service_name)
        url = f"{config.url.rstrip('/')}/{endpoint.lstrip('/')}"

        # Get fresh token and add to headers
        token = self.get_auth_token(config.url)
        headers = headers or {}
        headers["Authorization"] = f"Bearer {token}"

        # Add correlation ID from Flask context if available.
        try:
            from flask import g
            if hasattr(g, 'correlation_id'):
                headers['X-Correlation-ID'] = g.correlation_id
        except (ImportError, RuntimeError):
            # Handle cases where Flask is not installed or outside request context
            pass

        # Use provided timeout or fall back to config timeout
        request_timeout = timeout or config.timeout

        try:
            response = self._session.request(
                method=method,
                url=url,
                json=json,
                content=data,
                params=params,
                headers=headers,
                timeout=request_timeout
            )
            return response
        except httpx.RequestError as e:
            logger.error(f"Request failed: {str(e)}")
            raise

    async def _make_async_request(
            self,
            method: str,
            service_name: str,
            endpoint: str = "",
            json: Optional[Dict] = None,
            data: Optional[Union[str, bytes, Iterator[bytes], Generator[bytes, None, None]]] = None,
            params: Optional[Dict] = None,
            headers: Optional[Dict] = None,
            timeout: Optional[int] = None,
            stream: bool = False
    ) -> httpx.Response:
        """
        Make an authenticated async request to the service.

        :param method: HTTP method (GET, POST, PUT, DELETE)
        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data (mutually exclusive with data)
        :param data: Raw data (string, bytes, or generator/iterator) for the request body
        :param params: Query parameters
        :param headers: Additional headers
        :param timeout: Optional timeout override for this specific request
        :param stream: Whether to stream the response (for large downloads)
        :return: aiohttp ClientResponse
        """
        # Validate that we don't have both json and data
        if json is not None and data is not None:
            raise ValueError("Cannot specify both 'json' and 'data' parameters")

        self._ensure_async_session()
        config = self._get_service_config(service_name)
        url = f"{config.url.rstrip('/')}/{endpoint.lstrip('/')}"

        # Get fresh token and add to headers
        token = self.get_auth_token(config.url)
        headers = headers or {}
        headers["Authorization"] = f"Bearer {token}"

        # Add correlation ID from Flask context if available.
        try:
            from flask import g
            if hasattr(g, 'correlation_id'):
                headers['X-Correlation-ID'] = g.correlation_id
        except (ImportError, RuntimeError):
            # Handle cases where Flask is not installed or outside request context
            pass

        # Use provided timeout or fall back to config timeout
        request_timeout = timeout or config.timeout

        try:
            response = await self._async_session.request(
                method=method,
                url=url,
                json=json,
                content=data,
                params=params,
                headers=headers,
                timeout=request_timeout
            )
            return response
        except httpx.RequestError as e:
            logger.error(f"Async request failed: {str(e)}")
            raise

    def get(self, service_name: str, endpoint: str = "", params: Optional[Dict] = None,
            timeout: Optional[int] = None) -> httpx.Response:
        """
        Make GET request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param params: Query parameters
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("GET", service_name, endpoint, params=params, timeout=timeout)

    def post(self, service_name: str, endpoint: str = "", json: Optional[Dict] = None,
             timeout: Optional[int] = None) -> httpx.Response:
        """
        Make POST request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("POST", service_name, endpoint, json=json, timeout=timeout)

    def post_binary(self, service_name: str, endpoint: str = "", data: Union[str, bytes] = None,
                   params: Optional[Dict] = None, headers: Optional[Dict] = None,
                   timeout: Optional[int] = None) -> httpx.Response:
        """
        Make POST request with binary/raw data to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param data: Binary data or string to upload
        :param params: Query parameters
        :param headers: Additional headers (Content-Type, etc.)
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("POST", service_name, endpoint, data=data,
                                params=params, headers=headers, timeout=timeout)

    def get_stream(self, service_name: str, endpoint: str = "", params: Optional[Dict] = None,
                   timeout: Optional[int] = None) -> httpx.Response:
        """
        Make GET request with streaming response to service.

        Use this for downloading large files or streaming data.
        The response should be consumed in chunks using response.iter_content().

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param params: Query parameters
        :param timeout: Optional timeout override for this request
        :return: Streaming response from the service
        """
        return self._make_request("GET", service_name, endpoint, params=params,
                                timeout=timeout, stream=True)

    def post_stream(self, service_name: str, endpoint: str = "",
                   data_generator: Optional[Union[Iterator[bytes], Generator[bytes, None, None]]] = None, json: Optional[Dict] = None,
                   params: Optional[Dict] = None, headers: Optional[Dict] = None,
                   timeout: Optional[int] = None) -> httpx.Response:
        """
        Make POST request with streaming data to service.

        Use this for uploading large files or streaming data.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param data_generator: Generator or iterator yielding data chunks
        :param json: JSON body data (alternative to data_generator)
        :param params: Query parameters
        :param headers: Additional headers
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        if data_generator is not None:
            return self._make_request("POST", service_name, endpoint, data=data_generator,
                                    params=params, headers=headers, timeout=timeout)
        else:
            return self._make_request("POST", service_name, endpoint, json=json,
                                    params=params, headers=headers, timeout=timeout)

    def put(self, service_name: str, endpoint: str = "", json: Optional[Dict] = None,
            timeout: Optional[int] = None) -> httpx.Response:
        """
        Make PUT request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("PUT", service_name, endpoint, json=json, timeout=timeout)

    def delete(self, service_name: str, endpoint: str = "",
               timeout: Optional[int] = None) -> httpx.Response:
        """
        Make DELETE request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param timeout: Optional timeout override for this request
        :return: Response from the service
        """
        return self._make_request("DELETE", service_name, endpoint, timeout=timeout)

    # Async HTTP Methods
    async def async_get(self, service_name: str, endpoint: str = "", params: Optional[Dict] = None,
                       timeout: Optional[int] = None) -> httpx.Response:
        """
        Make async GET request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param params: Query parameters
        :param timeout: Optional timeout override for this request
        :return: httpx Response
        """
        return await self._make_async_request("GET", service_name, endpoint, params=params, timeout=timeout)

    async def async_post(self, service_name: str, endpoint: str = "", json: Optional[Dict] = None,
                        timeout: Optional[int] = None) -> httpx.Response:
        """
        Make async POST request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data
        :param timeout: Optional timeout override for this request
        :return: httpx Response
        """
        return await self._make_async_request("POST", service_name, endpoint, json=json, timeout=timeout)

    async def async_put(self, service_name: str, endpoint: str = "", json: Optional[Dict] = None,
                       timeout: Optional[int] = None) -> httpx.Response:
        """
        Make async PUT request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param json: JSON body data
        :param timeout: Optional timeout override for this request
        :return: httpx Response
        """
        return await self._make_async_request("PUT", service_name, endpoint, json=json, timeout=timeout)

    async def async_delete(self, service_name: str, endpoint: str = "",
                          timeout: Optional[int] = None) -> httpx.Response:
        """
        Make async DELETE request to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param timeout: Optional timeout override for this request
        :return: httpx Response
        """
        return await self._make_async_request("DELETE", service_name, endpoint, timeout=timeout)

    async def async_post_binary(self, service_name: str, endpoint: str = "", data: Union[str, bytes] = None,
                               params: Optional[Dict] = None, headers: Optional[Dict] = None,
                               timeout: Optional[int] = None) -> httpx.Response:
        """
        Make async POST request with binary/raw data to service.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param data: Binary data or string to upload
        :param params: Query parameters
        :param headers: Additional headers (Content-Type, etc.)
        :param timeout: Optional timeout override for this request
        :return: httpx Response
        """
        return await self._make_async_request("POST", service_name, endpoint, data=data,
                                            params=params, headers=headers, timeout=timeout)

    async def async_get_stream(self, service_name: str, endpoint: str = "", params: Optional[Dict] = None,
                              timeout: Optional[int] = None) -> httpx.Response:
        """
        Make async GET request with streaming response to service.

        Use this for downloading large files or streaming data asynchronously.
        The response should be consumed using async iteration: async for chunk in response.content.iter_chunked().

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param params: Query parameters
        :param timeout: Optional timeout override for this request
        :return: Streaming aiohttp ClientResponse
        """
        return await self._make_async_request("GET", service_name, endpoint, params=params,
                                            timeout=timeout, stream=True)

    async def async_post_stream(self, service_name: str, endpoint: str = "",
                               data_generator: Optional[Union[Iterator[bytes], Generator[bytes, None, None]]] = None, json: Optional[Dict] = None,
                               params: Optional[Dict] = None, headers: Optional[Dict] = None,
                               timeout: Optional[int] = None) -> httpx.Response:
        """
        Make async POST request with streaming data to service.

        Use this for uploading large files or streaming data asynchronously.

        :param service_name: Name of the registered service
        :param endpoint: Endpoint path
        :param data_generator: Async generator or iterator yielding data chunks
        :param json: JSON body data (alternative to data_generator)
        :param params: Query parameters
        :param headers: Additional headers
        :param timeout: Optional timeout override for this request
        :return: aiohttp ClientResponse
        """
        if data_generator is not None:
            return await self._make_async_request("POST", service_name, endpoint, data=data_generator,
                                                 params=params, headers=headers, timeout=timeout)
        else:
            return await self._make_async_request("POST", service_name, endpoint, json=json,
                                                 params=params, headers=headers, timeout=timeout)

    # Async Context Manager Support
    async def __aenter__(self):
        """Async context manager entry."""
        self._ensure_async_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.async_close()

    async def async_close(self) -> None:
        """Close async session properly."""
        if self._async_session:
            await self._async_session.aclose()
            self._async_session = None
            logger.debug("Service client async session closed")

    def close(self) -> None:
        """Close both sync and async sessions."""
        if self._session:
            self._session.close()
            self._session = None
            logger.debug("Service client sync session closed")
        if self._async_session:
            # Note: async session should be closed with await session.close()
            # This is a fallback for cleanup, but proper async cleanup should be done explicitly
            try:
                import asyncio
                if not self._async_session.closed:
                    # Try to close gracefully if event loop is running
                    try:
                        loop = asyncio.get_running_loop()
                        loop.create_task(self._async_session.close())
                    except RuntimeError:
                        # No running loop, create a new one for cleanup
                        asyncio.run(self._async_session.close())
            except Exception as e:
                logger.warning(f"Failed to close async session: {e}")
            finally:
                self._async_session = None
                logger.debug("Service client async session closed")


def create_service_error_handler(base_error_class: type) -> Callable:
    """
    Create an error handler function with the BaseError class in closure.

    :param base_error_class: Your BaseError class from the main app
    :return: Error handler function
    """

    ERROR_MAPPINGS = {
        400: ("Invalid request", "SERVICE_VALIDATION_ERROR"),
        401: ("Authentication failed", "SERVICE_AUTH_ERROR"),
        403: ("Access denied", "SERVICE_FORBIDDEN_ERROR"),
        404: ("Resource not found", "SERVICE_NOT_FOUND_ERROR"),
        408: ("Service timeout", "SERVICE_TIMEOUT_ERROR"),
        409: ("Resource conflict", "SERVICE_CONFLICT_ERROR"),
        422: ("Unprocessable entity", "SERVICE_UNPROCESSABLE_ERROR"),
        429: ("Too many requests", "SERVICE_RATE_LIMIT_ERROR"),
        500: ("Internal service error", "SERVICE_INTERNAL_ERROR"),
        502: ("Bad gateway", "SERVICE_BAD_GATEWAY_ERROR"),
        503: ("Service temporarily unavailable", "SERVICE_UNAVAILABLE_ERROR"),
        504: ("Gateway timeout", "SERVICE_GATEWAY_TIMEOUT_ERROR"),
    }

    def handle_service_response(response: httpx.Response) -> None:
        """
        Handle service response and raise the appropriate error using BaseError. As BaseError is part of `blitzy_utils`
        and we need to add this workaround.

        :param response: The response object from requests
        :raises: Instance of base_error_class with appropriate message and status code
        """
        if response.status_code < 400:
            return

        try:
            error_data = response.json()
        except Exception:
            error_data = {"raw_response": response.text}

        logger.debug(f"Service response: {error_data} with status code {response.status_code}")

        error_message = (
            error_data.get("error") or
            error_data.get("message") or
            error_data.get("detail") or
            error_data.get("error_description") or
            error_data.get("raw_response")
        )

        default_message, error_code = ERROR_MAPPINGS.get(
            response.status_code,
            (f"Service error: {response.status_code}", "SERVICE_ERROR")
        )

        final_message = error_message if error_message else default_message

        detailed_message = f"{final_message}"

        error = base_error_class(
            message=detailed_message,
            status_code=response.status_code,
            error_code=error_code
        )

        error.response_data = error_data

        raise error

    return handle_service_response
