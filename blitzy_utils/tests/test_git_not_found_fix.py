#!/usr/bin/env python3
"""
Test script to verify that the git command not found error is properly handled.
This test simulates the scenario where git is not available in the system PATH.
"""

import os
import sys
import tempfile
import unittest.mock
from unittest.mock import patch, Mock
import subprocess

# Add the blitzy_utils directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def test_git_not_found_error_handling():
    """Test that FileNotFoundError for git command is properly handled."""
    print("🧪 Testing Git Command Not Found Error Handling")
    print("=" * 60)

    # Import after path setup
    from blitzy_utils.github import clone_repository_with_auth

    with tempfile.TemporaryDirectory() as temp_dir:
        clone_path = os.path.join(temp_dir, "test_repo")

        print(f"📁 Test directory: {clone_path}")

        # Mock subprocess.run to raise FileNotFoundError (git not found)
        def mock_subprocess_run(*args, **kwargs):
            # Check if this is a git command
            if args[0] and len(args[0]) > 0 and args[0][0] == "git":
                raise FileNotFoundError("[Errno 2] No such file or directory: 'git'")
            # For non-git commands, use the real subprocess.run
            return subprocess.run(*args, **kwargs)

        print("\n1️⃣ Testing clone_repository_with_auth with git not found...")

        # Patch subprocess.run in the github module specifically
        with patch('blitzy_utils.github.subprocess.run', side_effect=mock_subprocess_run):
            try:
                result = clone_repository_with_auth(
                    full_repo_name="octocat/Hello-World",
                    access_token="dummy_token",
                    clone_path=clone_path,
                    branch_name="main"
                )

                print(f"📊 Clone result: {result}")

                # The function should return False (not raise an exception)
                if result is False:
                    print("✅ Function correctly returned False when git not found")
                else:
                    print("❌ Function should have returned False")
                    return False

            except Exception as e:
                print(f"❌ Function raised unexpected exception: {e}")
                return False

        print("\n🎯 Test completed successfully!")
        print("✅ The git not found error is properly handled:")
        print("   - clone_repository_with_auth returns False instead of crashing")
        print("   - Error messages are clear and informative")

        return True


def test_git_available_normal_flow():
    """Test that normal flow works when git is available (but with auth failure)."""
    print("\n🧪 Testing Normal Flow When Git Is Available")
    print("=" * 60)

    # Import after path setup
    from blitzy_utils.github import clone_repository_with_auth

    with tempfile.TemporaryDirectory() as temp_dir:
        clone_path = os.path.join(temp_dir, "test_repo")

        print(f"📁 Test directory: {clone_path}")

        # Mock subprocess.run to simulate git command available but auth failure
        def mock_subprocess_run(*args, **kwargs):
            # Check if this is a git clone command
            if args[0] and len(args[0]) > 0 and args[0][0] == "git" and "clone" in args[0]:
                # Simulate authentication failure (non-zero return code)
                mock_result = Mock()
                mock_result.returncode = 128  # Git authentication error
                mock_result.stderr = "fatal: Authentication failed"
                return mock_result
            # For other git commands, simulate success
            elif args[0] and len(args[0]) > 0 and args[0][0] == "git":
                mock_result = Mock()
                mock_result.returncode = 0
                mock_result.stderr = ""
                return mock_result
            # For non-git commands, use the real subprocess.run
            return subprocess.run(*args, **kwargs)

        print("\n1️⃣ Testing clone_repository_with_auth with git available but auth failure...")

        # Patch subprocess.run in the github module specifically
        with patch('blitzy_utils.github.subprocess.run', side_effect=mock_subprocess_run):
            try:
                result = clone_repository_with_auth(
                    full_repo_name="octocat/Hello-World",
                    access_token="invalid_token",
                    clone_path=clone_path,
                    branch_name="main"
                )

                print(f"📊 Clone result: {result}")

                # The function should return False due to auth failure
                if result is False:
                    print("✅ Function correctly returned False due to authentication failure")
                else:
                    print("❌ Function should have returned False due to auth failure")
                    return False

            except Exception as e:
                print(f"❌ Function raised unexpected exception: {e}")
                return False

        print("\n🎯 Normal flow test completed successfully!")
        print("✅ When git is available:")
        print("   - Function proceeds to actual git commands")
        print("   - Authentication failures are handled gracefully")
        print("   - No FileNotFoundError exceptions occur")

        return True


def main():
    """Run all tests."""
    try:
        success1 = test_git_not_found_error_handling()
        success2 = test_git_available_normal_flow()

        if success1 and success2:
            print("\n🎉 All tests passed!")
            return 0
        else:
            print("\n💥 Some tests failed!")
            return 1

    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
