"""
Unit tests for ServiceClient streaming functionality.
Tests both sync and async streaming methods.
"""
import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import os

from blitzy_utils.service_client import ServiceClient


class TestServiceClientStreaming:
    """Test ServiceClient streaming functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.test_env = {
            'SERVICE_URL_ADMIN': 'https://admin.example.com',
            'SERVICE_URL_STORAGE': 'https://storage.example.com'
        }
    
    @patch('blitzy_utils.service_client.default')
    def test_get_stream_method_exists(self, mock_default):
        """Test that get_stream method exists and has correct signature."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            
            # Check if get_stream method exists
            assert hasattr(client, 'get_stream'), "ServiceClient should have get_stream method"
            
            # Check method signature
            import inspect
            sig = inspect.signature(client.get_stream)
            params = list(sig.parameters.keys())
            
            expected_params = ['service_name', 'endpoint', 'params', 'timeout']
            for param in expected_params:
                assert param in params, f"get_stream should have {param} parameter"
    
    @patch('blitzy_utils.service_client.default')
    def test_post_stream_method_exists(self, mock_default):
        """Test that post_stream method exists and has correct signature."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            
            # Check if post_stream method exists
            assert hasattr(client, 'post_stream'), "ServiceClient should have post_stream method"
            
            # Check method signature
            import inspect
            sig = inspect.signature(client.post_stream)
            params = list(sig.parameters.keys())
            
            expected_params = ['service_name', 'endpoint', 'data_generator', 'json', 'params', 'headers', 'timeout']
            for param in expected_params:
                assert param in params, f"post_stream should have {param} parameter"
    
    @patch('blitzy_utils.service_client.default')
    def test_get_stream_calls_make_request_with_stream_true(self, mock_default):
        """Test that get_stream calls _make_request with stream=True."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._make_request = Mock(return_value=Mock())
            
            # Call get_stream
            result = client.get_stream(
                service_name="storage",
                endpoint="/v1/files/download",
                params={'file_id': 'test123'},
                timeout=300
            )
            
            # Verify _make_request was called with stream=True
            client._make_request.assert_called_once_with(
                "GET", "storage", "/v1/files/download",
                params={'file_id': 'test123'},
                timeout=300,
                stream=True
            )
    
    @patch('blitzy_utils.service_client.default')
    def test_post_stream_with_data_generator(self, mock_default):
        """Test post_stream with data generator."""
        mock_default.return_value = (Mock(), 'test-project')
        
        def data_generator():
            yield b"chunk1"
            yield b"chunk2"
            yield b"chunk3"
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._make_request = Mock(return_value=Mock())
            
            # Call post_stream with data generator
            result = client.post_stream(
                service_name="storage",
                endpoint="/v1/files/upload",
                data_generator=data_generator(),
                params={'file_path': 'large-file.bin'},
                headers={'Content-Type': 'application/octet-stream'},
                timeout=600
            )
            
            # Verify _make_request was called correctly
            client._make_request.assert_called_once()
            call_args = client._make_request.call_args
            
            assert call_args[0] == ("POST", "storage", "/v1/files/upload")
            assert call_args[1]['params'] == {'file_path': 'large-file.bin'}
            assert call_args[1]['headers'] == {'Content-Type': 'application/octet-stream'}
            assert call_args[1]['timeout'] == 600
    
    @patch('blitzy_utils.service_client.default')
    def test_post_stream_with_json_fallback(self, mock_default):
        """Test post_stream falls back to JSON when no data_generator provided."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._make_request = Mock(return_value=Mock())
            
            # Call post_stream with JSON data
            result = client.post_stream(
                service_name="admin",
                endpoint="/v1/metadata",
                json={'file_info': 'test'},
                params={'action': 'create'}
            )
            
            # Verify _make_request was called with JSON
            client._make_request.assert_called_once_with(
                "POST", "admin", "/v1/metadata",
                json={'file_info': 'test'},
                params={'action': 'create'},
                headers=None,
                timeout=None
            )
    
    @patch('blitzy_utils.service_client.default')
    def test_make_request_supports_stream_parameter(self, mock_default):
        """Test that _make_request method supports stream parameter."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            
            # Check _make_request signature includes stream parameter
            import inspect
            sig = inspect.signature(client._make_request)
            params = list(sig.parameters.keys())
            
            assert 'stream' in params, "_make_request should support stream parameter"
    
    @patch('blitzy_utils.service_client.default')
    def test_async_get_stream_method_exists(self, mock_default):
        """Test that async_get_stream method exists."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            
            # Check if async_get_stream method exists
            assert hasattr(client, 'async_get_stream'), "ServiceClient should have async_get_stream method"
            
            # Check method signature
            import inspect
            sig = inspect.signature(client.async_get_stream)
            params = list(sig.parameters.keys())
            
            expected_params = ['service_name', 'endpoint', 'params', 'timeout']
            for param in expected_params:
                assert param in params, f"async_get_stream should have {param} parameter"
    
    @patch('blitzy_utils.service_client.default')
    def test_async_post_stream_method_exists(self, mock_default):
        """Test that async_post_stream method exists."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            
            # Check if async_post_stream method exists
            assert hasattr(client, 'async_post_stream'), "ServiceClient should have async_post_stream method"
            
            # Check method signature
            import inspect
            sig = inspect.signature(client.async_post_stream)
            params = list(sig.parameters.keys())
            
            expected_params = ['service_name', 'endpoint', 'data_generator', 'json', 'params', 'headers', 'timeout']
            for param in expected_params:
                assert param in params, f"async_post_stream should have {param} parameter"
    
    @patch('blitzy_utils.service_client.default')
    @patch('blitzy_utils.service_client.AIOHTTP_AVAILABLE', True)
    async def test_async_get_stream_calls_make_async_request(self, mock_default):
        """Test that async_get_stream calls _make_async_request with stream=True."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._make_async_request = AsyncMock(return_value=Mock())
            
            # Call async_get_stream
            result = await client.async_get_stream(
                service_name="storage",
                endpoint="/v1/files/download",
                params={'file_id': 'async_test123'},
                timeout=300
            )
            
            # Verify _make_async_request was called with stream=True
            client._make_async_request.assert_called_once_with(
                "GET", "storage", "/v1/files/download",
                params={'file_id': 'async_test123'},
                timeout=300,
                stream=True
            )
    
    @patch('blitzy_utils.service_client.default')
    def test_make_async_request_supports_stream_parameter(self, mock_default):
        """Test that _make_async_request method supports stream parameter."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            
            # Check _make_async_request signature includes stream parameter
            import inspect
            sig = inspect.signature(client._make_async_request)
            params = list(sig.parameters.keys())
            
            assert 'stream' in params, "_make_async_request should support stream parameter"
            assert 'data' in params, "_make_async_request should support data parameter"


class TestStreamingIntegration:
    """Integration tests for streaming functionality."""
    
    @patch('blitzy_utils.service_client.default')
    def test_streaming_download_workflow(self, mock_default):
        """Test complete streaming download workflow."""
        mock_default.return_value = (Mock(), 'test-project')
        
        # Mock streaming response
        mock_response = Mock()
        mock_response.iter_content.return_value = [b"chunk1", b"chunk2", b"chunk3"]
        
        with patch.dict(os.environ, {'SERVICE_URL_STORAGE': 'https://storage.example.com'}):
            client = ServiceClient()
            client._make_request = Mock(return_value=mock_response)
            
            # Use get_stream for downloading
            response = client.get_stream(
                service_name="storage",
                endpoint="/v1/files/download/large-file.zip",
                params={'company_id': 'test123'},
                timeout=600
            )
            
            # Verify streaming request was made
            client._make_request.assert_called_once_with(
                "GET", "storage", "/v1/files/download/large-file.zip",
                params={'company_id': 'test123'},
                timeout=600,
                stream=True
            )
            
            # Simulate consuming the stream
            chunks = list(response.iter_content())
            assert chunks == [b"chunk1", b"chunk2", b"chunk3"]
    
    @patch('blitzy_utils.service_client.default')
    def test_streaming_upload_workflow(self, mock_default):
        """Test complete streaming upload workflow."""
        mock_default.return_value = (Mock(), 'test-project')
        
        def large_file_generator():
            """Simulate reading a large file in chunks."""
            for i in range(5):
                yield f"large_file_chunk_{i}".encode()
        
        mock_response = Mock()
        mock_response.json.return_value = {'filePath': 'gs://bucket/large-file.bin'}
        
        with patch.dict(os.environ, {'SERVICE_URL_STORAGE': 'https://storage.example.com'}):
            client = ServiceClient()
            client._make_request = Mock(return_value=mock_response)
            
            # Use post_stream for uploading
            response = client.post_stream(
                service_name="storage",
                endpoint="/v1/files/upload",
                data_generator=large_file_generator(),
                params={'file_path': 'company_test/large-file.bin'},
                headers={'Content-Type': 'application/octet-stream'},
                timeout=1200
            )
            
            # Verify streaming request was made
            client._make_request.assert_called_once()
            call_args = client._make_request.call_args
            
            assert call_args[0] == ("POST", "storage", "/v1/files/upload")
            assert call_args[1]['params'] == {'file_path': 'company_test/large-file.bin'}
            assert call_args[1]['headers'] == {'Content-Type': 'application/octet-stream'}
            assert call_args[1]['timeout'] == 1200
            
            # Verify response
            result = response.json()
            assert result['filePath'] == 'gs://bucket/large-file.bin'
