"""
Integration tests for ServiceClient.
Tests real scenarios with actual HTTP calls (mocked at transport level).
"""
import pytest
import os
import json
from unittest.mock import Mock, patch, MagicMock
from blitzy_utils.service_client import ServiceClient, ServiceConfig


class TestServiceClientRealScenarios:
    """Test ServiceClient with realistic scenarios."""
    
    def setup_method(self):
        """Setup test environment."""
        self.test_services = {
            'SERVICE_URL_ADMIN': 'https://admin-service.example.com',
            'SERVICE_URL_AUTH': 'https://auth-service.example.com',
            'SERVICE_URL_STORAGE': 'https://storage-service.example.com'
        }
    
    @patch('blitzy_utils.service_client.default')
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', True)
    @patch('blitzy_utils.service_client.httpx')
    def test_file_upload_to_admin_service(self, mock_httpx, mock_default):
        """Test realistic file upload scenario to admin service."""
        # Setup authentication
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        # Setup HTTP client mock
        mock_client = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'success': True,
            'filePath': 'gs://test-bucket/company_123/attachments_proj456/file789.pdf',
            'size': 1024
        }
        mock_response.raise_for_status.return_value = None
        mock_client.request.return_value = mock_response
        mock_httpx.Client.return_value = mock_client
        
        # Test file data
        file_content = b"PDF file content here..."
        
        with patch.dict(os.environ, self.test_services):
            with patch('blitzy_utils.service_client.id_token') as mock_id_token:
                mock_id_token.fetch_id_token.return_value = 'valid-jwt-token'
                
                with ServiceClient() as client:
                    response = client.post_binary(
                        service_name="admin",
                        endpoint="/v1/storage/upload",
                        data=file_content,
                        params={
                            'file_path': 'company_123/attachments_proj456/file789.pdf',
                            'company_id': '123'
                        },
                        headers={
                            'Content-Type': 'application/pdf'
                        },
                        timeout=600
                    )
        
        # Verify the request
        assert mock_client.request.call_count == 1
        call_args = mock_client.request.call_args
        
        # Check request details
        assert call_args[1]['method'] == 'POST'
        assert call_args[1]['url'] == 'https://admin-service.example.com/v1/storage/upload'
        assert call_args[1]['data'] == file_content
        assert call_args[1]['params']['file_path'] == 'company_123/attachments_proj456/file789.pdf'
        assert call_args[1]['params']['company_id'] == '123'
        assert call_args[1]['headers']['Content-Type'] == 'application/pdf'
        assert call_args[1]['headers']['Authorization'] == 'Bearer valid-jwt-token'
        assert call_args[1]['timeout'] == 600
        
        # Check response
        result = response.json()
        assert result['success'] is True
        assert 'gs://test-bucket' in result['filePath']
    
    @patch('blitzy_utils.service_client.default')
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', True)
    @patch('blitzy_utils.service_client.httpx')
    def test_file_download_from_admin_service(self, mock_httpx, mock_default):
        """Test realistic file download scenario from admin service."""
        # Setup authentication
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        # Setup HTTP client mock
        mock_client = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b"Downloaded file content here..."
        mock_response.headers = {'Content-Type': 'application/pdf', 'Content-Length': '27'}
        mock_response.raise_for_status.return_value = None
        mock_client.request.return_value = mock_response
        mock_httpx.Client.return_value = mock_client
        
        with patch.dict(os.environ, self.test_services):
            with patch('blitzy_utils.service_client.id_token') as mock_id_token:
                mock_id_token.fetch_id_token.return_value = 'valid-jwt-token'
                
                with ServiceClient() as client:
                    response = client.get(
                        service_name="admin",
                        endpoint="/v1/storage/download",
                        params={
                            'file_path': 'company_123/attachments_proj456/file789.pdf',
                            'company_id': '123'
                        },
                        timeout=300
                    )
        
        # Verify the request
        call_args = mock_client.request.call_args
        assert call_args[1]['method'] == 'GET'
        assert call_args[1]['url'] == 'https://admin-service.example.com/v1/storage/download'
        assert call_args[1]['params']['file_path'] == 'company_123/attachments_proj456/file789.pdf'
        assert call_args[1]['headers']['Authorization'] == 'Bearer valid-jwt-token'
        
        # Check response
        assert response.content == b"Downloaded file content here..."
        assert response.headers['Content-Type'] == 'application/pdf'
    
    @patch('blitzy_utils.service_client.default')
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', False)
    @patch('blitzy_utils.service_client.requests')
    def test_fallback_to_requests_for_binary_upload(self, mock_requests, mock_default):
        """Test fallback to requests when httpx is not available."""
        # Setup authentication
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        # Setup requests session mock
        mock_session = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'filePath': 'gs://bucket/file.txt'}
        mock_response.raise_for_status.return_value = None
        mock_session.request.return_value = mock_response
        mock_requests.Session.return_value = mock_session
        
        # Mock retry adapter setup
        mock_adapter = Mock()
        mock_requests.adapters.HTTPAdapter.return_value = mock_adapter
        
        file_content = b"test file content"
        
        with patch.dict(os.environ, self.test_services):
            with patch('blitzy_utils.service_client.id_token') as mock_id_token:
                mock_id_token.fetch_id_token.return_value = 'valid-jwt-token'
                
                with ServiceClient() as client:
                    response = client.post_binary(
                        service_name="admin",
                        endpoint="/v1/storage/upload",
                        data=file_content,
                        params={'file_path': 'test/file.txt'},
                        headers={'Content-Type': 'text/plain'}
                    )
        
        # Verify requests was used
        mock_requests.Session.assert_called_once()
        mock_session.request.assert_called_once()
        
        call_args = mock_session.request.call_args
        assert call_args[1]['data'] == file_content
        assert call_args[1]['headers']['Authorization'] == 'Bearer valid-jwt-token'
    
    @patch('blitzy_utils.service_client.default')
    def test_multiple_services_registration(self, mock_default):
        """Test that multiple services are registered correctly."""
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        with patch.dict(os.environ, self.test_services):
            client = ServiceClient()
        
        # Verify all services were registered
        assert 'admin' in client._services
        assert 'auth' in client._services
        assert 'storage' in client._services
        
        assert client._services['admin'].url == 'https://admin-service.example.com'
        assert client._services['auth'].url == 'https://auth-service.example.com'
        assert client._services['storage'].url == 'https://storage-service.example.com'
    
    @patch('blitzy_utils.service_client.default')
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', True)
    @patch('blitzy_utils.service_client.httpx')
    def test_error_handling_and_retry(self, mock_httpx, mock_default):
        """Test error handling and retry behavior."""
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        # Setup client that fails first, then succeeds
        mock_client = Mock()
        mock_error_response = Mock()
        mock_error_response.status_code = 503
        mock_success_response = Mock()
        mock_success_response.status_code = 200
        mock_success_response.json.return_value = {'success': True}
        
        # First call fails, second succeeds (simulating retry)
        mock_client.request.side_effect = [
            Exception("Service temporarily unavailable"),
            mock_success_response
        ]
        mock_httpx.Client.return_value = mock_client
        
        with patch.dict(os.environ, self.test_services):
            with patch('blitzy_utils.service_client.id_token') as mock_id_token:
                mock_id_token.fetch_id_token.return_value = 'valid-jwt-token'
                
                with ServiceClient() as client:
                    # This should raise the exception since we're not handling retries at this level
                    with pytest.raises(Exception, match="Service temporarily unavailable"):
                        client.post(
                            service_name="admin",
                            endpoint="/v1/test",
                            json={'test': 'data'}
                        )
    
    @patch('blitzy_utils.service_client.default')
    def test_custom_service_configuration(self, mock_default):
        """Test adding custom service configuration."""
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        with patch.dict(os.environ, {}):  # No env services
            client = ServiceClient()
            
            # Add custom service
            custom_config = ServiceConfig(
                url="https://custom-service.example.com",
                timeout=60,
                max_retries=5
            )
            client.add_service("custom", custom_config)
        
        assert 'custom' in client._services
        assert client._services['custom'].url == "https://custom-service.example.com"
        assert client._services['custom'].timeout == 60
        assert client._services['custom'].max_retries == 5
    
    @patch('blitzy_utils.service_client.default')
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', True)
    @patch('blitzy_utils.service_client.httpx')
    def test_large_file_upload_timeout(self, mock_httpx, mock_default):
        """Test handling of large file uploads with custom timeout."""
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        mock_client = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'filePath': 'gs://bucket/large-file.zip'}
        mock_client.request.return_value = mock_response
        mock_httpx.Client.return_value = mock_client
        
        # Simulate large file (10MB)
        large_file_content = b"x" * (10 * 1024 * 1024)
        
        with patch.dict(os.environ, self.test_services):
            with patch('blitzy_utils.service_client.id_token') as mock_id_token:
                mock_id_token.fetch_id_token.return_value = 'valid-jwt-token'
                
                with ServiceClient() as client:
                    response = client.post_binary(
                        service_name="admin",
                        endpoint="/v1/storage/upload",
                        data=large_file_content,
                        params={'file_path': 'large-files/archive.zip'},
                        headers={'Content-Type': 'application/zip'},
                        timeout=1200  # 20 minutes for large file
                    )
        
        # Verify timeout was set correctly
        call_args = mock_client.request.call_args
        assert call_args[1]['timeout'] == 1200
        assert len(call_args[1]['data']) == 10 * 1024 * 1024
