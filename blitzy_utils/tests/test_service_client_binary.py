"""
Unit tests for ServiceClient binary upload functionality.
Tests the new post_binary method and data parameter support.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import os

from blitzy_utils.service_client import ServiceClient, ServiceConfig


class TestServiceClientBinarySupport:
    """Test ServiceClient binary upload functionality."""
    
    def setup_method(self):
        """Setup test environment."""
        self.test_env = {
            'SERVICE_URL_ADMIN': 'https://admin.example.com',
            'SERVICE_URL_TEST': 'https://test.example.com'
        }
    
    @patch.dict(os.environ, {'SERVICE_URL_ADMIN': 'https://admin.example.com'})
    @patch('blitzy_utils.service_client.default')
    def test_service_client_initialization(self, mock_default):
        """Test ServiceClient initializes correctly with binary support."""
        mock_default.return_value = (Mock(), 'test-project')
        
        client = ServiceClient()
        
        assert 'admin' in client._services
        assert client._services['admin'].url == 'https://admin.example.com'
    
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', True)
    @patch('blitzy_utils.service_client.httpx')
    @patch('blitzy_utils.service_client.default')
    def test_ensure_client_httpx(self, mock_default, mock_httpx):
        """Test client creation with httpx."""
        mock_default.return_value = (Mock(), 'test-project')
        mock_client = Mock()
        mock_httpx.Client.return_value = mock_client
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._ensure_client()
        
        assert client._client == mock_client
        mock_httpx.Client.assert_called_once()
    
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', False)
    @patch('blitzy_utils.service_client.requests')
    @patch('blitzy_utils.service_client.default')
    def test_ensure_client_requests_fallback(self, mock_default, mock_requests):
        """Test client creation falls back to requests when httpx unavailable."""
        mock_default.return_value = (Mock(), 'test-project')
        mock_session = Mock()
        mock_requests.Session.return_value = mock_session
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._ensure_client()
        
        assert client._client == mock_session
        mock_requests.Session.assert_called_once()
    
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', True)
    @patch('blitzy_utils.service_client.httpx')
    @patch('blitzy_utils.service_client.default')
    def test_ensure_async_client_httpx(self, mock_default, mock_httpx):
        """Test async client creation with httpx."""
        mock_default.return_value = (Mock(), 'test-project')
        mock_async_client = Mock()
        mock_httpx.AsyncClient.return_value = mock_async_client
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._ensure_async_client()
        
        assert client._async_client == mock_async_client
        mock_httpx.AsyncClient.assert_called_once()
    
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', False)
    @patch('blitzy_utils.service_client.default')
    def test_ensure_async_client_no_httpx_raises(self, mock_default):
        """Test async client creation raises when httpx unavailable."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            
            with pytest.raises(ImportError, match="httpx is required"):
                client._ensure_async_client()
    
    @patch('blitzy_utils.service_client.default')
    def test_make_request_with_data_parameter(self, mock_default):
        """Test _make_request method supports data parameter."""
        mock_default.return_value = (Mock(), 'test-project')
        mock_client = Mock()
        mock_response = Mock()
        mock_client.request.return_value = mock_response
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._client = mock_client
            client.get_auth_token = Mock(return_value='test-token')
            
            # Test with binary data
            binary_data = b"test binary data"
            result = client._make_request(
                method="POST",
                service_name="admin",
                endpoint="/upload",
                data=binary_data,
                params={'key': 'value'},
                headers={'Content-Type': 'application/octet-stream'}
            )
        
        assert result == mock_response
        mock_client.request.assert_called_once()
        call_args = mock_client.request.call_args
        
        # Verify data parameter was passed
        assert call_args[1]['data'] == binary_data
        assert call_args[1]['params'] == {'key': 'value'}
        assert 'Authorization' in call_args[1]['headers']
        assert call_args[1]['headers']['Authorization'] == 'Bearer test-token'
    
    @patch('blitzy_utils.service_client.default')
    def test_post_binary_method(self, mock_default):
        """Test post_binary method calls _make_request correctly."""
        mock_default.return_value = (Mock(), 'test-project')
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._make_request = Mock(return_value=Mock())
            
            binary_data = b"test file content"
            params = {'file_path': 'test/file.txt', 'company_id': 'test'}
            headers = {'Content-Type': 'application/octet-stream'}
            
            result = client.post_binary(
                service_name="admin",
                endpoint="/v1/storage/upload",
                data=binary_data,
                params=params,
                headers=headers,
                timeout=600
            )
        
        client._make_request.assert_called_once_with(
            "POST", "admin", "/v1/storage/upload",
            data=binary_data, params=params, headers=headers, timeout=600
        )
    
    @patch('blitzy_utils.service_client.default')
    def test_context_manager_support(self, mock_default):
        """Test ServiceClient works as context manager."""
        mock_default.return_value = (Mock(), 'test-project')
        mock_client = Mock()
        
        with patch.dict(os.environ, self.test_env):
            with ServiceClient() as client:
                client._client = mock_client
                assert client is not None
        
        # Verify cleanup was called
        mock_client.close.assert_called_once()
    
    @patch('blitzy_utils.service_client.default')
    def test_auth_token_generation(self, mock_default):
        """Test authentication token generation."""
        mock_credentials = Mock()
        mock_default.return_value = (mock_credentials, 'test-project')
        
        with patch('blitzy_utils.service_client.id_token') as mock_id_token:
            mock_id_token.fetch_id_token.return_value = 'generated-token'
            
            with patch.dict(os.environ, self.test_env):
                client = ServiceClient()
                token = client.get_auth_token('https://admin.example.com')
            
            assert token == 'generated-token'
            mock_id_token.fetch_id_token.assert_called_once()
    
    @patch('blitzy_utils.service_client.default')
    def test_correlation_id_header(self, mock_default):
        """Test correlation ID is added to headers when available."""
        mock_default.return_value = (Mock(), 'test-project')
        mock_client = Mock()
        
        # Mock Flask context with correlation ID
        mock_g = Mock()
        mock_g.correlation_id = 'test-correlation-id'
        
        with patch.dict(os.environ, self.test_env):
            with patch('blitzy_utils.service_client.g', mock_g):
                client = ServiceClient()
                client._client = mock_client
                client.get_auth_token = Mock(return_value='test-token')
                
                client._make_request("GET", "admin", "/test")
        
        call_args = mock_client.request.call_args
        headers = call_args[1]['headers']
        assert headers['X-Correlation-ID'] == 'test-correlation-id'
    
    @patch('blitzy_utils.service_client.default')
    def test_service_config_retry_settings(self, mock_default):
        """Test ServiceConfig uses correct retry settings."""
        mock_default.return_value = (Mock(), 'test-project')
        
        config = ServiceConfig(url="https://test.com")
        
        # Should use constants from consts.py
        from blitzy_utils.consts import DEFAULT_MAX_RETRIES_SERVICES
        assert config.max_retries == DEFAULT_MAX_RETRIES_SERVICES
        assert config.timeout == 30
        assert config.retry_status_codes == (408, 429, 500, 502, 503, 504)
    
    @patch('blitzy_utils.service_client.default')
    def test_error_handling_in_make_request(self, mock_default):
        """Test error handling in _make_request method."""
        mock_default.return_value = (Mock(), 'test-project')
        mock_client = Mock()
        mock_client.request.side_effect = Exception("Network error")
        
        with patch.dict(os.environ, self.test_env):
            client = ServiceClient()
            client._client = mock_client
            client.get_auth_token = Mock(return_value='test-token')
            
            with pytest.raises(Exception, match="Network error"):
                client._make_request("GET", "admin", "/test")


class TestServiceClientIntegration:
    """Integration tests for ServiceClient with real-like scenarios."""
    
    @patch('blitzy_utils.service_client.default')
    @patch('blitzy_utils.service_client.HTTPX_AVAILABLE', True)
    @patch('blitzy_utils.service_client.httpx')
    def test_binary_upload_workflow(self, mock_httpx, mock_default):
        """Test complete binary upload workflow."""
        # Setup mocks
        mock_default.return_value = (Mock(), 'test-project')
        mock_client = Mock()
        mock_response = Mock()
        mock_response.json.return_value = {'filePath': 'gs://bucket/file.txt'}
        mock_client.request.return_value = mock_response
        mock_httpx.Client.return_value = mock_client
        
        # Test data
        binary_data = b"test file content for upload"
        
        with patch.dict(os.environ, {'SERVICE_URL_ADMIN': 'https://admin.example.com'}):
            with ServiceClient() as client:
                client.get_auth_token = Mock(return_value='test-token')
                
                response = client.post_binary(
                    service_name="admin",
                    endpoint="/v1/storage/upload",
                    data=binary_data,
                    params={
                        'file_path': 'company_test/attachments_proj/file.txt',
                        'company_id': 'test'
                    },
                    headers={'Content-Type': 'application/octet-stream'},
                    timeout=600
                )
        
        # Verify the request was made correctly
        mock_client.request.assert_called_once()
        call_args = mock_client.request.call_args
        
        assert call_args[1]['method'] == 'POST'
        assert call_args[1]['url'] == 'https://admin.example.com/v1/storage/upload'
        assert call_args[1]['data'] == binary_data
        assert call_args[1]['params']['file_path'] == 'company_test/attachments_proj/file.txt'
        assert call_args[1]['headers']['Content-Type'] == 'application/octet-stream'
        assert call_args[1]['headers']['Authorization'] == 'Bearer test-token'
        assert call_args[1]['timeout'] == 600
