# Attachments upload design

# Overall Design Considerations:

The design document outlines detailed considerations for attachment management, covering attachment support, storage strategy, and API requirements (CRUD operations). The meeting notes largely align with these areas, providing further clarification and decisions made during the discussion.

**Key Points from the Meeting Relevant to the Design:**

1. **File Types and Size Limits (Section 1 in Design):**  
   * The meeting confirms support for PDF, TXT, JPEG,WebP, and PNG based on Anthropic's documentation.  
   * Specific limits were set: 10 files and a total of 10 MB. This is a crucial detail to include in the design document if not already present.  
   * Frontend filtering for unsupported file types and sizes was discussed and agreed upon to improve user experience.  
2. **Storage Strategy (Section 2 in Design):**  
   * Attachments will be stored in GCS.  
   * Links to the GCS files will be stored in a database. This hybrid approach was discussed, with the understanding that the platform primarily relies on GCS.  
   * Attachments should be classified and stored by project ID, and potentially a second level like \`tech spec ID\`, within the GCS bucket for better organization and reconciliation.  
3. **API Requirements (Section 3 in Design):**  
   * A comprehensive API supporting CRUD operations is essential.  
   * **Create (Upload):** The upload process should ideally be synchronous, especially given the 10 MB total limit, with a progress indicator for larger uploads.  
   * **Read (Retrieve/Download):** An API is needed to retrieve attachment metadata (link, type, user description) and the prompt associated with a project and specific ID in a JSON format for the platform. An API to retrieve the prompt for display in the Wizard feature was also discussed.  
   * **Update (Rename/Replace):** While not explicitly detailed in the meeting, the design's inclusion of this is logical for a complete API.  
   * **Delete:** Removing attachments from the bucket and updating the database was agreed upon, especially in the context of draft mode.  
4. **Draft Mode and Backwards Compatibility:**  
   * The concept of a draft mode for prompts and attachments was introduced, allowing users to add and remove attachments before final submission. This requires micro-calls for adding and removing attachments.  
   * Backwards compatibility for existing projects without attachments is a critical requirement. The system should fall back to the old path if the new structure is not found, copy the old prompt over, and resolve for existing projects.  
5. **Permissions and Access Control:**  
   * A single service account will be used with an admin service handling uploads to GCS. Fine-grained file permissions are not required; access is managed internally through the service.  
6. **UI and Preview:**  
   * The UI is already implemented, including previews.  
   * For draft mode or when returning to a previously saved prompt, previews may not be shown directly from the backend. The UI should indicate attached files, potentially with default icons, rather than relying on backend preview generation.  
7. **Database Changes:**  
   * A new database table is likely needed to store attachment metadata and links. This is considered a significant change.

**Recommendations for the Design Document:**

* Explicitly include the agreed-upon file types (PDF, TXT, JPEG, PNG) and the limits (10 files, 10 MB total).  
* Detail the hybrid storage strategy (GCS for files, database for links) and the proposed GCS file path structure (by project ID and a second level ID).  
* Clarify the synchronous upload approach and the need for progress indication.  
* Add sections on draft mode and backwards compatibility, outlining the requirements discussed in the meeting.  
* Include details on the permission control using the service account and admin service.  
* Refine the UI section to reflect the decisions about previews in draft mode and when restoring prompts.  
* Note the requirement for a new database table.

# Detailed API Specifications

Figma: [Figma](https://www.figma.com/design/2qR7NSTmQLynkmlj9B4ltc/Blitzy-Platform?node-id=17321-50315&p=f&m=dev)  
This section expands on the API requirements, detailing each operation, its parameters, and the expected data flow.

Authentication & Authorization

Use existing patterns:

* @get\_user\_info decorator  
* get\_project\_by\_user\_id() for authorization  
* Company-specific bucket access via get\_company\_bucket\_name()

## 1.1 Data Schemas

### 1.1.1 Attachment Metadata Schema

```mermaid  
classDiagram
    class AttachmentMetadata {
        +string attachmentId
        +string projectId
        +string techSpecId
        +string fileName
        +string mimeType
        +long fileSize
        +string gcsPath
        +string uploadedByUserId
        +timestamp uploadTimestamp
        +string userDescription
        +string status
        +string version
    }
```

**Description:** This schema defines the structure for storing metadata about each attachment in the database.

| Field | Type | Description |
| :---- | :---- | :---- |
| `attachmentId` | `string` | Unique identifier for the attachment. |
| `projectId` | `string` | ID of the project the attachment belongs to. |
| `techSpecId` | `string` | ID of the specific entity within the project (e.g., tech spec). |
| `fileName` | `string` | Original name of the uploaded file. |
| `mimeType` | `string` | MIME type of the file (e.g., `application/pdf`, `image/jpeg`). |
| `fileSize` | `long` | Size of the file in bytes. |
| `gcsPath` | `string` | Full path to the file in Google Cloud Storage. |
| `uploadedByUserId` | `string` | ID of the user who uploaded the attachment. |
| `uploadTimestamp` | `timestamp` | Timestamp when the attachment was uploaded. |
| `userDescription` | `string` | Optional user-provided description for the attachment. |
| `status` | `string` | Current status of the attachment (e.g., pending scan, safe, infected). |
| `version` | `string` | Version of the attachment (if versioning is implemented). |

## 1.2 API Endpoints and Data Flow

### 1.2.1 Create (Upload) Attachment

**Endpoint:** `POST /v1/project/<projectId>/attachments/upload`

**Description:** Allows users to upload a new attachment. This operation will be synchronous for small files but with progress indicators for larger ones.

**Request:**

* **Headers:**  
  * `Content-Type: multipart/form-data`  
  * `Authorization: Bearer <token>`  
* **Path Parameters:**  
  * `projectId`: ID of the project.  
* **Body (Multipart Form Data):**  
  * `file`: The actual file content.  
  * `techSpecId`: ID of the entity (e.g., tech spec) to associate with, optional.  
  * `userDescription` (optional): Description provided by the user.

**Response (Success \- HTTP 201 Created):**

```
{
  "attachmentId": "uuid-of-new-attachment",
  "fileName": "document.pdf",
  "fileSize": 123456,
  "uploadTimestamp": "2025-07-28T10:00:00Z",
  "status": "uploaded"
}
```

**Data Flow:**

```mermaid  
sequenceDiagram  
    participant User  
    participant Frontend  
    participant Backend  
    participant Admin Service  
    participant GCS  
    participant Database

    User->>Frontend: Select file for upload  
    Frontend->>Backend: POST /v1/project/<projectId>/attachments/upload (multipart form data)  
    Backend->>Admin Service: Authenticate & Authorize request  
    Admin Service->>Admin Service: Validate file type, size, etc.  
    Admin Service->>GCS: Upload file content  
    GCS-->>Admin Service: Returns GCS file path  
    Admin Service->>Database: Store AttachmentMetadata (including GCS path)  
    Database-->>Admin Service: Confirmation  
    Admin Service-->>Backend: Success response with metadata  
    Backend-->>Frontend: Success response  
    Frontend->>User: Display upload success / progress  
```

### 1.2.2 Read (Retrieve) Attachment Metadata

Endpoint: POST /v1/project/\<projectId\>/attachments/metadata

Description: Retrieves metadata for all attachments associated with a specific project and optionally an entity.

Request:

Path Parameters:

projectId: ID of the project.

Headers:

Authorization: Bearer \<token\>

Body (JSON):

* `techSpecId`: ID of the entity (e.g., tech spec) to associate with, optional.

Response (Success \- HTTP 200 OK):

```json
[
  {
    "attachmentId": "uuid-1",
    "fileName": "project_plan.pdf",
    "mimeType": "application/pdf",
    "fileSize": 500000,
    "uploadTimestamp": "T14:30:00Z",
    "userDescription": "Initial project plan",
    "status": "safe"
  },
  {
    "attachmentId": "uuid-2",
    "fileName": "design_mockup.jpeg",
    "mimeType": "image/jpeg",
    "fileSize": 150000,
    "uploadTimestamp": "T16:00:00Z",
    "userDescription": "UI design concept",
    "status": "safe"
  }
]
```

**Data Flow:**


```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Admin Service
    participant Database

    User->>Frontend: Request attachments for Project X, Entity Y
    Frontend->>Backend: POST /v1/project/<projectId>/attachments/metadata
    Backend->>Admin Service: Authenticate & Authorize request
    Admin Service->>Database: Query AttachmentMetadata by projectId and techSpecId
    Database-->>Admin Service: Returns list of metadata
    Admin Service-->>Backend: List of attachment metadata
    Backend-->>Frontend: List of attachment metadata
    Frontend->>User: Display attachment list (e.g., filename, size, description)
```




### 1.2.3 Read (Download) Attachment Content

**Endpoint:** `POST /v1/project/<projectId>/attachments/download/{attachmentId}`

**Description:** Allows users to download the actual content of an attachment.

**Request:**

* **Path Parameters:**  
  * `attachmentId`: ID of the attachment to download.  
* **Headers:**  
  * `Authorization: Bearer <token>`  
* **Path Parameters:**  
  * `projectId`: ID of the project.  
* **`Body`**`:`  
  * `techSpecId`: ID of the entity (e.g., tech spec) to associate with, optional.

**Response (Success \- HTTP 200 OK):**

* **Headers:**  
  * `Content-Type`: MIME type of the file.  
  * `Content-Disposition`: `attachment; filename="[original_filename]"`  
* **Body:** Raw file content.

**Data Flow:**

```mermaid  
sequenceDiagram  
    participant User  
    participant Frontend  
    participant Backend  
    participant Admin Service  
    participant Database  
    participant GCS

    User->>Frontend: Click "Download" on attachment  
    Frontend->>Backend: POST /v1/project/<projectId>/attachments/download/{attachmentId}  
    Backend->>Admin Service: Authenticate & Authorize request  
    Admin Service->>Database: Retrieve GCS path for attachmentId  
    Database-->>Admin Service: Returns GCS path  
    Admin Service->>GCS: Request file content from GCS path  
    GCS-->>Admin Service: Streams file content  
    Admin Service-->>Backend: Streams file content  
    Backend-->>Frontend: Streams file content  
    Frontend->>User: Initiates file download  
```

### 



### 

### 1.2.4 Update (Rename) Attachment

**Endpoint:** `PUT /v1/project/<projectId>/attachments/{attachmentId}/rename`

**Description:** Allows renaming an existing attachment.

**Request:**

* **Path Parameters:**  
  * `projectId`: ID of the project.  
  * `attachmentId`: ID of the attachment to rename.  
* **Headers:**  
  * `Content-Type: application/json`  
  * `Authorization: Bearer <token>`  
* **Body:**

```json
{
  "newFileName": "updated_document_name.pdf",
  "techSpecId": "wiweoi8239_28wea89"// ID of the entity (e.g., tech spec) to associate with, optional.
}
```

**Response (Success \- HTTP 200 OK):**

```json
{
  "attachmentId": "uuid-of-attachment",
  "fileName": "updated_document_name.pdf",
  "status": "renamed"
}
```

**Data Flow:**

```mermaid  
sequenceDiagram  
participant User  
participant Frontend  
participant Backend  
participant Admin Service  
participant Database

User->>Frontend: Request to rename attachment  
Frontend->>Backend: PUT /v1/project/<projectId>/attachments/uuid-of-attachment/rename (newFileName)  
Backend->>Admin Service: Authenticate & Authorize request  
Admin Service->>Database: Update fileName for attachmentId  
Database-->>Admin Service: Confirmation  
Admin Service-->>Backend: Success response with updated metadata  
Backend-->>Frontend: Success response  
Frontend->>User: Display updated filename  
```  


### 1.2.5 Delete Attachment

**Endpoint:** DELETE /v1/project/\<projectId\>/attachments/{attachmentId}

**Description:** Allows deleting an existing attachment.

**Request:**

* **Path Parameters:**  
  * `projectId`: ID of the project.  
  * attachmentId: ID of the attachment to delete.  
* **Headers:**  
  * Authorization: Bearer \<token\>

**Response (Success \- HTTP 204 No Content):**

(No content is returned for a successful deletion)

**Data Flow:**

```mermaid  
sequenceDiagram  
participant User  
participant Frontend  
participant Backend  
participant Admin Service  
participant Database  
participant GCS

User->>Frontend: Click "Delete" on attachment (with confirmation)  
Frontend->>Backend: DELETE /v1/project/<projectId>/attachments/{attachmentId}  
Backend->>Admin Service: Authenticate & Authorize request  
Admin Service->>Database: Retrieve GCS path for attachmentId  
Database-->>Admin Service: Returns GCS path  
Admin Service->>GCS: Delete file content from GCS path  
GCS-->>Admin Service: Confirmation  
Admin Service->>Database: Delete AttachmentMetadata for attachmentId  
Database-->>Admin Service: Confirmation  
Admin Service-->>Backend: Success response  
Backend-->>Frontend: Success response (HTTP 204)  
Frontend->>User: Update UI, remove deleted attachment  
```

### 1.2.6 Retrieve Prompts with Attachments

Endpoint: POST /v1/project/{projectId}/prompts

Description: Retrieves a specific prompt along with its associated attachments, designed to integrate with the Wizard feature for user display and download.

Request:

Path Parameters:

projectId: ID of the project the prompt belongs to.

Headers:

Authorization: Bearer \<token\>

Body:

techSpecId (optional): ID of the specific entity (e.g., tech spec) the prompt is for.

**Response (Success \- HTTP 200 OK):**

```json
{
  "promptLink": "/bucket/path", // path to a signed google cloud storage file link
  "attachments": [
    {
      "attachmentId": "uuid-1",
      "fileName": "project_plan.pdf",
      "mimeType": "application/pdf",
      "fileSize": 500000,
      "userDescription": "Initial project plan",
      "downloadLink": "/v1/project/<projectId>/attachments/download/uuid-1"
    },
    {
      "attachmentId": "uuid-2",
      "fileName": "design_mockup.jpeg",
      "mimeType": "image/jpeg",
      "fileSize": 150000,
      "userDescription": "UI design concept",
      "downloadLink": "/v1/project/<projectId>/attachments/download/uuid-2"
    }
  ]
}
```

**Data Flow:**

```mermaid  
sequenceDiagram  
participant User  
participant Frontend  
participant Backend  
participant Admin Service  
participant Database

User->>Frontend: Request to view prompt for Project X, CodeSpecId Y  
Frontend->>Backend: POST /v1/prompts/{projectId}  
Backend->>Admin Service: Authenticate & Authorize request  
Admin Service->>Database: Query prompt content by projectId and techSpecId  
Database-->>Admin Service: Returns prompt content  
Admin Service->>Database: Query AttachmentMetadata by projectId and techSpecId  
Database-->>Admin Service: Returns list of attachment metadata  
Admin Service-->>Backend: Combined response (prompt + attachment metadata with download links)  
Backend-->>Frontend: Combined response  
Frontend->>User: Display prompt and list of attachments with download options  
```

**Batch Operations for Attachments**

The current API design focuses on single attachment operations (upload, retrieve metadata, download, rename, delete). For scenarios requiring efficiency and reduced API calls, especially in user interfaces that allow multiple selections or drag-and-drop of numerous files, batch operations should be considered.

Here's how multi-attachment operations could be implemented:

### 1.2.7 Create (Upload) Multiple Attachments

**Endpoint:** POST /v1/project/\<projectId\>/attachments/batch-upload

**Description:** Allows users to upload multiple attachments in a single request. This is particularly useful for user interfaces supporting drag-and-drop or multi-file selection, improving efficiency by reducing the number of individual API calls.

**Request:**

* **Headers:**  
  * Content-Type: multipart/form-data  
  * Authorization: Bearer \<token\>  
* **Path Parameters:**  
  * **projectId:** ID of the project to which the attachments belong.  
* **Body (Multipart Form Data):**  
  * files: An array of file contents. Each file should be included with a distinct field name (e.g., file, file, or if the client supports it, multiple fields named file).  
  * techSpecId (optional): ID of the specific  tech spec, to associate these attachments with. This helps in organizing files within the GCS bucket.  
  * userDescriptions (optional): An array of strings, where each string is an optional user-provided description corresponding to the order of files in the files array.

**Response (Success \- HTTP 201 Created):**

```json
[
{
"attachmentId": "uuid-of-new-attachment-1",
"fileName": "document1.pdf",
"fileSize": 123456,
"uploadTimestamp": "T10:01:00Z",
"status": "uploaded",
"gcsPath": "gs://your-company-bucket/documents/uuid-of-new-attachment-1.pdf"
},
{
"attachmentId": "uuid-of-new-attachment-2",
"fileName": "image.jpeg",
"fileSize": 78900,
"uploadTimestamp": "T10:01:05Z",
"status": "uploaded",
"gcsPath": "gs://your-company-bucket/documents/uuid-of-new-attachment-2.jpeg"
}
] 
```

 \* A list of AttachmentMetadata objects is returned for each successfully uploaded file.

* In case of partial success, the response would include details for successfully uploaded files, and an appropriate HTTP status code (e.g., 207 Multi-Status) or a detailed error object would be returned for failed uploads within the batch.

**Data Flow:**

```mermaid
  sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Admin Service
    participant GCS
    participant Database
    User->>Frontend: Select multiple files for upload (e.g., drag-and-drop)
    Frontend->>Backend: POST /v1/project/<projectId>/attachments/batch-upload (multipart form data with multiple files)

    Backend->>Admin Service: Authenticate & Authorize batch request
    activate Admin Service

    loop For each file in the batch (concurrently)
    Admin Service->>Admin Service: Validate file type, size, etc.
    alt File validation successful
    Admin Service->>GCS: Upload file content
    GCS-->>Admin Service: Returns GCS file path
    Admin Service->>Database: Store AttachmentMetadata for individual file (including GCS path)
    Database-->>Admin Service: Confirmation
    else File validation or upload failed
    Admin Service->>Admin Service: Log individual file error
    Admin Service-->>Backend: Report individual file failure status
    end
    end

    Admin Service-->>Backend: Aggregate success/failure response for all files
    deactivate Admin Service

    Backend-->>Frontend: Success response with metadata for uploaded files (or partial success/error)
    Frontend->>User: Display upload success / progress / errors for each file
```



**Considerations for Batch Operations (Specific to Upload):**

**Performance:**

* **Reduced Network Overhead:** Consolidating multiple file uploads into a single HTTP request significantly reduces the number of round trips between the client and server, leading to faster overall upload times, especially for many small files.  
* **Backend Optimization (Concurrency):** The backend *must* optimize resource allocation for processing multiple files concurrently (e.g., using thread pools or asynchronous processing) within the same request context to maximize throughput.  
* **Stream Processing:** Backend should be designed to handle large multipart/form-data requests efficiently, potentially by streaming file contents directly to GCS without buffering the entire request in memory.  
  **Error Handling (Partial Success & User Feedback):**  
* **Partial Success:** It's crucial to define how to handle scenarios where some files in the batch succeed while others fail (e.g., due to file size limits, invalid types, or network issues for individual files). The recommended approach is to return a 201 Created or 200 OK status with a response body that details the status of each individual upload (e.g., an array of results, each indicating success or specific error for a file). A 207 Multi-Status could also be considered if broader WebDAV semantics are followed.  
* **Transaction Management:** While individual file uploads to GCS and database entries might be atomic, the entire batch operation is typically *not* atomic. If one file fails, it should not roll back the successful uploads of other files in the same batch. Each file should be processed independently, with its own success/failure recorded.  
* **Clear Error Messages:** The API response should provide clear, actionable error messages for each failed file, detailing the reason for failure (e.g., "File 'x.doc' not supported," "File 'y.pdf' exceeds 10MB limit").  
  **Scalability:**  
* The batch upload endpoint should be designed to scale horizontally to handle a high volume of concurrent requests, leveraging cloud services like Google Cloud Storage which are inherently scalable.  
* Consider maximum batch size (number of files or total size) to prevent individual requests from becoming too large and timing out.  
  **Security:**  
* All individual file validations (type, size, virus scanning) must still apply to each file within a batch upload.  
* Implement robust rate limiting on this endpoint to prevent abuse, as a single batch request can consume significant resources.  
* Ensure proper authentication and authorization checks are performed for each file's association with the projectId and techSpecId.  
  **User Experience (Frontend Interaction):**  
* The frontend should provide clear progress indicators for batch uploads, ideally showing the progress of the entire batch and potentially individual files.  
* Upon completion, the UI should clearly indicate which files were uploaded successfully and which failed, along with reasons for failure.  
* Allow users to retry failed uploads.

### 1.2.8 Delete Multiple Attachments

**Endpoint:**   
POST /v1/project/\<projectId\>/attachments/batch-delete (or DELETE with a request body, though POST is often preferred for batch deletes to include a body)

**Description:** Allows deleting multiple existing attachments in a single request.

**Request:**

* **Headers:**  
  * Content-Type: application/json  
  * Authorization: Bearer \<token\>  
* **Path Parameters:**  
  * `projectId`: ID of the project.  
* **Body:**

```json
{
  "attachmentIds": [
    "uuid-of-attachment-1",
    "uuid-of-attachment-2",
    "uuid-of-attachment-3"
  ],
"techSpecId": "wiweoi8239_28wea89"// ID of the entity (e.g., tech spec) to associate with, optional.
}
```

**Response (Success \- HTTP 204 No Content):**

(No content is returned for a successful deletion, but a 200 OK with a status object could be returned for partial success/failure scenarios if needed.)

**Data Flow:** The Admin Service would receive the list of IDs, retrieve their GCS paths from the database, initiate deletion of multiple files from GCS, and then delete the corresponding metadata entries from the database. Error handling would be crucial to manage cases where some deletions succeed and others fail.

## Considerations for Batch Operations:

* **Atomicity:** Decide if batch operations should be atomic (all succeed or all fail) or if partial success is acceptable. For uploads, partial success might be acceptable (e.g., if one file is infected, others can still upload). For deletes, atomicity might be preferred.  
* **Error Reporting:** For partial successes, the API response would need to include details about which specific operations failed and why.  
* **Performance:** Batch operations inherently improve performance by reducing network overhead and potentially allowing for more efficient database/storage operations.  
* **Transaction Management:** Proper transaction management in the backend is essential to ensure data consistency, especially when interacting with both the database and GCS.  
* **Rate Limiting:** Implement robust rate limiting to prevent abuse, particularly for batch operations that can consume more resources.

While not explicitly detailed in the initial API design, the inclusion of batch operations for common actions like upload and delete would significantly enhance the system's usability and performance for workflows involving multiple attachments.

# Detailed Considerations for Attachment Management

When implementing an attachment management system, several key areas require careful consideration to ensure a robust, secure, and user-friendly experience.

**1\. Attachment Support and Frontend Filtering:**

* **File Types:** Define the specific file types (e.g., images like JPG, PNG; documents like PDF, DOCX; spreadsheets like XLSX; presentations like PPTX; audio like MP3; video like MP4) that the system will support. This should be based on user needs and potential security implications.  
* **File Size Limits:** Establish clear maximum file size limits for individual attachments and potentially for the total size of attachments within a given context (e.g., per email, per record). This impacts storage, upload/download performance, and network bandwidth.  
* **Number of Attachments:** Determine if there are limits on the number of attachments per item or record. This can prevent abuse and manage storage requirements.  
* **Frontend Filtering:** Implement client-side validation and filtering to provide immediate feedback to users about unsupported file types or sizes *before* an upload attempt is made. This improves the user experience by preventing unnecessary server requests and failed uploads.

**2\. Storage Strategy:**

* **Storage Location:** Decide where attachments will be stored. Options include:  
  * **Local Storage:** On the application server itself (not recommended for scalability or resilience).  
  * **Dedicated File Storage Services:** Cloud-based solutions Google Cloud Storage. These offer scalability, durability, and often built-in security features.  
* **Data Redundancy and Backup:** Ensure the chosen storage solution provides adequate data redundancy and robust backup mechanisms to prevent data loss.  
* **Cost Implications:** Evaluate the cost of storage based on anticipated volume, access patterns, and desired performance.

**3\. API Requirements (CRUD Operations):**

A comprehensive API is essential for managing attachments programmatically. This API should support the following core operations:

* **Create (Upload):**  
  * Endpoint for uploading new attachments, handling various file types and sizes.  
  * Parameters for associating attachments with specific entities (e.g., an email, a document, a user profile).  
  * Support for multipart form data for file uploads.  
* **Read (Retrieve/Download):**  
  * Endpoints for retrieving attachment metadata (e.g., filename, size, type, upload date).  
  * Endpoints for downloading the actual attachment content.  
  * Consider different download options (e.g., direct download, streaming).  
* **Update (Rename/Replace):**  
  * Endpoint for renaming an existing attachment.  
  * Endpoint for replacing an existing attachment with a new version (if versioning is not explicitly handled by the version management system).  
* **Delete:**  
  * Endpoint for permanently deleting attachments.  
  * Consider soft deletion options (marking as deleted but retaining for a period) versus hard deletion.  
  * Implement appropriate authorization checks before allowing deletion.

**4\. Asynchronous vs. Synchronous Processing:**

* **Uploads:**  
  * **Asynchronous:** For large files or during periods of high traffic, asynchronous uploads are preferable. This involves offloading the actual file processing (e.g., virus scanning, transcoding, thumbnail generation) to a background task queue. The user receives immediate confirmation of upload initiation, and a notification can be sent upon completion. This prevents UI blocking and improves responsiveness.  
  * **Synchronous:** For very small files where immediate processing is required, synchronous uploads might be acceptable, but they can block the user interface.  
* **Notifications:** Implement a robust notification system to inform users about the status of their asynchronous tasks (e.g., "Upload complete," "Upload failed," "File scanned and safe"). This can be via UI alerts, email, or in-app notifications.

**5\. UI Display of Attachments:**

The user interface should provide intuitive ways to view and interact with attachments:

* **Thumbnails:** For image and video attachments, display small preview thumbnails to provide a quick visual representation without requiring a full download.  
* **List View:** A clear list of all attachments, showing key metadata like filename, size, and upload date.  
* **Preview Functionality:**  
  * **In-browser Preview:** For common document types (PDF, common image formats), allow in-browser preview to avoid requiring users to download and open files with external applications.  
  * **Integrated Viewers:** Utilize libraries or services for rendering more complex document types (e.g., Word, Excel, PowerPoint) within the application.

**6\. User Actions with Attachments in the UI:**

Users should be able to perform common actions directly from the UI:

* **Download:** A clear download button for each attachment.  
* **Delete:** A prominent delete option, ideally with a confirmation prompt to prevent accidental deletions.  
* **Rename:** Functionality to change the filename of an attachment.  
* **View/Open:** Action to trigger the preview functionality or open the file in its default application.

**7\. Error Handling:**

Robust error handling is crucial for a reliable attachment system:

* **Upload Failures:**  
  * Clear and user-friendly error messages (e.g., "File too large," "Invalid file type," "Network error").  
  * Guidance on how to resolve the issue.  
  * Logging of errors for debugging and monitoring.  
* **Corrupted Files:** Implement checks (e.g., checksums) during upload and download to detect file corruption. Provide appropriate error messages and potentially options for re-uploading.  
* **Permission Denied:** Inform users clearly if they lack the necessary permissions to perform an action (e.g., download, delete).  
* **System Errors:** Graceful handling of backend errors, informing the user of a temporary issue and suggesting retrying.

**8\. Security Considerations:**

Security is paramount for attachment management:

* **Access Control (Authorization):** Implement granular role-based access control (RBAC) or attribute-based access control (ABAC) to ensure only authorized users can upload, download, delete, or view specific attachments. This includes:  
  * Who can upload to which entities.  
  * Who can view/download which attachments.  
  * Who can delete specific attachments.  
* **Virus Scanning:** Integrate with an anti-virus engine (e.g., ClamAV, commercial solutions) to scan all uploaded files for malware. This should be done asynchronously before files are made accessible.  
* **Malicious Content Detection:** Beyond traditional viruses, consider scanning for other malicious content, such as scripts embedded in documents or images.  
* **Data Encryption:**  
  * **In Transit:** Use HTTPS/SSL for all attachment uploads and downloads to encrypt data as it travels over the network.  
  * **At Rest:** Encrypt attachments at rest in the storage solution to protect them from unauthorized access even if the storage itself is compromised.  
* **Input Validation:** Strictly validate all input related to attachments (e.g., filenames, MIME types) to prevent injection attacks or directory traversal vulnerabilities.  
* **DDoS Protection:** Implement measures to protect against denial-of-service attacks targeting the attachment upload/download endpoints.

**9\. Attachment Version Management (if applicable):**

For collaborative environments or systems where documents evolve, version management is highly beneficial:

* **Automatic Versioning:** When an attachment is modified or replaced, automatically create a new version while retaining previous versions.  
* **Version History:** Provide users with a clear history of all versions, including who made changes and when.  
* **Revert to Previous Version:** Allow users to revert an attachment to an older version.  
* **Storage Implications:** Understand the increased storage requirements associated with storing multiple versions of the same file.  
* **Metadata for Versions:** Store relevant metadata for each version (e.g., version number, upload timestamp, uploader).

