
	•	Markdown is stored as text in Google Cloud Storage
	•	Backend (Python) handles all Markdown ⇄ AST conversion
	•	Frontend fetches AST JSON and renders directly, including TOC
	•	No markdown-to-string rendering needed on frontend

⸻

🧠 Overview

```mermaid
    graph TD
    subgraph Frontend [Frontend - Node/React]
        FE1[GET /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/ast]
        FE2[Parse TOC using mdast-util-toc]
        FE3[Render AST using rehype-react]
        FE4[User edits block]
        FE5[Patch AST or send entire AST]
        FE1 --> FE2
        FE1 --> FE3
        FE4 --> FE5
    end

    subgraph Backend [Backend - Python Flask]
        BE1[Markdown stored in GCS]
        BE2[GET /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/ast]
        BE3[Markdown → AST markdown-it-py]
        BE4[POST /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/ast]
        BE5[AST → Markdown]
        BE2 --> BE3
        BE4 --> BE5
        BE5 --> BE1
    end

    FE5 --> BE4
    BE2 --> FE1
```

⸻

📦 API Design

✅ 1. Get Markdown AST

GET /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/ast

Returns:

{
  "project_id": "abc123",
  "ast": [/* markdown AST nodes in MDAST format */]
}

✅ 2. Save Markdown AST

POST /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/ast
Content-Type: application/json

Body:

{
  "ast": [/* modified AST in MDAST format */]
}

Backend Action:
	•	Converts AST → Markdown
	•	Saves .md to Google Cloud Storage

⸻

✨ Optional Endpoints

1. Get Raw Markdown (for debugging or export)

GET /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/raw

4. Save Raw Markdown (admin/manual override)

POST /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/raw


⸻

✅ Frontend Responsibilities
	•	Render AST with rehype-react or MDX-compatible renderer
	•	Generate TOC using mdast-util-toc
	•	Use unist-util-visit to support:
	•	Lazy rendering of large blocks
	•	Block-level edit (insert, delete, update)
	•	Optionally patch or diff AST before POST

⸻

🗃 Example Data Flow

```mermaid
sequenceDiagram
    participant UI as Frontend UI
    participant API as Python API
    participant GCS as Google Cloud Storage

    UI->>API: Fetch AST (GET /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/ast)
    API->>GCS: Load markdown from storage
    API->>API: Convert markdown → AST
    API->>UI: Return AST JSON

    UI->>UI: Render AST + generate TOC

    UI->>API: Submit modified AST (POST /v1/projects/:project_id/tech_specs/:tech_spec_id/docs/ast)
    API->>API: Convert AST → markdown
    API->>GCS: Save updated markdown
```

⸻

🔒 Notes on Block Identification

Each AST node should include a stable id:

{
  "type": "heading",
  "depth": 2,
  "id": "project-overview",
  "children": [ { "type": "text", "value": "Project Overview" } ]
}

Use deterministic id (e.g., slugified heading text) or generate UUIDs for non-heading blocks.

⸻

🧪 Want an Example?

I can generate:
	•	✅ Sample AST payload
	•	✅ React frontend code to render AST with TOC
	•	✅ Python code to convert markdown ⇄ AST




⸻

✅ Recommended Node Libraries for Frontend

🔧 Core Tooling: Unified Ecosystem

Purpose	Library	Description
AST Format	MDAST	Markdown Abstract Syntax Tree
TOC Generator	mdast-util-toc	Extracts TOC from MDAST
AST Traversal	unist-util-visit	Visit and mutate AST nodes
AST Rendering	rehype-react	Render HAST → React
MDAST → HAST	remark-rehype	Convert Markdown AST (MDAST) to HTML AST (HAST)

🧠 These libraries are part of the unified ecosystem, which powers many MDX/Markdown apps.

⸻

🧩 Example Usage Stack

Install (with npm or pnpm)

npm install unified remark-rehype rehype-react mdast-util-toc unist-util-visit


⸻

🧠 Flow of Rendering

```mermaid
graph TD
  A[MDAST JSON from Backend]
  A -->|mdast-util-toc| B[Table of Contents]
  A -->|remark-rehype| C[HAST]
  C -->|rehype-react| D[React Components]
```

⸻

✨ Sample Usage

import { unified } from 'unified'
import remarkRehype from 'remark-rehype'
import rehypeReact from 'rehype-react'
import { toc } from 'mdast-util-toc'
import { visit } from 'unist-util-visit'
import { createElement } from 'react'

// Convert AST to TOC
export function extractTOC(mdast) {
  return toc(mdast, { maxDepth: 3 }).map
}

// Convert AST → React JSX
export function renderMarkdownAST(mdast) {
  return unified()
    .use(remarkRehype)
    .use(rehypeReact, { createElement })
    .run(mdast) // Optional: process async
    .then(file => unified().stringify(file))
}


⸻

🧱 Optional Tools

Purpose	Library
AST diffing	unist-diff (unofficial) or custom patching
Live block editor	MDX Editor (can adapt to MDAST)
Lazy rendering	Combine AST with react-window or react-virtualized


⸻

✅ Summary

To render Markdown AST with TOC on frontend, use:

✔ unified
✔ mdast-util-toc
✔ remark-rehype
✔ rehype-react
✔ unist-util-visit


===
in backend, we generate hierachy markdown AST with unique ids for frontend to locate.


pip install mistune python-slugify

```python
import sys
import json
from mistune import create_markdown
from mistune.renderers import AstRenderer
from slugify import slugify
from pathlib import Path


def assign_ids(ast, index_path=None):
    """ Recursively assign unique IDs to each AST node. """
    if index_path is None:
        index_path = []

    result = []

    for idx, node in enumerate(ast):
        node_id = generate_id(node, index_path + [idx])
        node['id'] = node_id

        # Recursively process children if any
        if 'children' in node and isinstance(node['children'], list):
            node['children'] = assign_ids(node['children'], index_path + [idx])

        result.append(node)

    return result


def generate_id(node, index_path):
    if node['type'] == 'heading':
        text = ''
        for child in node.get('children', []):
            if child['type'] == 'text':
                text += child['text']
        slug = slugify(text) or 'untitled'
        return f"heading--{slug}"
    else:
        pos = '-'.join(map(str, index_path))
        return f"node--{pos}"


def main():
    if len(sys.argv) != 3:
        print("Usage: python md_2_ast.py <markdown_file> <output_json_file>")
        sys.exit(1)

    input_path = Path(sys.argv[1])
    output_path = Path(sys.argv[2])

    if not input_path.exists():
        print(f"Input file not found: {input_path}")
        sys.exit(1)

    with input_path.open('r', encoding='utf-8') as f:
        markdown = f.read()

    md = create_markdown(renderer=AstRenderer())
    raw_ast = md(markdown)
    ast_with_ids = assign_ids(raw_ast)

    with output_path.open('w', encoding='utf-8') as f:
        json.dump(ast_with_ids, f, indent=2)

    print(f"✅ AST written to: {output_path}")


if __name__ == '__main__':
    main()

```