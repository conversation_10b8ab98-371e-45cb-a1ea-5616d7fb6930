"""
Unit tests for AttachmentMetadata model and related database operations.
Tests model validation, relationships, and database constraints.
"""
import pytest
from datetime import datetime, timezone
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError
import uuid

from common_models.models import (
    AttachmentMetadata, 
    AttachmentStatus, 
    Base,
    Project,
    User,
    TechnicalSpec
)


class TestAttachmentMetadataModel:
    """Test AttachmentMetadata model functionality."""
    
    @pytest.fixture
    def db_session(self):
        """Create in-memory SQLite database for testing."""
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(engine)
        Session = sessionmaker(bind=engine)
        session = Session()
        
        # Create test user
        test_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            name="Test User"
        )
        session.add(test_user)
        
        # Create test project
        test_project = Project(
            id=str(uuid.uuid4()),
            name="Test Project",
            description="Test project for attachments",
            created_by_user_id=test_user.id
        )
        session.add(test_project)
        
        # Create test technical spec
        test_spec = TechnicalSpec(
            id=str(uuid.uuid4()),
            project_id=test_project.id,
            title="Test Spec",
            content="Test technical specification"
        )
        session.add(test_spec)
        
        session.commit()
        
        yield session, test_user, test_project, test_spec
        session.close()
    
    def test_attachment_metadata_creation(self, db_session):
        """Test creating AttachmentMetadata with all required fields."""
        session, user, project, spec = db_session
        
        attachment = AttachmentMetadata(
            project_id=project.id,
            tech_spec_id=spec.id,
            file_name="test-document.pdf",
            mime_type="application/pdf",
            file_size=1024,
            gcs_path="gs://bucket/company_123/attachments_proj456/file789.pdf",
            uploaded_by_user_id=user.id,
            user_description="Test attachment for unit testing"
        )
        
        session.add(attachment)
        session.commit()
        
        # Verify the attachment was created
        assert attachment.id is not None
        assert attachment.project_id == project.id
        assert attachment.tech_spec_id == spec.id
        assert attachment.file_name == "test-document.pdf"
        assert attachment.mime_type == "application/pdf"
        assert attachment.file_size == 1024
        assert attachment.gcs_path == "gs://bucket/company_123/attachments_proj456/file789.pdf"
        assert attachment.uploaded_by_user_id == user.id
        assert attachment.user_description == "Test attachment for unit testing"
        assert attachment.status == AttachmentStatus.UPLOADED.value
        assert attachment.version == "1.0"
        assert attachment.upload_timestamp is not None
        assert not attachment.is_deleted  # From VisibilityMixin
    
    def test_attachment_metadata_without_tech_spec(self, db_session):
        """Test creating AttachmentMetadata without technical spec (nullable)."""
        session, user, project, _ = db_session
        
        attachment = AttachmentMetadata(
            project_id=project.id,
            tech_spec_id=None,  # This should be allowed
            file_name="general-file.txt",
            mime_type="text/plain",
            file_size=512,
            gcs_path="gs://bucket/company_123/attachments_proj456/general.txt",
            uploaded_by_user_id=user.id
        )
        
        session.add(attachment)
        session.commit()
        
        assert attachment.tech_spec_id is None
        assert attachment.technical_spec is None
    
    def test_attachment_metadata_required_fields(self, db_session):
        """Test that required fields cannot be null."""
        session, user, project, _ = db_session
        
        # Test missing project_id
        with pytest.raises(IntegrityError):
            attachment = AttachmentMetadata(
                project_id=None,  # Required field
                file_name="test.txt",
                mime_type="text/plain",
                file_size=100,
                gcs_path="gs://bucket/file.txt",
                uploaded_by_user_id=user.id
            )
            session.add(attachment)
            session.commit()
        
        session.rollback()
        
        # Test missing file_name
        with pytest.raises(IntegrityError):
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name=None,  # Required field
                mime_type="text/plain",
                file_size=100,
                gcs_path="gs://bucket/file.txt",
                uploaded_by_user_id=user.id
            )
            session.add(attachment)
            session.commit()
        
        session.rollback()
        
        # Test missing uploaded_by_user_id
        with pytest.raises(IntegrityError):
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name="test.txt",
                mime_type="text/plain",
                file_size=100,
                gcs_path="gs://bucket/file.txt",
                uploaded_by_user_id=None  # Required field
            )
            session.add(attachment)
            session.commit()
    
    def test_attachment_status_enum_values(self, db_session):
        """Test AttachmentStatus enum values."""
        session, user, project, _ = db_session
        
        # Test all valid status values
        valid_statuses = [
            AttachmentStatus.UPLOADED,
            AttachmentStatus.PENDING_SCAN,
            AttachmentStatus.SAFE,
            AttachmentStatus.INFECTED,
            AttachmentStatus.FAILED
        ]
        
        for status in valid_statuses:
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name=f"test-{status.value}.txt",
                mime_type="text/plain",
                file_size=100,
                gcs_path=f"gs://bucket/{status.value}.txt",
                uploaded_by_user_id=user.id,
                status=status.value
            )
            session.add(attachment)
        
        session.commit()
        
        # Verify all attachments were created with correct statuses
        attachments = session.query(AttachmentMetadata).all()
        assert len(attachments) == len(valid_statuses)
        
        status_values = [att.status for att in attachments]
        expected_values = [status.value for status in valid_statuses]
        assert set(status_values) == set(expected_values)
    
    def test_attachment_relationships(self, db_session):
        """Test AttachmentMetadata relationships with other models."""
        session, user, project, spec = db_session
        
        attachment = AttachmentMetadata(
            project_id=project.id,
            tech_spec_id=spec.id,
            file_name="relationship-test.pdf",
            mime_type="application/pdf",
            file_size=2048,
            gcs_path="gs://bucket/relationship-test.pdf",
            uploaded_by_user_id=user.id
        )
        
        session.add(attachment)
        session.commit()
        
        # Test project relationship
        assert attachment.project is not None
        assert attachment.project.id == project.id
        assert attachment.project.name == "Test Project"
        
        # Test technical spec relationship
        assert attachment.technical_spec is not None
        assert attachment.technical_spec.id == spec.id
        assert attachment.technical_spec.title == "Test Spec"
        
        # Test user relationship
        assert attachment.uploaded_by_user is not None
        assert attachment.uploaded_by_user.id == user.id
        assert attachment.uploaded_by_user.email == "<EMAIL>"
        
        # Test reverse relationships
        assert attachment in project.attachments
        assert attachment in spec.attachments
    
    def test_attachment_foreign_key_constraints(self, db_session):
        """Test foreign key constraints are enforced."""
        session, user, project, _ = db_session
        
        # Test invalid project_id
        with pytest.raises(IntegrityError):
            attachment = AttachmentMetadata(
                project_id="invalid-project-id",
                file_name="test.txt",
                mime_type="text/plain",
                file_size=100,
                gcs_path="gs://bucket/test.txt",
                uploaded_by_user_id=user.id
            )
            session.add(attachment)
            session.commit()
        
        session.rollback()
        
        # Test invalid user_id
        with pytest.raises(IntegrityError):
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name="test.txt",
                mime_type="text/plain",
                file_size=100,
                gcs_path="gs://bucket/test.txt",
                uploaded_by_user_id="invalid-user-id"
            )
            session.add(attachment)
            session.commit()
    
    def test_attachment_field_lengths(self, db_session):
        """Test field length constraints."""
        session, user, project, _ = db_session
        
        # Test file_name length (255 chars max)
        long_filename = "a" * 256  # Too long
        with pytest.raises(Exception):  # Could be IntegrityError or DataError
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name=long_filename,
                mime_type="text/plain",
                file_size=100,
                gcs_path="gs://bucket/test.txt",
                uploaded_by_user_id=user.id
            )
            session.add(attachment)
            session.commit()
        
        session.rollback()
        
        # Test valid file_name length (255 chars)
        valid_filename = "a" * 255
        attachment = AttachmentMetadata(
            project_id=project.id,
            file_name=valid_filename,
            mime_type="text/plain",
            file_size=100,
            gcs_path="gs://bucket/test.txt",
            uploaded_by_user_id=user.id
        )
        session.add(attachment)
        session.commit()
        
        assert attachment.file_name == valid_filename
    
    def test_attachment_default_values(self, db_session):
        """Test default values are set correctly."""
        session, user, project, _ = db_session
        
        attachment = AttachmentMetadata(
            project_id=project.id,
            file_name="defaults-test.txt",
            mime_type="text/plain",
            file_size=100,
            gcs_path="gs://bucket/defaults-test.txt",
            uploaded_by_user_id=user.id
            # Not setting status, version, upload_timestamp - should use defaults
        )
        
        session.add(attachment)
        session.commit()
        
        # Check defaults
        assert attachment.status == AttachmentStatus.UPLOADED.value
        assert attachment.version == "1.0"
        assert attachment.upload_timestamp is not None
        assert isinstance(attachment.upload_timestamp, datetime)
        assert attachment.upload_timestamp.tzinfo is not None  # Should have timezone
    
    def test_attachment_visibility_mixin(self, db_session):
        """Test VisibilityMixin functionality (soft delete)."""
        session, user, project, _ = db_session
        
        attachment = AttachmentMetadata(
            project_id=project.id,
            file_name="visibility-test.txt",
            mime_type="text/plain",
            file_size=100,
            gcs_path="gs://bucket/visibility-test.txt",
            uploaded_by_user_id=user.id
        )
        
        session.add(attachment)
        session.commit()
        
        # Initially not deleted
        assert not attachment.is_deleted
        
        # Soft delete
        attachment.is_deleted = True
        session.commit()
        
        # Verify soft delete
        assert attachment.is_deleted
        
        # The attachment should still exist in database but marked as deleted
        found_attachment = session.query(AttachmentMetadata).filter_by(id=attachment.id).first()
        assert found_attachment is not None
        assert found_attachment.is_deleted
    
    def test_attachment_uuid_generation(self, db_session):
        """Test UUID generation from BaseUuidMixin."""
        session, user, project, _ = db_session
        
        attachment1 = AttachmentMetadata(
            project_id=project.id,
            file_name="uuid-test-1.txt",
            mime_type="text/plain",
            file_size=100,
            gcs_path="gs://bucket/uuid-test-1.txt",
            uploaded_by_user_id=user.id
        )
        
        attachment2 = AttachmentMetadata(
            project_id=project.id,
            file_name="uuid-test-2.txt",
            mime_type="text/plain",
            file_size=100,
            gcs_path="gs://bucket/uuid-test-2.txt",
            uploaded_by_user_id=user.id
        )
        
        session.add_all([attachment1, attachment2])
        session.commit()
        
        # Verify UUIDs are generated and unique
        assert attachment1.id is not None
        assert attachment2.id is not None
        assert attachment1.id != attachment2.id
        
        # Verify UUID format
        import uuid
        uuid.UUID(attachment1.id)  # Should not raise exception
        uuid.UUID(attachment2.id)  # Should not raise exception
