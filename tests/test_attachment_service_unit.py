"""
Unit tests for attachment service functionality.
Tests individual functions and components in isolation using mocks.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from io import BytesIO
from datetime import datetime, timezone
from werkzeug.datastructures import FileStorage

from src.service.attachment_service import (
    get_company_bucket_name,
    upload_binary_to_gcs_direct,
    download_binary_from_gcs_direct,
    upload_binary_to_admin_service,
    download_attachment_from_gcs,
    create_attachment_metadata,
    save_attachment_metadata,
    upload_file_to_gcs
)
from common_models.models import AttachmentMetadata, AttachmentStatus


class TestBucketNaming:
    """Test bucket naming logic."""
    
    def test_get_company_bucket_name_with_company_id(self):
        """Test bucket naming with company ID."""
        with patch.dict('os.environ', {'GCS_BUCKET_NAME': 'test-bucket'}):
            result = get_company_bucket_name("company-123")
            assert result == "test-bucket-company-123"
    
    def test_get_company_bucket_name_default_company(self):
        """Test bucket naming with default/empty company ID."""
        with patch.dict('os.environ', {'GCS_BUCKET_NAME': 'test-bucket'}):
            assert get_company_bucket_name("") == "test-bucket"
            assert get_company_bucket_name("default") == "test-bucket"
    
    def test_get_company_bucket_name_default_bucket(self):
        """Test bucket naming with default bucket name."""
        with patch.dict('os.environ', {}, clear=True):
            result = get_company_bucket_name("company-123")
            assert result == "blitzy-os-internal-company-123"


class TestDirectGCSOperations:
    """Test direct GCS operations (fallback functionality)."""
    
    @patch('src.service.attachment_service.storage.Client')
    def test_upload_binary_to_gcs_direct_success(self, mock_storage_client):
        """Test successful direct GCS upload."""
        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.get_bucket.return_value = mock_bucket
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        
        # Test data
        file_data = b"test file content"
        file_path = "company_test/attachments_proj/file.txt"
        company_id = "test"
        content_type = "text/plain"
        
        # Execute
        result = upload_binary_to_gcs_direct(file_data, file_path, company_id, content_type)
        
        # Verify
        assert result == "gs://blitzy-os-internal-test/company_test/attachments_proj/file.txt"
        mock_blob.upload_from_string.assert_called_once_with(file_data, content_type=content_type)
    
    @patch('src.service.attachment_service.storage.Client')
    def test_upload_binary_to_gcs_direct_bucket_creation(self, mock_storage_client):
        """Test bucket creation when bucket doesn't exist."""
        from google.cloud.exceptions import NotFound
        
        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.get_bucket.side_effect = NotFound("Bucket not found")
        mock_client.bucket.return_value = mock_bucket
        mock_client.create_bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        
        # Execute
        result = upload_binary_to_gcs_direct(b"test", "test/file.txt", "test", "text/plain")
        
        # Verify bucket creation
        mock_client.create_bucket.assert_called_once()
        assert result.startswith("gs://")
    
    @patch('src.service.attachment_service.storage.Client')
    def test_download_binary_from_gcs_direct_success(self, mock_storage_client):
        """Test successful direct GCS download."""
        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.exists.return_value = True
        mock_blob.download_as_bytes.return_value = b"downloaded content"
        
        # Execute
        result = download_binary_from_gcs_direct("gs://bucket/path/file.txt", "test")
        
        # Verify
        assert result == b"downloaded content"
        mock_blob.download_as_bytes.assert_called_once()
    
    @patch('src.service.attachment_service.storage.Client')
    def test_download_binary_from_gcs_direct_file_not_found(self, mock_storage_client):
        """Test download when file doesn't exist."""
        # Setup mocks
        mock_client = Mock()
        mock_bucket = Mock()
        mock_blob = Mock()
        
        mock_storage_client.return_value = mock_client
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_blob.exists.return_value = False
        
        # Execute and verify exception
        with pytest.raises(Exception, match="File not found"):
            download_binary_from_gcs_direct("gs://bucket/path/file.txt", "test")


class TestAdminServiceIntegration:
    """Test admin service integration."""
    
    @patch('src.service.attachment_service.ServiceClient')
    @patch.dict('os.environ', {'SERVICE_URL_ADMIN': 'https://admin.example.com'})
    def test_upload_binary_to_admin_service_success(self, mock_service_client):
        """Test successful upload via admin service."""
        # Setup mocks
        mock_client = Mock()
        mock_response = Mock()
        mock_response.json.return_value = {'filePath': 'gs://bucket/file.txt'}
        mock_client.__enter__.return_value = mock_client
        mock_client.__exit__.return_value = None
        mock_client.post_binary.return_value = mock_response
        mock_service_client.return_value = mock_client
        
        # Execute
        result = upload_binary_to_admin_service(
            b"test data", "test/file.txt", "company", "text/plain"
        )
        
        # Verify
        assert result == 'gs://bucket/file.txt'
        mock_client.post_binary.assert_called_once_with(
            service_name="admin",
            endpoint="/v1/storage/upload",
            data=b"test data",
            params={'file_path': "test/file.txt", 'company_id': "company"},
            headers={'Content-Type': "text/plain"},
            timeout=600
        )
    
    @patch('src.service.attachment_service.upload_binary_to_gcs_direct')
    @patch('src.service.attachment_service.ServiceClient')
    @patch.dict('os.environ', {'SERVICE_URL_ADMIN': 'https://admin.example.com'})
    def test_upload_binary_to_admin_service_fallback(self, mock_service_client, mock_direct_upload):
        """Test fallback to direct GCS when admin service fails."""
        # Setup mocks
        mock_client = Mock()
        mock_client.__enter__.return_value = mock_client
        mock_client.__exit__.return_value = None
        mock_client.post_binary.side_effect = Exception("Service unavailable")
        mock_service_client.return_value = mock_client
        mock_direct_upload.return_value = "gs://bucket/fallback.txt"
        
        # Execute
        result = upload_binary_to_admin_service(
            b"test data", "test/file.txt", "company", "text/plain"
        )
        
        # Verify fallback was called
        assert result == "gs://bucket/fallback.txt"
        mock_direct_upload.assert_called_once_with(
            b"test data", "test/file.txt", "company", "text/plain"
        )
    
    @patch('src.service.attachment_service.download_binary_from_gcs_direct')
    @patch('src.service.attachment_service.ServiceClient')
    @patch.dict('os.environ', {'SERVICE_URL_ADMIN': 'https://admin.example.com'})
    def test_download_attachment_from_gcs_admin_success(self, mock_service_client, mock_direct_download):
        """Test successful download via admin service."""
        # Setup mocks
        mock_client = Mock()
        mock_response = Mock()
        mock_response.content = b"downloaded content"
        mock_client.__enter__.return_value = mock_client
        mock_client.__exit__.return_value = None
        mock_client.get.return_value = mock_response
        mock_service_client.return_value = mock_client
        
        # Execute
        result = download_attachment_from_gcs("gs://bucket/path/file.txt", "company")
        
        # Verify
        assert result == b"downloaded content"
        mock_client.get.assert_called_once()
        mock_direct_download.assert_not_called()


class TestAttachmentMetadata:
    """Test attachment metadata operations."""
    
    def test_create_attachment_metadata(self):
        """Test creating attachment metadata from file."""
        # Create mock file
        file_content = b"test file content"
        file_stream = BytesIO(file_content)
        file_storage = FileStorage(
            stream=file_stream,
            filename="test.txt",
            content_type="text/plain"
        )
        
        # Execute
        attachment = create_attachment_metadata(
            file=file_storage,
            project_id="proj-123",
            uploaded_by_user_id="user-456",
            tech_spec_id="spec-789",
            user_description="Test file"
        )
        
        # Verify
        assert isinstance(attachment, AttachmentMetadata)
        assert attachment.project_id == "proj-123"
        assert attachment.uploaded_by_user_id == "user-456"
        assert attachment.tech_spec_id == "spec-789"
        assert attachment.user_description == "Test file"
        assert attachment.file_name == "test.txt"
        assert attachment.mime_type == "text/plain"
        assert attachment.file_size == len(file_content)
        assert attachment.status == AttachmentStatus.UPLOADED
        assert attachment.version == "1.0"
        assert not attachment.is_deleted
        assert attachment.id is not None
    
    @patch('src.service.attachment_service.get_db_session')
    def test_save_attachment_metadata(self, mock_get_session):
        """Test saving attachment metadata to database."""
        # Setup mocks
        mock_session = Mock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        
        # Create test attachment
        attachment = AttachmentMetadata(
            id="test-id",
            project_id="proj-123",
            file_name="test.txt",
            mime_type="text/plain",
            file_size=100,
            uploaded_by_user_id="user-456"
        )
        
        # Execute
        result = save_attachment_metadata(attachment, mock_session)
        
        # Verify
        assert result == attachment
        mock_session.add.assert_called_once_with(attachment)
        mock_session.flush.assert_called_once()


class TestFileUploadIntegration:
    """Test the main file upload function."""
    
    @patch('src.service.attachment_service.upload_binary_to_admin_service')
    def test_upload_file_to_gcs_success(self, mock_admin_upload):
        """Test successful file upload with correct path structure."""
        # Setup mocks
        mock_admin_upload.return_value = "gs://bucket/company_test/attachments_proj/file-id.txt"
        
        # Create mock file
        file_content = b"test file content"
        file_stream = BytesIO(file_content)
        file_storage = FileStorage(
            stream=file_stream,
            filename="test.txt",
            content_type="text/plain"
        )
        
        # Execute
        result = upload_file_to_gcs(
            file=file_storage,
            company_id="test",
            project_id="proj",
            attachment_id="file-id"
        )
        
        # Verify
        assert result == "gs://bucket/company_test/attachments_proj/file-id.txt"
        mock_admin_upload.assert_called_once()
        
        # Verify correct path structure
        call_args = mock_admin_upload.call_args
        assert call_args[1]['file_path'] == "company_test/attachments_proj/file-id.txt"
        assert call_args[1]['company_id'] == "test"
        assert call_args[1]['content_type'] == "text/plain"
    
    @patch('src.service.attachment_service.upload_binary_to_admin_service')
    def test_upload_file_to_gcs_default_company(self, mock_admin_upload):
        """Test file upload with default company (empty company_id)."""
        mock_admin_upload.return_value = "gs://bucket/attachments_proj/file-id.txt"
        
        file_stream = BytesIO(b"test")
        file_storage = FileStorage(stream=file_stream, filename="test.txt")
        
        result = upload_file_to_gcs(
            file=file_storage,
            company_id="",
            project_id="proj",
            attachment_id="file-id"
        )
        
        # Verify path structure for default company
        call_args = mock_admin_upload.call_args
        assert call_args[1]['file_path'] == "attachments_proj/file-id.txt"
