#!/usr/bin/env python3
"""
Test runner for attachment system tests.
Runs all unit tests and integration tests for the attachment functionality.
"""
import sys
import os
import pytest
import subprocess
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_tests():
    """Run all attachment-related tests."""
    print("🧪 Running Attachment System Tests")
    print("=" * 50)
    
    # Test files to run
    test_files = [
        "tests/test_attachment_metadata_model.py",
        "tests/test_attachment_database_integration.py"
    ]
    
    # Check if test files exist
    missing_files = []
    for test_file in test_files:
        if not os.path.exists(test_file):
            missing_files.append(test_file)
    
    if missing_files:
        print(f"❌ Missing test files: {missing_files}")
        return False
    
    # Run pytest with verbose output
    pytest_args = [
        "-v",  # Verbose output
        "-s",  # Don't capture output
        "--tb=short",  # Short traceback format
        "--color=yes",  # Colored output
    ] + test_files
    
    print(f"📋 Running tests: {', '.join(test_files)}")
    print("-" * 50)
    
    try:
        result = pytest.main(pytest_args)
        
        if result == 0:
            print("\n" + "=" * 50)
            print("✅ All attachment tests passed!")
            return True
        else:
            print("\n" + "=" * 50)
            print("❌ Some tests failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        "pytest",
        "sqlalchemy",
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {missing_packages}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def setup_test_environment():
    """Setup test environment variables."""
    # Set test database URL if not already set
    if not os.environ.get("DATABASE_URL"):
        os.environ["DATABASE_URL"] = "sqlite:///:memory:"
    
    # Set test GCS bucket
    if not os.environ.get("GCS_BUCKET_NAME"):
        os.environ["GCS_BUCKET_NAME"] = "test-bucket"
    
    print("🔧 Test environment configured")

def run_specific_test(test_name):
    """Run a specific test by name."""
    if test_name == "model":
        test_files = ["tests/test_attachment_metadata_model.py"]
    elif test_name == "integration":
        test_files = ["tests/test_attachment_database_integration.py"]
    elif test_name == "all":
        return run_tests()
    else:
        print(f"❌ Unknown test name: {test_name}")
        print("Available tests: model, integration, all")
        return False
    
    pytest_args = ["-v", "-s", "--tb=short", "--color=yes"] + test_files
    
    try:
        result = pytest.main(pytest_args)
        return result == 0
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False

def main():
    """Main test runner function."""
    print("🚀 Attachment System Test Runner")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Setup environment
    setup_test_environment()
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        success = run_specific_test(test_name)
    else:
        success = run_tests()
    
    if success:
        print("\n🎉 Test run completed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Test run failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
