import pytest
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from werkzeug.datastructures import FileStorage
from io import BytesIO

from src.service.attachment_service import (
    process_batch_upload,
    upload_file_to_gcs,
    create_attachment_metadata,
    save_attachment_metadata
)
from common_models.models import AttachmentMetadata, AttachmentStatus


class TestAttachmentUpload:
    """Test cases for attachment upload functionality."""

    def test_create_attachment_metadata(self):
        """Test creating attachment metadata object."""
        # Test data
        file_name = "test_document.pdf"
        file_size = 1024
        mime_type = "application/pdf"
        project_id = "proj_123"
        uploaded_by_user_id = "user_456"
        gcs_path = "attachments/proj_123/att_789.pdf"
        user_description = "Test document"
        tech_spec_id = "tech_spec_123"

        # Create attachment metadata
        attachment = create_attachment_metadata(
            file_name=file_name,
            file_size=file_size,
            mime_type=mime_type,
            project_id=project_id,
            uploaded_by_user_id=uploaded_by_user_id,
            gcs_path=gcs_path,
            user_description=user_description,
            tech_spec_id=tech_spec_id
        )

        # Assertions
        assert attachment.file_name == file_name
        assert attachment.file_size == file_size
        assert attachment.mime_type == mime_type
        assert attachment.project_id == project_id
        assert attachment.uploaded_by_user_id == uploaded_by_user_id
        assert attachment.gcs_path == gcs_path
        assert attachment.user_description == user_description
        assert attachment.tech_spec_id == tech_spec_id
        assert attachment.status == AttachmentStatus.UPLOADED
        assert attachment.upload_timestamp is not None
        assert attachment.id is not None

    def test_create_attachment_metadata_without_optional_fields(self):
        """Test creating attachment metadata without optional fields."""
        # Test data
        file_name = "test_document.pdf"
        file_size = 1024
        mime_type = "application/pdf"
        project_id = "proj_123"
        uploaded_by_user_id = "user_456"
        gcs_path = "attachments/proj_123/att_789.pdf"

        # Create attachment metadata without optional fields
        attachment = create_attachment_metadata(
            file_name=file_name,
            file_size=file_size,
            mime_type=mime_type,
            project_id=project_id,
            uploaded_by_user_id=uploaded_by_user_id,
            gcs_path=gcs_path
        )

        # Assertions
        assert attachment.file_name == file_name
        assert attachment.file_size == file_size
        assert attachment.mime_type == mime_type
        assert attachment.project_id == project_id
        assert attachment.uploaded_by_user_id == uploaded_by_user_id
        assert attachment.gcs_path == gcs_path
        assert attachment.user_description is None
        assert attachment.tech_spec_id is None
        assert attachment.status == AttachmentStatus.UPLOADED

    @patch('src.service.attachment_service.get_company_bucket_name')
    @patch('src.service.attachment_service.create_bucket_if_not_exists')
    @patch('src.service.attachment_service.storage.Client')
    def test_upload_file_to_gcs_success(self, mock_storage_client, mock_create_bucket, mock_get_bucket_name):
        """Test successful file upload to GCS."""
        # Mock setup
        mock_get_bucket_name.return_value = "test-company-bucket"
        mock_bucket = Mock()
        mock_blob = Mock()
        mock_client = Mock()
        mock_client.bucket.return_value = mock_bucket
        mock_bucket.blob.return_value = mock_blob
        mock_storage_client.return_value = mock_client

        # Create test file
        test_content = b"Test file content"
        test_file = FileStorage(
            stream=BytesIO(test_content),
            filename="test.txt",
            content_type="text/plain"
        )

        # Test upload
        gcs_path = upload_file_to_gcs(
            file=test_file,
            company_id="company_123",
            project_id="proj_456",
            attachment_id="att_789"
        )

        # Assertions
        mock_get_bucket_name.assert_called_once_with("company_123")
        mock_create_bucket.assert_called_once_with("test-company-bucket")
        mock_client.bucket.assert_called_once_with("test-company-bucket")
        mock_bucket.blob.assert_called_once_with("attachments/proj_456/att_789.txt")
        mock_blob.upload_from_file.assert_called_once()
        assert gcs_path == "attachments/proj_456/att_789.txt"

    @patch('src.service.attachment_service.upload_file_to_gcs')
    @patch('src.service.attachment_service.save_attachment_metadata')
    def test_process_batch_upload_success(self, mock_save_metadata, mock_upload_gcs):
        """Test successful batch upload processing."""
        # Mock setup
        mock_upload_gcs.return_value = "attachments/proj_123/att_456.txt"
        mock_save_metadata.return_value = True

        # Create test files
        test_files = [
            FileStorage(
                stream=BytesIO(b"Test content 1"),
                filename="test1.txt",
                content_type="text/plain"
            ),
            FileStorage(
                stream=BytesIO(b"Test content 2"),
                filename="test2.txt",
                content_type="text/plain"
            )
        ]

        # Test batch upload
        result = process_batch_upload(
            files=test_files,
            project_id="proj_123",
            uploaded_by_user_id="user_456",
            company_id="company_789"
        )

        # Assertions
        assert "results" in result
        assert len(result["results"]) == 2
        assert "errors" not in result or result["errors"] is None
        
        # Verify upload was called for each file
        assert mock_upload_gcs.call_count == 2
        assert mock_save_metadata.call_count == 2

    @patch('src.service.attachment_service.upload_file_to_gcs')
    @patch('src.service.attachment_service.save_attachment_metadata')
    def test_process_batch_upload_with_user_descriptions(self, mock_save_metadata, mock_upload_gcs):
        """Test batch upload with user descriptions."""
        # Mock setup
        mock_upload_gcs.return_value = "attachments/proj_123/att_456.txt"
        mock_save_metadata.return_value = True

        # Create test files
        test_files = [
            FileStorage(
                stream=BytesIO(b"Test content 1"),
                filename="test1.txt",
                content_type="text/plain"
            )
        ]

        user_descriptions = ["This is a test file"]

        # Test batch upload with descriptions
        result = process_batch_upload(
            files=test_files,
            project_id="proj_123",
            uploaded_by_user_id="user_456",
            company_id="company_789",
            user_descriptions=user_descriptions
        )

        # Assertions
        assert "results" in result
        assert len(result["results"]) == 1
        assert result["results"][0].user_description == "This is a test file"

    @patch('src.service.attachment_service.upload_file_to_gcs')
    @patch('src.service.attachment_service.save_attachment_metadata')
    def test_process_batch_upload_partial_failure(self, mock_save_metadata, mock_upload_gcs):
        """Test batch upload with some files failing."""
        # Mock setup - first file succeeds, second fails
        mock_upload_gcs.side_effect = [
            "attachments/proj_123/att_456.txt",  # Success
            Exception("Upload failed")  # Failure
        ]
        mock_save_metadata.return_value = True

        # Create test files
        test_files = [
            FileStorage(
                stream=BytesIO(b"Test content 1"),
                filename="test1.txt",
                content_type="text/plain"
            ),
            FileStorage(
                stream=BytesIO(b"Test content 2"),
                filename="test2.txt",
                content_type="text/plain"
            )
        ]

        # Test batch upload
        result = process_batch_upload(
            files=test_files,
            project_id="proj_123",
            uploaded_by_user_id="user_456",
            company_id="company_789"
        )

        # Assertions
        assert "results" in result
        assert len(result["results"]) == 1  # Only one successful upload
        assert "errors" in result
        assert len(result["errors"]) == 1  # One error
        assert result["errors"][0]["fileName"] == "test2.txt"
        assert "Upload failed" in result["errors"][0]["error"]

    def test_process_batch_upload_empty_files(self):
        """Test batch upload with empty file list."""
        result = process_batch_upload(
            files=[],
            project_id="proj_123",
            uploaded_by_user_id="user_456",
            company_id="company_789"
        )

        # Assertions
        assert "results" in result
        assert len(result["results"]) == 0
        assert "errors" not in result or result["errors"] is None


if __name__ == "__main__":
    pytest.main([__file__])
