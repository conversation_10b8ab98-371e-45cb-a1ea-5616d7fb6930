"""
Integration tests for attachment database operations.
Tests real database scenarios with proper setup and teardown.
"""
import pytest
import os
from datetime import datetime, timezone
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError
import uuid

from common_models.models import (
    AttachmentMetadata, 
    AttachmentStatus, 
    Base,
    Project,
    User,
    TechnicalSpec
)
from common_models.db_client import get_db_session


class TestAttachmentDatabaseIntegration:
    """Integration tests for attachment database operations."""
    
    @pytest.fixture(scope="class")
    def test_database(self):
        """Create test database with proper schema."""
        # Use PostgreSQL-compatible in-memory database for testing
        # In real scenarios, this would connect to a test PostgreSQL instance
        engine = create_engine("sqlite:///:memory:", echo=False)
        
        # Create all tables
        Base.metadata.create_all(engine)
        
        Session = sessionmaker(bind=engine)
        return Session
    
    @pytest.fixture
    def db_session(self, test_database):
        """Create database session with test data."""
        session = test_database()
        
        # Create test user
        test_user = User(
            id=str(uuid.uuid4()),
            email="<EMAIL>",
            name="Integration Test User",
            created_at=datetime.now(timezone.utc)
        )
        session.add(test_user)
        
        # Create test project
        test_project = Project(
            id=str(uuid.uuid4()),
            name="Integration Test Project",
            description="Project for integration testing",
            created_by_user_id=test_user.id,
            created_at=datetime.now(timezone.utc)
        )
        session.add(test_project)
        
        # Create test technical spec
        test_spec = TechnicalSpec(
            id=str(uuid.uuid4()),
            project_id=test_project.id,
            title="Integration Test Spec",
            content="Technical specification for integration testing",
            created_at=datetime.now(timezone.utc)
        )
        session.add(test_spec)
        
        session.commit()
        
        yield session, test_user, test_project, test_spec
        
        session.close()
    
    def test_bulk_attachment_creation(self, db_session):
        """Test creating multiple attachments in bulk."""
        session, user, project, spec = db_session
        
        # Create multiple attachments
        attachments = []
        for i in range(10):
            attachment = AttachmentMetadata(
                project_id=project.id,
                tech_spec_id=spec.id if i % 2 == 0 else None,  # Some with spec, some without
                file_name=f"bulk-test-{i}.pdf",
                mime_type="application/pdf",
                file_size=1024 * (i + 1),
                gcs_path=f"gs://test-bucket/company_123/attachments_{project.id}/bulk-{i}.pdf",
                uploaded_by_user_id=user.id,
                user_description=f"Bulk test attachment {i}"
            )
            attachments.append(attachment)
        
        # Bulk insert
        session.add_all(attachments)
        session.commit()
        
        # Verify all attachments were created
        created_attachments = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.file_name.like("bulk-test-%")
        ).all()
        
        assert len(created_attachments) == 10
        
        # Verify some have tech specs, some don't
        with_spec = [att for att in created_attachments if att.tech_spec_id is not None]
        without_spec = [att for att in created_attachments if att.tech_spec_id is None]
        
        assert len(with_spec) == 5
        assert len(without_spec) == 5
    
    def test_attachment_querying_and_filtering(self, db_session):
        """Test various querying and filtering scenarios."""
        session, user, project, spec = db_session
        
        # Create attachments with different statuses and types
        test_data = [
            ("document.pdf", "application/pdf", AttachmentStatus.UPLOADED, 1024),
            ("image.jpg", "image/jpeg", AttachmentStatus.SAFE, 2048),
            ("archive.zip", "application/zip", AttachmentStatus.PENDING_SCAN, 4096),
            ("infected.exe", "application/octet-stream", AttachmentStatus.INFECTED, 512),
            ("failed.doc", "application/msword", AttachmentStatus.FAILED, 256)
        ]
        
        for filename, mime_type, status, size in test_data:
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name=filename,
                mime_type=mime_type,
                file_size=size,
                gcs_path=f"gs://test-bucket/company_123/attachments_{project.id}/{filename}",
                uploaded_by_user_id=user.id,
                status=status.value
            )
            session.add(attachment)
        
        session.commit()
        
        # Test filtering by status
        safe_attachments = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.status == AttachmentStatus.SAFE.value
        ).all()
        assert len(safe_attachments) == 1
        assert safe_attachments[0].file_name == "image.jpg"
        
        # Test filtering by mime type
        pdf_attachments = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.mime_type == "application/pdf"
        ).all()
        assert len(pdf_attachments) == 1
        assert pdf_attachments[0].file_name == "document.pdf"
        
        # Test filtering by file size range
        large_attachments = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.file_size > 1000
        ).all()
        assert len(large_attachments) == 3  # pdf, jpg, zip
        
        # Test filtering by project
        project_attachments = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.project_id == project.id
        ).all()
        assert len(project_attachments) >= 5  # At least our test data
    
    def test_attachment_soft_delete_operations(self, db_session):
        """Test soft delete operations and visibility."""
        session, user, project, _ = db_session
        
        # Create test attachment
        attachment = AttachmentMetadata(
            project_id=project.id,
            file_name="soft-delete-test.txt",
            mime_type="text/plain",
            file_size=100,
            gcs_path="gs://test-bucket/soft-delete-test.txt",
            uploaded_by_user_id=user.id
        )
        session.add(attachment)
        session.commit()
        
        attachment_id = attachment.id
        
        # Verify attachment is visible
        visible_attachment = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.id == attachment_id,
            AttachmentMetadata.is_deleted == False
        ).first()
        assert visible_attachment is not None
        
        # Soft delete
        attachment.is_deleted = True
        session.commit()
        
        # Verify attachment is not visible in normal queries
        visible_attachment = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.id == attachment_id,
            AttachmentMetadata.is_deleted == False
        ).first()
        assert visible_attachment is None
        
        # But still exists when including deleted
        deleted_attachment = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.id == attachment_id
        ).first()
        assert deleted_attachment is not None
        assert deleted_attachment.is_deleted
    
    def test_attachment_cascade_operations(self, db_session):
        """Test cascade behavior when related entities are deleted."""
        session, user, project, spec = db_session
        
        # Create attachment linked to technical spec
        attachment = AttachmentMetadata(
            project_id=project.id,
            tech_spec_id=spec.id,
            file_name="cascade-test.pdf",
            mime_type="application/pdf",
            file_size=1024,
            gcs_path="gs://test-bucket/cascade-test.pdf",
            uploaded_by_user_id=user.id
        )
        session.add(attachment)
        session.commit()
        
        attachment_id = attachment.id
        
        # Verify attachment exists
        assert session.query(AttachmentMetadata).filter_by(id=attachment_id).first() is not None
        
        # Delete technical spec (should not cascade delete attachment due to nullable FK)
        session.delete(spec)
        session.commit()
        
        # Attachment should still exist but with null tech_spec_id
        remaining_attachment = session.query(AttachmentMetadata).filter_by(id=attachment_id).first()
        assert remaining_attachment is not None
        assert remaining_attachment.tech_spec_id is None
    
    def test_attachment_concurrent_operations(self, db_session):
        """Test concurrent operations on attachments."""
        session, user, project, _ = db_session
        
        # Create base attachment
        attachment = AttachmentMetadata(
            project_id=project.id,
            file_name="concurrent-test.txt",
            mime_type="text/plain",
            file_size=100,
            gcs_path="gs://test-bucket/concurrent-test.txt",
            uploaded_by_user_id=user.id,
            status=AttachmentStatus.UPLOADED.value
        )
        session.add(attachment)
        session.commit()
        
        # Simulate concurrent status updates
        # In real scenario, this would be multiple sessions/transactions
        attachment.status = AttachmentStatus.PENDING_SCAN.value
        session.commit()
        
        # Verify status was updated
        updated_attachment = session.query(AttachmentMetadata).filter_by(id=attachment.id).first()
        assert updated_attachment.status == AttachmentStatus.PENDING_SCAN.value
        
        # Update to final status
        updated_attachment.status = AttachmentStatus.SAFE.value
        session.commit()
        
        # Verify final status
        final_attachment = session.query(AttachmentMetadata).filter_by(id=attachment.id).first()
        assert final_attachment.status == AttachmentStatus.SAFE.value
    
    def test_attachment_search_operations(self, db_session):
        """Test search and text-based operations."""
        session, user, project, _ = db_session
        
        # Create attachments with searchable content
        search_data = [
            ("important-document.pdf", "Important project documentation"),
            ("meeting-notes.txt", "Notes from the important meeting"),
            ("design-specs.pdf", "Technical design specifications"),
            ("random-file.jpg", "Random image file"),
        ]
        
        for filename, description in search_data:
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name=filename,
                mime_type="application/pdf" if filename.endswith('.pdf') else "text/plain",
                file_size=1024,
                gcs_path=f"gs://test-bucket/{filename}",
                uploaded_by_user_id=user.id,
                user_description=description
            )
            session.add(attachment)
        
        session.commit()
        
        # Search by filename pattern
        pdf_files = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.file_name.like("%.pdf")
        ).all()
        assert len(pdf_files) == 2
        
        # Search by description content
        important_files = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.user_description.like("%important%")
        ).all()
        assert len(important_files) == 2
        
        # Search by filename content
        design_files = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.file_name.like("%design%")
        ).all()
        assert len(design_files) == 1
        assert design_files[0].file_name == "design-specs.pdf"
    
    def test_attachment_statistics_queries(self, db_session):
        """Test statistical and aggregation queries."""
        session, user, project, _ = db_session
        
        # Create attachments with various sizes and types
        stats_data = [
            ("small.txt", "text/plain", 100),
            ("medium.pdf", "application/pdf", 1024),
            ("large.zip", "application/zip", 10240),
            ("huge.mp4", "video/mp4", 102400),
        ]
        
        for filename, mime_type, size in stats_data:
            attachment = AttachmentMetadata(
                project_id=project.id,
                file_name=filename,
                mime_type=mime_type,
                file_size=size,
                gcs_path=f"gs://test-bucket/{filename}",
                uploaded_by_user_id=user.id
            )
            session.add(attachment)
        
        session.commit()
        
        # Count attachments by project
        from sqlalchemy import func
        
        count_result = session.query(func.count(AttachmentMetadata.id)).filter(
            AttachmentMetadata.project_id == project.id
        ).scalar()
        assert count_result >= 4  # At least our test data
        
        # Sum total file sizes
        total_size = session.query(func.sum(AttachmentMetadata.file_size)).filter(
            AttachmentMetadata.project_id == project.id,
            AttachmentMetadata.file_name.in_([data[0] for data in stats_data])
        ).scalar()
        expected_total = sum(data[2] for data in stats_data)
        assert total_size == expected_total
        
        # Average file size
        avg_size = session.query(func.avg(AttachmentMetadata.file_size)).filter(
            AttachmentMetadata.project_id == project.id,
            AttachmentMetadata.file_name.in_([data[0] for data in stats_data])
        ).scalar()
        expected_avg = expected_total / len(stats_data)
        assert abs(float(avg_size) - expected_avg) < 0.01  # Float comparison
        
        # Group by mime type
        mime_counts = session.query(
            AttachmentMetadata.mime_type,
            func.count(AttachmentMetadata.id)
        ).filter(
            AttachmentMetadata.project_id == project.id,
            AttachmentMetadata.file_name.in_([data[0] for data in stats_data])
        ).group_by(AttachmentMetadata.mime_type).all()
        
        mime_dict = dict(mime_counts)
        assert mime_dict.get("text/plain") == 1
        assert mime_dict.get("application/pdf") == 1
        assert mime_dict.get("application/zip") == 1
        assert mime_dict.get("video/mp4") == 1
