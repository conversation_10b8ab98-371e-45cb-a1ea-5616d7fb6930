import importlib.util
import json
import logging
import sys
import time
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional

from sqlalchemy import text

logger = logging.getLogger(__name__)


class MigrationStatus(Enum):
    PENDING = "pending"
    SUCCESS = "success"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


@dataclass
class MigrationResult:
    """Represents the result of a migration execution"""
    version: str
    success: bool
    elapsed_ms: int
    error_message: Optional[str] = None
    applied_at: datetime = None
    status: MigrationStatus = MigrationStatus.PENDING
    details: Dict[str, Any] = None


class MigrationExecutor:
    """Handles execution of migrations"""

    def __init__(self, engine, versions_dir: str):
        self.engine = engine
        self.versions_dir = Path(versions_dir)

    def _ensure_migration_table(self):
        """Ensure schema_migrations table exists"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS schema_migrations (
            version varchar NOT NULL PRIMARY KEY,
            applied_at timestamptz NOT NULL,
            description varchar(1024),
            checksum varchar(64),
            is_success bool NOT NULL,
            error_message varchar,
            elapsed_time_ms bigint,
            status varchar(50),
            details jsonb
        );
        """

        with self.engine.connect().execution_options(isolation_level="AUTOCOMMIT") as conn:
            conn.execute(text(create_table_sql))
            logger.info("Ensured schema_migrations table exists")

    def _get_applied_migrations(self) -> List[str]:
        """Get list of already applied migrations"""
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT version, applied_at, is_success, status
                FROM schema_migrations
                ORDER BY applied_at
            """))
            return [row[0] for row in result if row.is_success]

    def _get_pending_migrations(self) -> List[str]:
        """Get list of pending migrations"""
        migrations = [
            f.stem for f in self.versions_dir.glob("*.py")
            if f.stem != '__init__' and not f.stem.startswith('_')
        ]

        applied = set(self._get_applied_migrations())
        pending = [m for m in migrations if m not in applied]
        return sorted(pending)

    def _load_migration_module(self, version: str):
        """Load migration module dynamically"""
        try:
            file_path = self.versions_dir / f"{version}.py"
            if not file_path.exists():
                raise ValueError(f"Migration file not found: {version}")

            spec = importlib.util.spec_from_file_location(version, file_path)
            module = importlib.util.module_from_spec(spec)
            sys.modules[version] = module
            spec.loader.exec_module(module)

            required_functions = ['up', 'down']
            for func in required_functions:
                if not hasattr(module, func):
                    raise ValueError(f"Migration {version} missing required function: {func}")

            return module

        except Exception as e:
            logger.error(f"Error loading migration {version}: {str(e)}")
            raise

    def _record_migration(self, result: MigrationResult):
        """Record migration execution in schema_migrations table"""
        with self.engine.connect().execution_options(isolation_level="AUTOCOMMIT") as conn:
            try:
                # Try to update existing record
                update_query = text("""
                    UPDATE schema_migrations
                    SET
                        applied_at = :applied_at,
                        is_success = :success,
                        error_message = :error,
                        elapsed_time_ms = :elapsed_ms,
                        status = :status,
                        details = :details
                    WHERE version = :version
                """)

                update_result = conn.execute(
                    update_query,
                    {
                        "version": result.version,
                        "applied_at": result.applied_at or datetime.now(timezone.utc),
                        "success": result.success,
                        "error": result.error_message,
                        "elapsed_ms": result.elapsed_ms,
                        "status": result.status.value,
                        "details": json.dumps(result.details) if result.details else None
                    }
                )

                # If no record was updated, insert new record
                if update_result.rowcount == 0:
                    conn.execute(
                        text("""
                            INSERT INTO schema_migrations
                            (version, applied_at, is_success, error_message, elapsed_time_ms, status, details)
                            VALUES (:version, :applied_at, :success, :error, :elapsed_ms, :status, :details)
                        """),
                        {
                            "version": result.version,
                            "applied_at": result.applied_at or datetime.now(timezone.utc),
                            "success": result.success,
                            "error": result.error_message,
                            "elapsed_ms": result.elapsed_ms,
                            "status": result.status.value,
                            "details": json.dumps(result.details) if result.details else None
                        }
                    )

                logger.info(f"Recorded migration result for version {result.version}")

            except Exception as e:
                logger.error(f"Failed to record migration result: {str(e)}")
                raise

    def run_migrations(self, target_version: Optional[str] = None, dry_run: bool = False):
        """Run all pending migrations or up to target version"""
        self._ensure_migration_table()
        pending = self._get_pending_migrations()

        if not pending:
            logger.info("No pending migrations. Database is up to date.")
            return []

        results = []
        for version in pending:
            try:
                start_time = time.time()
                module = self._load_migration_module(version)

                # Execute each DDL statement separately with AUTOCOMMIT
                with self.engine.connect().execution_options(isolation_level="AUTOCOMMIT") as conn:
                    statements = module.up()
                    executed_statements = []

                    try:
                        for statement in statements:
                            try:
                                conn.execute(text(statement))
                                logger.info(f"Executed statement: {statement}")
                                executed_statements.append(statement)
                            except Exception as e:
                                logger.error(f"Failed to execute statement '{statement}': {str(e)}")
                                raise

                        # Record successful migration
                        elapsed_ms = int((time.time() - start_time) * 1000)
                        result = MigrationResult(
                            version=version,
                            success=True,
                            elapsed_ms=elapsed_ms,
                            status=MigrationStatus.SUCCESS,
                            applied_at=datetime.now(timezone.utc),
                            details={"statements_executed": len(executed_statements)}
                        )
                        self._record_migration(result)
                        results.append(result)
                        logger.info(f"Successfully applied migration: {version}")

                    except Exception as e:
                        # Record failed migration
                        error_result = MigrationResult(
                            version=version,
                            success=False,
                            elapsed_ms=int((time.time() - start_time) * 1000),
                            error_message=str(e),
                            status=MigrationStatus.FAILED,
                            applied_at=datetime.now(timezone.utc),
                            details={
                                "error_type": type(e).__name__,
                                "executed_statements": len(executed_statements)
                            }
                        )
                        self._record_migration(error_result)
                        results.append(error_result)
                        logger.error(f"Error applying migration {version}: {str(e)}")
                        raise

            except Exception as e:
                logger.error(f"Migration {version} failed: {str(e)}")
                raise

        return results

    def rollback(self, steps: int = 1):
        """Rollback the last n migrations"""
        # Get successfully applied migrations
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT version
                FROM schema_migrations
                WHERE is_success = true
                ORDER BY applied_at DESC
            """))
            applied = [row[0] for row in result]

        if not applied:
            logger.info("No migrations to rollback")
            return []

        to_rollback = applied[:steps]
        results = []

        for version in reversed(to_rollback):
            try:
                start_time = time.time()
                module = self._load_migration_module(version)

                # Execute down migration statements with AUTOCOMMIT
                with self.engine.connect().execution_options(isolation_level="AUTOCOMMIT") as conn:
                    statements = module.down()
                    for statement in statements:
                        try:
                            conn.execute(text(statement))
                            logger.info(f"Executed rollback statement: {statement}")
                        except Exception as e:
                            raise Exception(f"Failed to execute rollback statement '{statement}': {str(e)}")

                # Record successful rollback
                elapsed_ms = int((time.time() - start_time) * 1000)
                result = MigrationResult(
                    version=version,
                    success=True,
                    elapsed_ms=elapsed_ms,
                    status=MigrationStatus.ROLLED_BACK,
                    applied_at=datetime.now(timezone.utc),
                    details={"rollback": True, "statements_executed": len(statements)}
                )

                self._record_migration(result)
                results.append(result)
                logger.info(f"Successfully rolled back migration: {version}")

            except Exception as e:
                # Just log the error but don't record failed rollbacks
                logger.error(f"Error rolling back migration {version}: {str(e)}")
                raise

        return results

    def get_detailed_status(self) -> Dict[str, Any]:
        """Get detailed status of all migrations"""
        all_migrations = {
            f.stem: {"file_path": f, "status": MigrationStatus.PENDING}
            for f in self.versions_dir.glob("*.py")
            if f.stem != '__init__' and not f.stem.startswith('_')
        }

        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT
                    version,
                    applied_at,
                    is_success,
                    error_message,
                    elapsed_time_ms,
                    status,
                    details
                FROM schema_migrations
                ORDER BY applied_at
            """))

            for row in result:
                if row.version in all_migrations:
                    all_migrations[row.version].update({
                        "applied_at": row.applied_at,
                        "success": row.is_success,
                        "error_message": row.error_message,
                        "elapsed_ms": row.elapsed_time_ms,
                        "status": MigrationStatus(row.status),
                        "details": row.details
                    })

        return {
            "total_migrations": len(all_migrations),
            "pending_count": len([m for m in all_migrations.values() if m["status"] == MigrationStatus.PENDING]),
            "success_count": len([m for m in all_migrations.values() if m["status"] == MigrationStatus.SUCCESS]),
            "failed_count": len([m for m in all_migrations.values() if m["status"] == MigrationStatus.FAILED]),
            "rolled_back_count": len(
                [m for m in all_migrations.values() if m["status"] == MigrationStatus.ROLLED_BACK]),
            "migrations": all_migrations
        }
