"""
Auto-generated migration: add_attachment_metadata
Version: 20250801_145217_5cbfaa63
Created At: 2025-08-01T14:52:17.449468
Author: sdai
"""

from typing import List, Optional

from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "CREATE TABLE attachment_metadata (project_id VARCHAR NOT NULL, tech_spec_id VARCHAR, file_name VA<PERSON>HAR(255) NOT NULL, mime_type VARCHAR(100) NOT NULL, file_size INTEGER NOT NULL, gcs_path VARCHAR(500) NOT NULL, uploaded_by_user_id VARCHAR NOT NULL, upload_timestamp TIMESTAMPTZ, user_description TEXT, status VARCHAR(12) NOT NULL DEFAULT 'UPLOADED', version VARCHAR(50) NOT NULL DEFAULT '1.0', is_deleted BOOLEAN NOT NULL DEFAULT FALSE, deleted_at TIMESTAMPTZ, id VARCHAR PRIMARY KEY NOT NULL, created_at TIMESTAMPTZ, updated_at TIMESTAMPTZ);",
        "ALTER TABLE attachment_metadata ADD CONSTRAINT fk_attachment_metadata_uploaded_by_user_id FOREIGN KEY (uploaded_by_user_id) REFERENCES users(id);",
        "ALTER TABLE attachment_metadata ADD CONSTRAINT fk_attachment_metadata_project_id FOREIGN KEY (project_id) REFERENCES projects(id);",
        "ALTER TABLE attachment_metadata ADD CONSTRAINT fk_attachment_metadata_tech_spec_id FOREIGN KEY (tech_spec_id) REFERENCES technical_specs(id);"
    ]


def down() -> List[str]:
    return [
        "DROP TABLE attachment_metadata;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
