"""
Manual migration: add_indexes_to_cloud_run_job_tracker
Version: 20250806_132217_d1314bcc_add_indexes_to_cloud_run_job_tracker
Created At: 2025-08-06T13:22:17.942480
Author: adarshsinghparihar
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        # Single column indexes
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_project_id ON cloud_run_job_trackers (project_id);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_tech_spec_id ON cloud_run_job_trackers (tech_spec_id);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_code_gen_id ON cloud_run_job_trackers (code_gen_id);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_job_id ON cloud_run_job_trackers (job_id);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_job_phase ON cloud_run_job_trackers (job_phase);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_job_status ON cloud_run_job_trackers (job_status);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_job_name ON cloud_run_job_trackers (job_name);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_user_id ON cloud_run_job_trackers (user_id);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_is_triggered ON cloud_run_job_trackers (is_triggered);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_trigger_topic ON cloud_run_job_trackers (trigger_topic);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_original_created_at ON cloud_run_job_trackers (original_created_at);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_updated_at ON cloud_run_job_trackers (updated_at);",
        
        # Composite indexes for optimized query patterns
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_status_created_at ON cloud_run_job_trackers (job_status, created_at DESC);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_status_updated_at ON cloud_run_job_trackers (job_status, updated_at DESC);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_user_project ON cloud_run_job_trackers (user_id, project_id);",
        "CREATE INDEX IF NOT EXISTS idx_cloud_run_job_trackers_trigger_topic_status ON cloud_run_job_trackers (trigger_topic, job_status);"
    ]


def down() -> List[str]:
    return [
        # Drop composite indexes first
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_trigger_topic_status;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_user_project;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_status_updated_at;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_status_created_at;",
        
        # Drop single column indexes
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_updated_at;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_original_created_at;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_trigger_topic;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_is_triggered;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_user_id;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_job_name;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_job_status;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_job_phase;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_job_id;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_code_gen_id;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_tech_spec_id;",
        "DROP INDEX IF EXISTS idx_cloud_run_job_trackers_project_id;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass