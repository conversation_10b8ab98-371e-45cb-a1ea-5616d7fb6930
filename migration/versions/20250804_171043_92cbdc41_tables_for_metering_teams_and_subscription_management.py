"""
Manual migration: tables_for_metering_teams_and_subscription_management
Version: 20250804_171043_92cbdc41_tables_for_metering_teams_and_subscription_management
Created At: 2025-08-04T17:10:43.271683
Author: neeraj
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        """
        CREATE TABLE project_run_metering (
            id VARCHAR PRIMARY KEY NOT NULL,
            project_run_id VARCHAR NOT NULL,
            estimated_lines_generated INTEGER,
            estimated_hours_saved FLOAT,
            files_onboarded INTEGER,
            lines_onboarded INTEGER,
            files_touched INTEGER,
            lines_added INTEGER,
            lines_edited INTEGER,
            lines_removed INTEGER,
            lines_generated INTEGER,
            hours_saved FLOAT,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            deleted_at TIMESTAMPTZ,
            CONSTRAINT fk_project_run_metering_project_run FOREIGN KEY (project_run_id) REFERENCES project_runs(id)
        );
        """,
        """
        CREATE TABLE aggregate_metering (
            id VARCHAR PRIMARY KEY NOT NULL,
            entity_type VARCHAR NOT NULL,
            entity_id VARCHAR NOT NULL,
            subscription_id VARCHAR NOT NULL,
            files_onboarded INTEGER,
            lines_onboarded INTEGER,
            files_touched INTEGER,
            lines_added INTEGER,
            lines_edited INTEGER,
            lines_removed INTEGER,
            lines_generated INTEGER,
            hours_saved FLOAT,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            deleted_at TIMESTAMPTZ,
            CONSTRAINT fk_aggregate_metering_subscription_id FOREIGN KEY (subscription_id) REFERENCES subscriptions(id)
        );
        """,
        """
        CREATE TABLE aggregate_metering_trail (
            id VARCHAR PRIMARY KEY NOT NULL,
            aggregate_metering_id VARCHAR NOT NULL,
            project_run_id VARCHAR NOT NULL,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            deleted_at TIMESTAMPTZ,
            CONSTRAINT fk_aggregate_metering_trail_aggregate_metering FOREIGN KEY (aggregate_metering_id) REFERENCES aggregate_metering(id),
            CONSTRAINT fk_aggregate_metering_trail_project_run FOREIGN KEY (project_run_id) REFERENCES project_runs(id)
        );
        """,
        """
        CREATE TABLE reserved_estimates (
            id VARCHAR PRIMARY KEY NOT NULL,
            subscription_id VARCHAR NOT NULL,
            project_id VARCHAR NOT NULL,
            code_gen_id VARCHAR NOT NULL,
            estimated_lines_generated INTEGER,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            deleted_at TIMESTAMPTZ,
            CONSTRAINT fk_reserved_estimate_subscription FOREIGN KEY (subscription_id) REFERENCES subscriptions(id),
            CONSTRAINT fk_reserved_estimate_project FOREIGN KEY (project_id) REFERENCES projects(id),
            CONSTRAINT fk_reserved_estimate_code_gen FOREIGN KEY (code_gen_id) REFERENCES code_generations(id)
        );
        """,
        """
        CREATE TABLE subscription_overages (
            id VARCHAR PRIMARY KEY NOT NULL,
            subscription_id VARCHAR NOT NULL,
            overage_type VARCHAR NOT NULL,
            overage_amount FLOAT NOT NULL,
            payment_status VARCHAR NOT NULL,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
            deleted_at TIMESTAMPTZ,
            CONSTRAINT fk_subscription_overages_subscription FOREIGN KEY (subscription_id) REFERENCES subscriptions(id)
        );
        """,
        """
        CREATE TABLE subscribers (
            id VARCHAR PRIMARY KEY NOT NULL,
            plan_subscriber_id VARCHAR NOT NULL,
            user_id VARCHAR NOT NULL
        );
        """,
        """
        ALTER TABLE subscriptions ADD COLUMN plan_owner_id VARCHAR;
        """,
        """
        ALTER TABLE subscriptions ADD COLUMN plan_subscriber_id VARCHAR;
        """,
    ]


def down() -> List[str]:
    return [
        "ALTER TABLE subscriptions DROP COLUMN plan_subscriber_id;",
        "ALTER TABLE subscriptions DROP COLUMN plan_owner_id;",
        "DROP TABLE subscribers;",
        "DROP TABLE subscription_overages;",
        "DROP TABLE reserved_estimates;",
        "DROP TABLE aggregate_metering_trail;",
        "DROP TABLE aggregate_metering;",
        "DROP TABLE project_run_metering;",
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
