"""
Manual migration: add_original_created_at_column_to_cloud_run_job_tracker
Version: 20250806_132103_fe8deaf9_add_original_created_at_column_to_cloud_run_job_tracker
Created At: 2025-08-06T13:21:03.332774
Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        # Column already exists, so we'll skip this migration
        # The original_created_at column was already added to the database
        "ALTER TABLE cloud_run_job_trackers ADD COLUMN original_created_at TIMESTAMPTZ;"
    ]


def down() -> List[str]:
    return [
        # No rollback needed since column already existed
        "ALTER TABLE cloud_run_job_trackers DROP COLUMN original_created_at;"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
