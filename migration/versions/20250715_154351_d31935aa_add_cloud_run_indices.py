"""
Manual migration: add_cloud_run_indices
Version: 20250715_154351_d31935aa_add_cloud_run_indices
Created At: 2025-07-15T15:43:51.858227
Author: christian
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "CREATE INDEX idx_cloud_run_job_trackers_id_not_deleted ON cloud_run_job_trackers (id, is_deleted)",
        "CREATE INDEX idx_cloud_run_job_trackers_status_not_deleted ON cloud_run_job_trackers (job_status, is_deleted)",
        "CREATE INDEX idx_cloud_run_job_trackers_not_deleted ON cloud_run_job_trackers (is_deleted, created_at)",
        "CREATE INDEX idx_cloud_run_job_trackers_user_active ON cloud_run_job_trackers (user_id, is_deleted, created_at)",
        "CREATE INDEX idx_cloud_run_job_trackers_job_phase_active ON cloud_run_job_trackers (job_phase, is_deleted, job_status, created_at)",
        "CREATE INDEX idx_cloud_run_job_trackers_triggered_jobs ON cloud_run_job_trackers (is_triggered, is_deleted, trigger_topic, created_at)"
    ]


def down() -> List[str]:
    return [
        "DROP INDEX idx_cloud_run_job_trackers_triggered_jobs",
        "DROP INDEX idx_cloud_run_job_trackers_job_phase_active",
        "DROP INDEX idx_cloud_run_job_trackers_user_active",
        "DROP INDEX idx_cloud_run_job_trackers_not_deleted",
        "DROP INDEX idx_cloud_run_job_trackers_status_not_deleted",
        "DROP INDEX idx_cloud_run_job_trackers_id_not_deleted"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
