"""
Manual migration: update_teammembers_constraint
Version: 20250715_143655_5850094b_update_teammembers_constraint
Created At: 2025-07-15T14:36:55.420484
Author: ad<PERSON><PERSON><PERSON><PERSON><PERSON>har
"""

from typing import List, Optional
from sqlalchemy.orm import Session


def up() -> List[str]:
    return [
        "alter table teammembers drop CONSTRAINT fk_teammembers_companies;",
        """ALTER TABLE teammembers 
        ALTER COLUMN company_id TYPE varchar,
        ALTER COLUMN company_id DROP NOT NULL;""",
    ]


def down() -> List[str]:
    return [
       """ALTER TABLE teammembers 
        ALTER COLUMN company_id TYPE varchar,
        ALTER COLUMN company_id SET NOT NULL;""",

        "alter table teammembers add CONSTRAINT fk_teammembers_companies FOREIGN KEY (company_id) REFERENCES companies(id);"
    ]


def data_migration(session: Optional[Session] = None) -> None:
    # Add any data migration logic here if needed
    pass
