name: Docker Build and Push to GCP Artifactory

on:
  push:
    branches:
    - main

  workflow_dispatch:
    inputs:
      environment:
        description: Environment to run tests against
        required: true
        default: staging
        type: choice
        options:
        - staging
        - production
      debug_enabled:
        description: Run with debug logging
        required: false
        type: boolean
        default: false

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-secret-manager
      IMAGE_TAG: latest
      CLOUD_RUN_SERVICE: archie-secret-manager
      CLOUD_RUN_REGION: us-central1

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.SERVICE_ACCOUNT_SERVICE_KEY }}

    - name: Configure Docker for GCP Artifactory
      run: |
        gcloud auth configure-docker $ARTIFACTORY_DOMAIN

    - name: Build Docker image
      run: |
        make

    - name: Install deployment utils
      run: make install-deployment-utils

    - name: Tag Docker image
      run: |
        docker tag $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}

    - name: Push Docker image
      run: |
        echo "Pushing $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG"
        echo "Pushing image with SHA $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}"
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG

    - name: Deploy to Cloud Run
      run: make deploy YAML_FILE=service.yaml tag=${{ github.sha }}

    - name: Notify Slack
      if: always()
      uses: slackapi/slack-github-action@v1.24.0
      env:
        SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      with:
        channel-id: ${{ vars.SLACK_BOT_CHANNEL }}
        slack-message: |
          >>> *Build Status:* ${{ job.status == 'success' && '✅ Success' || '❌ Failure' }}
          *Repository:* blitzy-ai/${{ github.repository }}
          *Branch:* `${{ github.ref_name }}`
          *Triggered by:* ${{ github.actor }}
          *Commit:* <${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }}|${{ github.sha }}> 
          *Action:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Build>
