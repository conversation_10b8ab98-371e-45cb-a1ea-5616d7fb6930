

#   Download Button API Specification

This document outlines the complete Python API specification for the download button functionality, based on the actual codebase architecture and existing patterns.

## 1. Document Types and Current Storage

The download button supports five distinct document types, each with specific storage patterns in the existing system:

### Document Type Mapping

| Document Type | Description | Current Storage Pattern | Version Handling |
|---------------|-------------|------------------------|------------------|
| **summary_of_changes** | Extracted from "0. SUMMARY OF CHANGES" section of tech specs | Derived from tech spec documents | Uses tech spec version (integer) |
| **codebase_context** | Repository structure and code context information | Stored as repo metadata and structure files | Based on head_commit_hash |
| **project_guide** | Generated project guide document | `{blob_name}/Project Guide.md` | Latest version |
| **input_prompt** | Original user input prompt | `{blob_name}/blitzy/documentation/Input Prompt.md` | Project-level |
| **tech_spec** | Technical specification documents | `{blob_name}/blitzy/documentation/Technical Specifications_{tech_spec_id}.md` | Uses TechnicalSpec.version (integer) |

## 2. API Endpoints

### Download Endpoint

```
POST /v1/projects/{project_id}/documents/download
```

**Request Body:**
```json
{
  "document_types": [
    {
      "document_type": "tech_spec|summary_of_changes|codebase_context|project_guide|input_prompt",
      "version": "string (optional)",
      "tech_spec_id": "string (optional, for specific tech spec)"
    }
  ]
}
```

**Response:**
- Single document: File stream (PDF, TXT, MD)
- Multiple documents: ZIP file stream

## 3. Revised Data Model

Based on the actual database schema:

```mermaid
erDiagram
    PROJECT ||--o{ TECHNICAL_SPEC : contains
    PROJECT ||--o{ PROJECT_RUN : has
    TECHNICAL_SPEC {
        string id PK
        string project_id FK
        text prompt
        enum status
        integer version
        enum job_type
        jsonb job_metadata
        datetime created_at
    }
    PROJECT {
        string id PK
        string name
        string repo_url
        string user_id FK
        enum status
    }
    TECH_SPEC_DOCUMENT_CONTEXT {
        string id PK
        string project_id
        string tech_spec_id
        string head_commit_hash
        string repo_name
        jsonb context_metadata
    }
```

## 4. Actual GCS Path Structure

Based on the existing utility functions:

### Tech Specs (NEW_PRODUCT job type)
```
gs://{bucket_name}/blitzy/{repo_url}/blitzy/documentation/Technical Specifications_{tech_spec_id}.md
```

### Tech Specs (Other job types)
```
gs://{bucket_name}/{generated_path_from_job_metadata}/Technical Specifications_{tech_spec_id}.md
```

### Input Prompts
```
gs://{bucket_name}/blitzy/{repo_url}/blitzy/documentation/Input Prompt.md
```

### Project Guides
```
gs://{bucket_name}/{blob_name}/Project Guide.md
```

### Codebase Context Files
```
gs://{bucket_name}/{repo_blitzy_folder_path}/GitHub Repo Files List.json
gs://{bucket_name}/{repo_blitzy_folder_path}/GitHub Repo - metadata.json
```

## 5. Implementation Strategy

### Leverage Existing Infrastructure

1. **Use existing storage utilities:**
   - `src.api.utils.gcs_utils.generate_signed_url()`
   - `src.api.utils.tech_spec_utils.get_tech_spec_presigned_url()`
   - `src.api.routes.storage.download_file()` patterns

2. **Build on existing path generation:**
   - `blitzy_utils.common.generate_technical_spec_document_path()`
   - `src.utils.tech_spec_utils.generate_technical_spec_document_url()`
   - `src.api.utils.project_utils.generate_input_prompt_document_url()`

3. **Use existing version handling:**
   - `TechnicalSpec.version` (integer, auto-incremented)
   - `head_commit_hash` from job metadata for codebase context
   - Latest document approach for project guides

### New Components Needed

1. **Document Type Resolver:**
```python
class DocumentTypeResolver:
    def resolve_document_path(self, project_id: str, document_type: str, 
                            version: Optional[str] = None, 
                            tech_spec_id: Optional[str] = None) -> str:
        # Implementation based on existing patterns
```

2. **ZIP Stream Generator:**
```python
def generate_zip_stream(document_paths: List[str], 
                       company_id: str) -> Iterator[bytes]:
    # Stream ZIP generation using existing GCS utilities
```

3. **Summary of Changes Extractor:**
```python
def extract_summary_of_changes(tech_spec_content: str) -> str:
    # Extract "0. SUMMARY OF CHANGES" section using existing parsing utilities
```

## 6. Error Handling

Leverage existing error patterns:
- `ProjectNotFoundError` (already exists)
- `TechnicalSpecificationNotFoundError` (already exists)
- `FlaskFileNotFound` (already exists)
- Add new: `DocumentTypeNotSupportedError`

## 7. Authentication & Authorization

Use existing patterns:
- `@get_user_info` decorator
- `get_project_by_user_id()` for authorization
- Company-specific bucket access via `get_company_bucket_name()`

## Key Corrections from Original Design

1. **Version is integer, not commit hash** - TechnicalSpec.version is auto-incremented integer
2. **Commit hashes stored separately** - In job_metadata and TechSpecDocumentContext
3. **Existing download infrastructure** - Can build on `/storage/download` patterns
4. **Real document types** - Summary of Changes, Project Guide, Input Prompt are actual concepts
5. **Company-specific buckets** - Not just one GCS bucket, but company-specific ones
6. **Existing path utilities** - Don't need to create new path generation logic

 
