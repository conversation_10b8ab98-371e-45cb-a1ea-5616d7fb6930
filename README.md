# db-common-model
A repository to hold all database common models required by other services

## How to use this library
To use this library, simply add following statement in the `requirements.txt` of your project.
Once you download this library you can start using the models and connection. For a time being all the [models](common_models/models.py) are added here.
```text
git+ssh://**************/blitzy-ai/db-common-model.git@main#egg=db-common-model
```

## Integrating with Cloud Run
Currently, Blitzy is using Google spanner with Postgres dialect as backend database. To work with this setup, Please
follow steps mentioned below.

To connect google spanner we use [PGAdapter](https://github.com/GoogleCloudPlatform/pgadapter). Which we have to
deploy as side container in cloud run. For this we have to deploy cloud run service using yaml. Please refer to
this [example](https://github.com/blitzy-ai/archie-service-backend/blob/main/service.yaml).

Expose following environment variables while deploying cloud run service.
```yaml
env:
  - name: SPANNER_DATABASE_NAME
    value: projects/blitzy-os-dev/instances/blitzy-internal/databases/blitzy-os-db
  - name: ALLOW_TABLE_CREATION
    value: 'false'
```
Note:
Spanner database URL is prepared like this.
```text
/project/<project-id>/instances/<instance-id>/databases/<database-id>
```
To enable CI deployment please follow this [example](https://github.com/blitzy-ai/archie-service-backend/blob/main/.github/workflows/ci-build.yaml).

To deploy this via local, run the following target:
```shell
make deploy tag=latest
```
and then to execute the migration, run the following command:
```shell
gcloud run jobs execute archie-db-migration --region us-east1
```

# Migration

For all Migration scripts you need to export following variable in the environment
```shell
# For dev
export SPANNER_DATABASE_NAME=projects/blitzy-os-dev/instances/blitzy-internal/databases/blitzy-os-db
```

## Generating Migrations
Generate a new migration file:
```shell
# Generate manual migration
python migrate.py generate --name add_user_roles --manual

# Generate automatic migration (detects schema changes)
python migrate.py generate --name add_user_roles --auto
```

## Running Migrations
```shell
# Run all pending migrations
python migrate.py migrate

# Dry run (show what would be executed)
python migrate.py migrate --dry-run
```

## Check status
```shell
# Show migration status
python migrate.py status
```

## Rolling Back
**_Note: This is risky business, better is to create new migration file and apply changes.
Still If it's safe functionality has been provided._**
```shell
# Rollback last migration
python migrate.py rollback

# Rollback multiple migrations
python migrate.py rollback --steps 2
```

## Migration File Structure
```python
def up() -> List[str]:
    return [
        # Your UP migration SQL statements
        """
        CREATE TABLE example (
            id varchar NOT NULL,
            name varchar(100),
            created_at timestamptz
        );
        """
    ]

def down() -> List[str]:
    return [
        # Your DOWN migration SQL statements
        "DROP TABLE example;"
    ]

def data_migration(session: Optional[Session] = None) -> None:
    # Optional: Add any data migration logic
    pass
```

## Error Handling

- Failed migrations are automatically rolled back
- Migration status is recorded in the schema_migrations table
- Errors are reported to Slack through the CI/CD pipeline
- One migration is script is meant to run only once. If something goes wrong please create new migration script.
- In case rollback is difficult for new migration. You can return empty array in `down()` function.

## Features not supported yet.
- Detecting changes in existing columns.
