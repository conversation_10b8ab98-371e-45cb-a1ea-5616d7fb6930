apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: {{SERVICE_NAME}}
  labels:
    managed-by: github-actions
    cloud.googleapis.com/location: {{DEPLOYMENT_REGION}}
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/minScale: '1'
        run.googleapis.com/container-dependencies: '{"archie-secret-manager-1":["pgadapter"]}'
        run.googleapis.com/vpc-access-connector: {{CONNECTOR_NAME}}
        run.googleapis.com/vpc-access-egress: {{EGRESS_SETTING}}
    spec:
      containerConcurrency: 100
      timeoutSeconds: 3600
      serviceAccountName: {{SERVICE_ACCOUNT}}
      volumes:
      - name: sockets-dir
        emptyDir:
          sizeLimit: 50Mi
          medium: Memory
      containers:
      - name: archie-secret-manager-1
        image: {{ARTIFACTORY_REGION}}-docker.pkg.dev/{{PROJECT_ID}}/{{REPOSITORY}}/{{IMAGE_NAME}}:{{IMAGE_TAG}}
        ports:
        - name: http1
          containerPort: {{PORT}}
        env:
        - name: SPANNER_DATABASE_NAME
          value: {{SPANNER_DATABASE_NAME}}
        - name: ALLOW_TABLE_CREATION
          value: '{{ALLOW_TABLE_CREATION}}'
        - name: PROJECT_ID
          value: {{PROJECT_ID}}
        - name: LOG_LEVEL
          value: {{LOG_LEVEL}}
        - name: SERVICE_NAME
          value: {{SERVICE_NAME}}
        - name: GITHUB_WEBHOOK_SECRET
          value: {{GITHUB_WEBHOOK_SECRET}}
        - name: SERVICE_URL_SECRET
          value: {{SERVICE_URL_SECRET}}
        - name: GITHUB_APP_ID
          value: '{{GITHUB_APP_ID}}'
        - name: GITHUB_PRIVATE_KEY
          value: {{GITHUB_PRIVATE_KEY}}
        - name: AZURE_CLIENT_ID
          value: {{AZURE_CLIENT_ID}}
        - name: AZURE_CLIENT_SECRET
          value: {{AZURE_CLIENT_SECRET}}
        resources:
          limits:
            cpu: 1000m
            memory: 2Gi
        startupProbe:
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 1
          tcpSocket:
            port: 8080
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
      - name: pgadapter
        image: gcr.io/cloud-spanner-pg-adapter/pgadapter
        volumeMounts:
        - mountPath: /sockets
          name: sockets-dir
        args:
        - -dir /sockets
        - -x
        startupProbe:
          initialDelaySeconds: 10
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 3
          tcpSocket:
            port: 5432
  traffic:
  - percent: 100
    latestRevision: true
