create table users
(
    id                      varchar      not null primary key,
    user_id                 varchar(50),
    first_name              varchar(100),
    last_name               varchar(100),
    email                   varchar(255) not null,
    company                 varchar(255),
    avatar_blob             text,
    auth_mechanism          varchar(50),
    company_id              varchar,
    country_code            varchar,
    is_verified             bool,
    is_github_authenticated bool default false,
    registration_completed  bool,
    is_deleted              bool,
    deleted_at              timestamptz,
    created_at              timestamptz,
    updated_at              timestamptz,
    job_metadata            jsonb,
    constraint fk_users_company_id foreign key (company_id) references companies (id)
);


create table projects
(
    id                     varchar primary key,
    name                   varchar(255),
    status                 varchar(50) not null,
    repo_prefix            varchar(50),
    repo_url               varchar(1000),
    prompt                 text,
    prompt_status          varchar(50),
    prompt_updated_at      timestamptz,
    user_id                varchar,
    code_gen_submitted     bool,
    project_valid          bool,
    project_invalid_reason text,
    is_disabled            bool,
    disable_reason         text,
    initial_type           varchar,
    is_deleted             bool,
    deleted_at             timestamptz,
    created_at             timestamptz not null,
    updated_at             timestamptz not null,
    constraint fk_projects_users foreign key (user_id) references users (id) on delete cascade,
    constraint fk_projects_teams foreign key (team_id) references teams (id) on delete cascade
);


-- TODO(Chaitanya): depracate this table
create table jobs
(
    id           varchar primary key,
    project_id   varchar,
    stage_type   varchar(50),
    status       varchar(50) not null,
    job_metadata jsonb,
    is_deleted   bool,
    deleted_at   timestamptz,
    created_at   timestamptz not null,
    updated_at   timestamptz not null,
    constraint fk_jobs_projects foreign key (project_id) references projects (id)
);


create table software_requirements
(
    id           varchar primary key,
    status       varchar(50) not null,
    requirements text,
    approved_at  timestamptz,
    link         varchar(255),
    storage_type varchar(50),
    project_id   varchar,
    job_id       varchar,
    is_deleted   bool,
    deleted_at   timestamptz,
    created_at   timestamptz not null,
    updated_at   timestamptz not null,
    constraint fk_software_requirements_projects foreign key (project_id) references projects (id) on delete cascade,
    constraint fk_software_requirements_jobs foreign key (job_id) references jobs (id)
);


create table technical_specs
(
    id                     varchar primary key,
    status                 varchar(50) not null,
    approved_at            timestamptz,
    link                   varchar(255),
    storage_type           varchar(50),
    project_id             varchar,
    job_id                 varchar,
    is_deleted             bool,
    deleted_at             timestamptz,
    created_at             timestamptz not null,
    updated_at             timestamptz not null,
    pdf_report_status      varchar(50),
    version                int,
    job_type               varchar,
    prompt                 text,
    project_invalid_reason text,
    project_run_id         varchar,
    job_metadata           jsonb,
    constraint fk_technical_specs_projects foreign key (project_id) references projects (id) on delete cascade,
    constraint fk_technical_specs_jobs foreign key (job_id) references jobs (id),
    constraint fk_technical_specs_project_run foreign key (project_run_id) references project_runs (id)
);

create table attachment_metadata
(
    id                   varchar primary key,
    file_name            varchar(255) not null,
    mime_type            varchar(100),
    file_size            integer,
    upload_timestamp     timestamptz not null,
    user_description     text,
    status               varchar(50) not null,
    gcs_path             varchar(1000),
    project_id           varchar not null,
    tech_spec_id         varchar,
    uploaded_by_user_id  varchar not null,
    is_deleted           bool,
    deleted_at           timestamptz,
    created_at           timestamptz not null,
    updated_at           timestamptz not null,
    constraint fk_attachment_metadata_projects foreign key (project_id) references projects (id) on delete cascade,
    constraint fk_attachment_metadata_technical_specs foreign key (tech_spec_id) references technical_specs (id) on delete set null,
    constraint fk_attachment_metadata_users foreign key (uploaded_by_user_id) references users (id) on delete cascade
);


create table code_generations
(
    id            varchar primary key,
    status        varchar(50) not null,
    code_repo_url varchar(1000),
    approved_at   timestamptz,
    link          varchar(255),
    storage_type  varchar(50),
    repo_type     varchar(50),
    project_id    varchar,
    job_id        varchar,
    ready_at      timestamptz,
    tech_spec_id  varchar,
    job_metadata  jsonb,
    is_deleted    bool,
    deleted_at    timestamptz,
    created_at    timestamptz not null,
    updated_at    timestamptz not null,
    constraint fk_code_generations_projects foreign key (project_id) references projects (id) on delete cascade,
    constraint fk_code_generations_jobs foreign key (job_id) references jobs (id),
    CONSTRAINT fk_code_generations_tech_spec_id FOREIGN KEY (tech_spec_id) REFERENCES technical_specs (id)
);


create table subscriptions
(
    id                        varchar primary key,
    user_id                   varchar,
    stripe_customer_id        varchar,
    stripe_subscription_id    varchar,
    plan_name                 varchar(100) not null,
    plan_owner_id             varchar not null,
    plan_subscriber_id        varchar not null,
    start_date                timestamptz,
    end_date                  timestamptz,
    status                    varchar(50),
    is_deleted                bool,
    remaining_runs            integer,
    is_trialing               bool,
    has_trial_received        bool,
    quota_lines_generated     integer,
    quota_lines_onboarded     integer,
    charge_lines_generated_overage numeric,
    charge_lines_onboarded_overage numeric,
    deleted_at                timestamptz,
    created_at                timestamptz  not null,
    updated_at                timestamptz  not null,
    trial_start_date          timestamptz,
    trial_end_date            timestamptz,
    current_period_start_date timestamptz,
    current_period_end_date   timestamptz,
    constraint fk_subscriptions_users foreign key (user_id) references users (id) on delete cascade
);

create table subscription_configs
(
    id                                    varchar primary key,
    plan_name                             varchar(50) not null,
    quota_lines_generated_base            integer not null,
    quota_lines_onboarded_base            integer not null,
    charge_lines_generated_overage_base   numeric not null,
    charge_lines_onboarded_overage_base   numeric not null,
    duration                              integer not null,
    duration_unit                         varchar(50) not null,
    description                           text,
    is_deleted                            boolean not null default false,
    deleted_at                            timestamptz,
    created_at                            timestamptz not null,
    updated_at                            timestamptz not null
);

create table subscription_benefits
(
    id                                   varchar primary key,
    subscription_id                      varchar,
    subscription_config_id               varchar,
    quota_lines_generated_delta          integer not null,
    quota_lines_onboarded_delta          integer not null,
    charge_lines_generated_overage_delta numeric not null,
    charge_lines_onboarded_overage_delta numeric not null,
    allotment_type                       varchar(50) not null,
    allotted_by                          varchar,
    is_deleted                           boolean not null default false,
    deleted_at                           timestamptz,
    created_at                           timestamptz not null,
    updated_at                           timestamptz not null,
    constraint fk_subscription_benefits_subscription_id foreign key (subscription_id) references subscriptions (id),
    constraint fk_subscription_benefits_subscription_config_id foreign key (subscription_config_id) references subscription_configs (id)
);

create table subscribers
(
    id                                    varchar primary key,
    plan_subscriber_id                    varchar not null,
    user_id                               varchar not null
);

create table platform_metrics
(
    id                          varchar     not null primary key,
    trial_user_code_submissions int         not null default 0,
    metrics_data                jsonb       not null default '{}'::jsonb,
    created_at                  timestamptz not null,
    updated_at                  timestamptz not null
);

create index idx_job_project_id_phase on jobs (project_id, stage_type);
create index idx_code_gen_submitted on projects (code_gen_submitted);
create index idx_user_id_code_gen_submitted on projects (user_id, code_gen_submitted);
create index idx_softreq_submission on software_requirements (project_id, status, updated_at);
create index idx_techspec_submission on technical_specs (project_id, status, updated_at);
create index idx_attachment_metadata_project_id on attachment_metadata (project_id);
create index idx_attachment_metadata_tech_spec_id on attachment_metadata (tech_spec_id);
create index idx_attachment_metadata_uploaded_by_user_id on attachment_metadata (uploaded_by_user_id);
create index idx_attachment_metadata_status on attachment_metadata (status);
create index idx_attachment_metadata_project_status on attachment_metadata (project_id, status, upload_timestamp);


-- Github installation
create table github_installations
(
    id                    varchar      not null primary key,

    installation_id       varchar(100) not null,
    installation_type     varchar(50),
    status                varchar(50),

    target_name           varchar(255),
    target_id             varchar(100),

    requires_approval     bool,
    requested_by          varchar,
    requested_at          timestamptz,
    approved_by           varchar,
    approved_at           timestamptz,
    user_id               varchar,
    installed_at          timestamptz,
    uninstalled_at        timestamptz,

    repository_selection  varchar,
    selected_repositories jsonb,

    installation_metadata jsonb,
    is_shared             boolean,

    svc_type              varchar(50) not null default "GITHUB",

    created_at            timestamptz  not null,
    updated_at            timestamptz  not null,
    deleted_at            timestamptz,
    is_deleted            bool,
    constraint fk_github_installation_users foreign key (user_id) references users (id) on delete cascade
);

create table pending_installation_requests
(
    id              varchar     not null,
    request_by      varchar     not null primary key,
    installation_id varchar,
    created_at      timestamptz not null,
    updated_at      timestamptz not null,
    constraint fk_pending_installation_requests_user_id foreign key (user_id) references users (id) on delete cascade
);

create index idx_github_installations_installation_id on github_installations (installation_id);

create table geolocations
(
    id           varchar     not null primary key,
    user_id      varchar     not null,
    ip_address   varchar(45),
    country_code varchar(2),
    city         varchar(100),
    region       varchar(100),
    latitude     float8,
    longitude    float8,
    timezone     varchar(50),
    is_deleted   bool,
    deleted_at   timestamptz,
    created_at   timestamptz not null,
    updated_at   timestamptz not null,
    constraint fk_geolocations_users foreign key (user_id) references users (id) on delete cascade
);

create index idx_country_code on geolocations (country_code);

-- Github repositories
create table github_repositories
(
    id                     varchar      not null primary key,

    repo_id                varchar(100),
    repo_name              varchar(255) not null,
    repo_full_name         varchar(255),
    status                 varchar(50),

    project_id             varchar,
    github_installation_id varchar,
    user_id                varchar,

    is_archived            bool,
    onboarded_at           timestamptz,

    previous_names         jsonb,
    repository_metadata    jsonb,

    created_at             timestamptz  not null,
    updated_at             timestamptz  not null,
    deleted_at             timestamptz,
    is_deleted             bool,

    constraint fk_github_repositories_projects foreign key (project_id) references projects (id) on delete cascade,
    constraint fk_github_repositories_github_installations foreign key (github_installation_id) references github_installations (id) on delete cascade,
    constraint fk_github_repositories_user foreign key (user_id) references users (id) on delete cascade
);

create index idx_github_repositories_repo_name on github_repositories (repo_name);

-- Companies table
create table companies
(
    id         varchar     not null primary key,
    name       varchar(100),
    domain     varchar(100),
    status     varchar(50),
    is_default bool default true,
    settings   jsonb,
    is_deleted bool,
    deleted_at timestamptz,
    created_at timestamptz not null,
    updated_at timestamptz not null
);

-- Teams table
create table teams
(
    id            varchar     not null primary key,
    company_id    varchar     not null,
    name          varchar(100),
    is_default    bool default true,
    is_admin_team bool default true,
    settings      jsonb,
    is_deleted    bool,
    deleted_at    timestamptz,
    created_at    timestamptz not null,
    updated_at    timestamptz not null,
    constraint fk_teams_companies foreign key (company_id) references companies (id) on delete cascade,
    constraint fk_teams_projects foreign key (project_id) references projects (id) on delete cascade
);

-- TeamMembers table
create table teammembers
(
    id            varchar     not null primary key,
    company_id    varchar     not null,
    team_id       varchar     not null,
    user_id       varchar     not null,
    role          varchar(50),
    role_settings jsonb,
    joined_at     timestamptz,
    is_deleted    bool,
    deleted_at    timestamptz,
    created_at    timestamptz not null,
    updated_at    timestamptz not null,
    constraint fk_teammembers_teams foreign key (team_id) references teams (id) on delete cascade,
    constraint fk_teammembers_users foreign key (user_id) references users (id) on delete cascade,
    constraint fk_teammembers_companies foreign key (company_id) references companies (id) on delete cascade
);


-- Github repo details
create table github_branch_patterns
(
    id          varchar primary key not null,
    org_name    varchar,
    repo_name   varchar,
    repo_id     varchar,
    company_id  varchar,
    branch_name varchar,
    status      varchar(9),
    is_deleted  boolean default false,
    deleted_at  timestamptz,
    created_at  timestamptz,
    updated_at  timestamptz
);

create table github_project_repo
(
    id                         varchar primary key not null,
    project_id                 varchar,
    org_id                     varchar,
    org_name                   varchar,
    installation_id            varchar,
    repo_name                  varchar,
    repo_id                    varchar,
    branch_name                varchar,
    usage_type                 varchar(6),
    onboarding_commit_hash     varchar,
    current_commit_hash        varchar,
    previous_commit_hash       varchar,
    github_current_commit_hash varchar,
    create_repo                boolean,
    last_scan_at               timestamptz,
    needs_scan                 boolean default false,
    repo_metadata              jsonb,
    branch_lock                boolean,
    is_deleted                 boolean default false,
    deleted_at                 timestamptz,
    created_at                 timestamptz,
    updated_at                 timestamptz,
    azure_project_id           varchar,
    azure_org_id               varchar,
    CONSTRAINT fk_github_project_repo_project_id FOREIGN KEY (project_id) REFERENCES projects (id)
);

create table github_branch_pattern_projects
(
    id         varchar primary key not null,
    pattern_id varchar,
    project_id varchar,
    usage_type varchar(6),
    is_deleted boolean default false,
    deleted_at timestamptz,
    created_at timestamptz,
    updated_at timestamptz,
    constraint fk_github_branch_pattern_projects_project_id foreign key (project_id) references projects (id),
    constraint fk_github_branch_pattern_projects_pattern_id foreign key (pattern_id) references github_branch_patterns (id)
);


-- Project run
create table project_runs
(
    id           varchar primary key not null,
    project_id   varchar             not null,
    code_gen_id  varchar,
    tech_spec_id varchar,
    stage        varchar(16),
    run_type     varchar(16),
    status       varchar(11),
    start_at     timestamptz,
    completed_at timestamptz,
    job_metadata jsonb,
    is_deleted   boolean,
    deleted_at   timestamptz,
    created_at   timestamptz,
    updated_at   timestamptz,

    constraint fk_project_runs_code_gen_id foreign key (code_gen_id) references code_generations (id),
    constraint fk_project_runs_tech_spec_id foreign key (tech_spec_id) references technical_specs (id),
    constraint fk_project_runs_project_id foreign key (project_id) references projects (id)
);


-- Blitzy commits
create table blitzy_commits
(
    id                        varchar     not null primary key,
    project_run_id            varchar     not null,
    code_gen_id               varchar,
    org_name                  varchar,
    repo_name                 varchar,
    repo_id                   varchar,
    repo_url                  varchar,
    branch_name               varchar,
    version_control_system    varchar,
    blitzy_commit_hash        varchar,
    blitzy_commit_url         varchar,
    blitzy_branch_url         varchar,
    status                    varchar     not null,
    original_head_commit_hash varchar,
    pr_number                 integer,
    pr_link                   varchar,
    pr_action                 varchar,
    commit_metadata           jsonb,
    resolved_at               timestamptz,
    created_at                timestamptz not null,
    updated_at                timestamptz not null,
    is_deleted                boolean     not null default false,
    deleted_at                timestamptz,
    constraint fk_blitzy_commits_project_run_id foreign key (project_run_id) references project_runs (id),
    constraint fk_blitzy_commits_code_gen_id foreign key (code_gen_id) references code_generations (id)
);

-- create indexes for foreign keys
create index idx_blitzy_commits_project_run_id on blitzy_commits (project_run_id);
create index idx_blitzy_commits_code_gen_id on blitzy_commits (code_gen_id);

-- optional additional indexes based on common query patterns
create index idx_blitzy_commits_status on blitzy_commits (status);
create index idx_blitzy_commits_org_repo on blitzy_commits (org_name, repo_name);


create table tech_spec_document_contexts
(
    id               varchar     not null primary key,
    company_id       varchar,
    project_id       varchar,
    team_id          varchar,
    tech_spec_id     varchar,
    org_name         varchar,
    repo_name        varchar,
    repo_id          varchar,
    branch_name      varchar,
    branch_id        varchar,
    head_commit_hash varchar,
    context_metadata jsonb,
    created_at       timestamptz not null,
    updated_at       timestamptz not null,
    is_deleted       boolean     not null default false,
    deleted_at       timestamptz,
)


-- cloud run job trackers
create table cloud_run_job_trackers
(
    id                      varchar not null primary key,
    project_id              varchar,
    tech_spec_id            varchar,
    code_gen_id             varchar,
    job_id                  varchar,
    job_phase               varchar(50),
    job_status              varchar(50),
    job_name                varchar,
    event_data              jsonb,
    job_submission_metadata jsonb,
    user_id                 varchar,
    is_triggered            boolean default false,
    trigger_topic           varchar,
    original_created_at     timestamptz,
    is_deleted              boolean,
    deleted_at              timestamptz,
    created_at              timestamptz,
    updated_at              timestamptz,
    constraint fk_cloud_run_job_trackers_project foreign key (project_id) references projects (id),
    constraint fk_cloud_run_job_trackers_user foreign key (user_id) references users (id)
);


CREATE INDEX idx_cloud_run_job_trackers_id_not_deleted ON cloud_run_job_trackers (id, is_deleted);
CREATE INDEX idx_cloud_run_job_trackers_status_not_deleted ON cloud_run_job_trackers (job_status, is_deleted);
CREATE INDEX idx_cloud_run_job_trackers_not_deleted ON cloud_run_job_trackers (is_deleted, created_at);
CREATE INDEX idx_cloud_run_job_trackers_user_active ON cloud_run_job_trackers (user_id, is_deleted, created_at);
CREATE INDEX idx_cloud_run_job_trackers_job_phase_active ON cloud_run_job_trackers (job_phase, is_deleted, job_status, created_at);
CREATE INDEX idx_cloud_run_job_trackers_triggered_jobs ON cloud_run_job_trackers (is_triggered, is_deleted, trigger_topic, created_at);
CREATE INDEX idx_cloud_run_job_trackers_project_id ON cloud_run_job_trackers (project_id);
CREATE INDEX idx_cloud_run_job_trackers_tech_spec_id ON cloud_run_job_trackers (tech_spec_id);
CREATE INDEX idx_cloud_run_job_trackers_code_gen_id ON cloud_run_job_trackers (code_gen_id);
CREATE INDEX idx_cloud_run_job_trackers_job_id ON cloud_run_job_trackers (job_id);
CREATE INDEX idx_cloud_run_job_trackers_job_phase ON cloud_run_job_trackers (job_phase);
CREATE INDEX idx_cloud_run_job_trackers_job_status ON cloud_run_job_trackers (job_status);
CREATE INDEX idx_cloud_run_job_trackers_job_name ON cloud_run_job_trackers (job_name);
CREATE INDEX idx_cloud_run_job_trackers_user_id ON cloud_run_job_trackers (user_id);
CREATE INDEX idx_cloud_run_job_trackers_is_triggered ON cloud_run_job_trackers (is_triggered);
CREATE INDEX idx_cloud_run_job_trackers_trigger_topic ON cloud_run_job_trackers (trigger_topic);
CREATE INDEX idx_cloud_run_job_trackers_original_created_at ON cloud_run_job_trackers (original_created_at);
CREATE INDEX idx_cloud_run_job_trackers_updated_at ON cloud_run_job_trackers (updated_at);
CREATE INDEX idx_cloud_run_job_trackers_status_created_at ON cloud_run_job_trackers (job_status, created_at DESC);
CREATE INDEX idx_cloud_run_job_trackers_status_updated_at ON cloud_run_job_trackers (job_status, updated_at DESC);
CREATE INDEX idx_cloud_run_job_trackers_running_duration ON cloud_run_job_trackers (job_status, updated_at) WHERE job_status = 'RUNNING';
CREATE INDEX idx_cloud_run_job_trackers_user_project ON cloud_run_job_trackers (user_id, project_id);
CREATE INDEX idx_cloud_run_job_trackers_trigger_topic_status ON cloud_run_job_trackers (trigger_topic, job_status);


create table branch_locks
(
    id                varchar primary key not null,
    branch_pattern_id varchar,
    project_repo_id   varchar,
    project_run_id    varchar,
    project_id        varchar,
    lock_reason       varchar,
    is_active         boolean default true,
    locked_at         timestamptz,
    released_at       timestamptz,
    is_deleted        boolean,
    deleted_at        timestamptz,
    created_at        timestamptz,
    updated_at        timestamptz,
    constraint fk_branch_locks_project_repo_id foreign key (project_repo_id) references github_project_repo (id),
    constraint fk_branch_locks_project_run_id foreign key (project_run_id) references project_runs (id),
    constraint fk_branch_locks_branch_pattern_id foreign key (branch_pattern_id) references github_branch_patterns (id),
    constraint fk_branch_locks_project_id foreign key (project_id) references projects (id)
);


-- Github installation access
create table github_installation_access
(
    id                 varchar primary key not null,
    integration_id     varchar,
    access_type        varchar,
    entity_id          varchar,
    role               varchar,
    is_owner           boolean default false,
    granted_by_user_id varchar,
    granted_at         timestamptz,
    is_deleted         boolean,
    deleted_at         timestamptz,
    created_at         timestamptz,
    updated_at         timestamptz,
    CONSTRAINT fk_github_integration_access_granted_by_user_id FOREIGN KEY (granted_by_user_id) REFERENCES users (id),
    CONSTRAINT fk_github_integration_access_integration_id FOREIGN KEY (integration_id) REFERENCES github_installations (id)
);


-- User config
create table user_configs
(
    user_id                        varchar,
    tech_spec_notification_enabled boolean,
    code_gen_notification_enabled  boolean,
    platform_config                jsonb,
    is_deleted                     boolean,
    deleted_at                     timestamptz,
    id                             varchar primary key not null,
    created_at                     timestamptz,
    updated_at                     timestamptz,
    CONSTRAINT fk_user_configs_user_id FOREIGN KEY (user_id) REFERENCES users (id)
);


-- Metering
create table project_run_metering
(
    id                     varchar primary key not null,
    project_run_id         varchar,
    estimated_lines_generated integer,
    estimated_hours_saved  float8,
    files_onboarded        integer,
    lines_onboarded        integer,
    files_touched          integer,
    lines_added            integer,
    lines_edited           integer,
    lines_removed          integer,
    lines_generated        integer, -- lines_added + lines_edited + lines_removed
    hours_saved            float8,
    created_at             timestamptz not null,
    updated_at             timestamptz not null,
    is_deleted             boolean default false,
    deleted_at             timestamptz,

    constraint fk_project_run_metering_project_run FOREIGN KEY (project_run_id) references project_runs (id)
);

create table aggregate_metering
(
    id                     varchar primary key not null,
    entity_type            varchar(50) not null, -- e.g., "PROJECT", "USER", "TEAM", "COMPANY"
    entity_id              varchar not null, -- e.g., project_id, user_id, team_id, company_id
    subscription_id        varchar not null,
    files_onboarded        integer,
    lines_onboarded        integer,
    files_touched          integer,
    lines_added            integer,
    lines_edited           integer,
    lines_removed          integer,
    lines_generated        integer, -- lines_added + lines_edited + lines_removed
    hours_saved            float8,
    created_at             timestamptz not null,
    updated_at             timestamptz not null,
    is_deleted             boolean default false,
    deleted_at             timestamptz,

    constraint fk_aggregate_metering_subscription FOREIGN KEY (subscription_id) references subscriptions (id)
);

-- Aggregate metering trail
create table aggregate_metering_trail
(
    id                     varchar primary key not null,
    aggregate_metering_id  varchar not null,
    project_run_id         varchar not null,
    created_at             timestamptz not null,
    updated_at             timestamptz not null,
    is_deleted             boolean default false,
    deleted_at             timestamptz,

    constraint fk_aggregate_metering_trail_aggregate_metering FOREIGN KEY (aggregate_metering_id) references aggregate_metering (id),
    constraint fk_aggregate_metering_trail_project_run FOREIGN KEY (project_run_id) references project_runs (id)
);


-- Reserved estimates
create table reserved_estimates
(
    id                     varchar primary key not null,
    subscription_id        varchar not null,
    project_id             varchar not null,
    code_gen_id            varchar not null,
    estimated_lines_generated integer not null,
    created_at             timestamptz not null,
    updated_at             timestamptz not null,
    is_deleted             boolean default false,
    deleted_at             timestamptz,

    constraint fk_reserved_estimates_subscription FOREIGN KEY (subscription_id) references subscriptions (id),
    constraint fk_reserved_estimates_project FOREIGN KEY (project_id) references projects (id),
    constraint fk_reserved_estimates_code_gen FOREIGN KEY (code_gen_id) references code_generations (id)
);


-- Subscription overage
create table subscription_overages
(
    id                     varchar primary key not null,
    subscription_id        varchar not null,
    overage_type           varchar(50) not null, -- e.g., "LINES_GENERATED", "LINES_ONBOARDED"
    overage_amount         integer not null,
    payment_status         varchar(50) not null, -- e.g., "PENDING", "PAID"
    created_at             timestamptz not null,
    updated_at             timestamptz not null,
    is_deleted             boolean default false,
    deleted_at             timestamptz,

    constraint fk_subscription_overages_subscription FOREIGN KEY (subscription_id) references subscriptions (id)
);