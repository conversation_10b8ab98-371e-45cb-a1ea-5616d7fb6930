# Chat with Codebase - Backend Design Document

## Overview

This document outlines the design for implementing a conversational AI interface that allows users to chat with and edit their codebase. The feature will leverage the existing code graph infrastructure and vector search capabilities to provide intelligent, context-aware responses about code structure, functionality, and relationships, while also enabling AI-driven code modifications with user oversight and control.

## Current Architecture Analysis

### Existing Infrastructure
- **Code Graph Generator**: Sophisticated system that analyzes codebases and creates Neo4j knowledge graphs
- **Vector Search**: Multiple vector indices for different code elements (files, functions, classes, imports, etc.)
- **LLM Integration**: LangChain integration with multiple LLMs (OpenAI, Anthropic, Google Vertex AI)
- **API Layer**: Flask-based REST API with authentication and authorization
- **Database**: Neo4j graph database with rich semantic relationships

### Key Components
1. **CodeGraphBuilder**: Manages Neo4j connections and graph operations
2. **Vector Indices**: 12+ specialized indices for different code elements
3. **Search Tools**: LangChain tools for querying different aspects of the codebase
4. **Authentication**: JWT-based auth with Firebase integration

## Design Goals

### Primary Objectives
- Enable natural language queries about codebase structure and functionality
- Provide accurate, contextual responses using existing code graph data
- Support real-time conversations with streaming responses
- **Enable AI-driven code editing with user oversight and control**
- **Provide safe, reversible code modifications with approval workflows**
- Maintain security and access control per repository/branch
- Scale to handle multiple concurrent users and large codebases

### User Experience Goals
- Intuitive chat interface similar to ChatGPT/Claude with editing capabilities
- Fast response times (<3 seconds for most queries)
- Rich responses with code snippets, file references, and explanations
- **Interactive code change proposals with diff views and approval controls**
- **Real-time preview of proposed changes before application**
- Conversation history and context retention
- Support for follow-up questions and clarifications
- **Undo/redo functionality for code changes**

## Architecture Design

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Chat API      │    │   Code Graph    │
│   (React/Vue)   │◄──►│   Service       │◄──►│   Service       │
│   + Diff Viewer │    │   + Edit Agent  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   LLM Service   │    │   Neo4j Graph   │
                       │   (LangChain)   │    │   Database      │
                       └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Git Service   │    │   Change        │
                       │   (Branching)   │◄──►│   Management    │
                       └─────────────────┘    └─────────────────┘
```

### Component Details

#### 1. Chat API Service
**Location**: New module `archie-chat-service`
**Technology**: Flask + LangChain + WebSockets
**Responsibilities**:
- Handle chat requests and maintain conversation context
- Route queries to appropriate search tools
- Orchestrate LLM responses with retrieved context
- **Manage code editing workflows and change proposals**
- **Coordinate with Git service for branch management**
- Manage streaming responses
- Handle authentication and authorization

#### 2. Code Edit Agent
**Purpose**: AI agent responsible for making code changes
**Technology**: LangChain + Custom Tools
**Capabilities**:
- Analyze code modification requests
- Generate code changes with proper context
- Validate changes against existing codebase
- Create detailed change proposals with explanations
- Handle multi-file modifications
- Ensure code quality and consistency

#### 3. Change Management Service
**Purpose**: Manage proposed changes and user approval workflow
**Features**:
- Change proposal storage and versioning
- Diff generation and visualization
- User approval/rejection tracking
- Rollback and undo capabilities
- Change impact analysis
- Conflict detection and resolution

#### 4. Git Integration Service
**Purpose**: Handle version control operations for code changes
**Features**:
- Automatic branch creation for changes
- Commit management with descriptive messages
- Merge conflict detection and resolution
- Integration with existing Git workflows
- Support for pull request creation

#### 5. Conversation Manager
**Purpose**: Manage chat sessions and context
**Features**:
- Session management with Redis/in-memory storage
- Conversation history tracking
- Context window management for LLMs
- **Change proposal tracking within conversations**
- User preference handling

#### 6. Query Router
**Purpose**: Intelligently route user queries to appropriate tools
**Logic**:
- Analyze query intent (read vs. edit operations)
- Distinguish between information queries and modification requests
- Select optimal combination of search/edit tools
- Determine search parameters and filters
- **Route to edit agent for modification requests**

#### 7. Response Generator
**Purpose**: Generate comprehensive responses using retrieved context
**Features**:
- Template-based response formatting
- Code snippet extraction and highlighting
- Reference linking to files/functions
- **Interactive change proposal formatting**
- **Diff visualization and explanation**
- Confidence scoring for responses

## Technical Implementation

### API Endpoints

#### Chat Endpoints
```
POST /v1/chat/sessions
- Create new chat session
- Input: { repo_id, branch_id, user_preferences, edit_mode: boolean }
- Output: { session_id, expires_at, working_branch }

POST /v1/chat/sessions/{session_id}/messages
- Send message to chat session
- Input: { message, stream: boolean }
- Output: { response, references, confidence, change_proposals? }

GET /v1/chat/sessions/{session_id}/messages
- Get conversation history
- Output: { messages[], session_info, pending_changes[] }

WebSocket /v1/chat/sessions/{session_id}/stream
- Real-time streaming responses with change proposals
```

#### Code Change Endpoints
```
POST /v1/chat/sessions/{session_id}/changes/propose
- Generate code change proposal
- Input: { modification_request, target_files?, scope }
- Output: { proposal_id, changes[], impact_analysis }

GET /v1/chat/sessions/{session_id}/changes/{proposal_id}
- Get detailed change proposal with diffs
- Output: { proposal, files_changed[], diff_data, explanation }

POST /v1/chat/sessions/{session_id}/changes/{proposal_id}/approve
- Approve and apply changes
- Input: { selected_changes?, commit_message? }
- Output: { commit_hash, applied_changes[], branch_info }

POST /v1/chat/sessions/{session_id}/changes/{proposal_id}/reject
- Reject proposed changes
- Input: { reason?, feedback? }
- Output: { status, alternative_suggestions? }

POST /v1/chat/sessions/{session_id}/changes/rollback
- Rollback recent changes
- Input: { target_commit?, change_ids[] }
- Output: { rollback_commit, reverted_changes[] }
```

#### Management Endpoints
```
GET /v1/chat/repositories/{repo_id}/status
- Check if repository is indexed and ready for chat
- Output: { indexed, last_updated, capabilities }

POST /v1/chat/repositories/{repo_id}/reindex
- Trigger reindexing of repository (admin only)
```

### Data Models

#### Chat Session
```python
class ChatSession:
    session_id: str
    user_id: str
    repo_id: str
    branch_id: str
    working_branch: str  # Separate branch for changes
    head_commit_hash: str
    created_at: datetime
    expires_at: datetime
    edit_mode: bool
    preferences: Dict[str, Any]
    context_window: List[Message]
    pending_changes: List[str]  # Change proposal IDs
```

#### Chat Message
```python
class ChatMessage:
    message_id: str
    session_id: str
    role: Literal["user", "assistant"]
    content: str
    timestamp: datetime
    references: List[CodeReference]
    change_proposals: Optional[List[str]]  # Proposal IDs
    confidence_score: float
    processing_time: float
```

#### Change Proposal
```python
class ChangeProposal:
    proposal_id: str
    session_id: str
    request_description: str
    proposed_changes: List[FileChange]
    impact_analysis: ImpactAnalysis
    status: Literal["pending", "approved", "rejected", "applied"]
    created_at: datetime
    applied_at: Optional[datetime]
    commit_hash: Optional[str]
    explanation: str
    confidence_score: float
```

#### File Change
```python
class FileChange:
    file_path: str
    change_type: Literal["create", "modify", "delete", "rename"]
    original_content: Optional[str]
    new_content: Optional[str]
    diff: str
    line_changes: List[LineChange]
    explanation: str
    dependencies_affected: List[str]
```

#### Impact Analysis
```python
class ImpactAnalysis:
    files_affected: int
    functions_modified: List[str]
    classes_modified: List[str]
    dependencies_broken: List[str]
    tests_affected: List[str]
    risk_level: Literal["low", "medium", "high"]
    recommendations: List[str]
```

#### Code Reference
```python
class CodeReference:
    type: Literal["file", "function", "class", "method", "import"]
    path: str
    name: str
    line_range: Optional[Tuple[int, int]]
    summary: str
    relevance_score: float
```

### Query Processing Pipeline

#### 1. Query Analysis
```python
def analyze_query(message: str) -> QueryIntent:
    """
    Analyze user query to determine intent and extract parameters
    """
    # Use LLM to classify query type (read vs. edit)
    # Extract entities (file names, function names, etc.)
    # Determine scope (specific file vs. entire codebase)
    # Identify question type (how, what, where, why) or action type (add, modify, delete)
    # Detect modification keywords and patterns
```

#### 2. Intent Routing
```python
def route_query_intent(intent: QueryIntent) -> ProcessingStrategy:
    """
    Route query to appropriate processing pipeline
    """
    if intent.is_modification_request:
        return EditProcessingStrategy(intent)
    else:
        return ReadProcessingStrategy(intent)
```

#### 3. Search Strategy Selection (Read Operations)
```python
def select_search_strategy(intent: QueryIntent) -> SearchStrategy:
    """
    Choose optimal combination of search tools based on query intent
    """
    strategies = {
        "file_location": [search_files],
        "function_behavior": [search_functions, search_steps],
        "class_structure": [search_classes, search_methods],
        "dependencies": [search_internal_imports, search_external_imports],
        "architecture": [search_folders, search_files, search_classes]
    }
```

#### 4. Edit Strategy Selection (Modification Operations)
```python
def select_edit_strategy(intent: QueryIntent) -> EditStrategy:
    """
    Choose appropriate editing approach based on modification request
    """
    strategies = {
        "add_function": [analyze_target_file, generate_function, validate_integration],
        "modify_class": [find_class, analyze_dependencies, propose_changes],
        "refactor_code": [analyze_scope, identify_patterns, generate_refactoring],
        "fix_bug": [locate_issue, analyze_root_cause, propose_fix],
        "add_feature": [analyze_requirements, design_implementation, generate_code]
    }
```

#### 5. Context Retrieval
```python
def retrieve_context(strategy: SearchStrategy, query: str) -> RetrievalContext:
    """
    Execute search tools and gather relevant context
    """
    # Execute selected search tools in parallel
    # Rank and filter results by relevance
    # Limit context size to fit LLM window
    # Include code snippets and summaries
    # For edit operations, include dependency analysis
```

#### 6. Change Generation (Edit Operations)
```python
def generate_code_changes(intent: QueryIntent, context: RetrievalContext) -> ChangeProposal:
    """
    Generate code modifications using AI agent
    """
    # Analyze current code structure
    # Generate proposed changes with explanations
    # Validate changes against codebase patterns
    # Perform impact analysis
    # Create detailed diff and documentation
```

#### 7. Response Generation
```python
def generate_response(query: str, context: RetrievalContext, changes: Optional[ChangeProposal]) -> ChatResponse:
    """
    Generate comprehensive response with optional change proposals
    """
    # Format context for LLM prompt
    # Generate response with citations
    # Include change proposals if applicable
    # Extract code references
    # Calculate confidence score
    # Format for interactive display
```

### LangChain Integration

#### Tool Configuration
```python
# Extend existing search tools for chat context
read_tools = [
    search_files,
    search_folders,
    search_functions,
    search_classes,
    search_methods,
    search_internal_imports,
    search_external_imports,
    search_declarations,
    search_type_definitions,
    search_exports,
    # Enhanced read tools
    get_file_content,
    get_function_implementation,
    get_class_hierarchy,
    analyze_dependencies
]

# New editing tools for code modifications
edit_tools = [
    create_file,
    modify_file,
    delete_file,
    rename_file,
    add_function,
    modify_function,
    delete_function,
    add_class,
    modify_class,
    add_import,
    remove_import,
    refactor_code,
    fix_syntax_errors,
    optimize_code,
    generate_tests,
    update_documentation
]

# Combined toolset for the agent
all_tools = read_tools + edit_tools
```

#### Agent Configuration
```python
def create_chat_agent(repo_context: RepoContext, edit_mode: bool = False) -> AgentExecutor:
    """
    Create LangChain agent with code graph tools
    """
    llm = llm_claude_3_5_sonnet  # Primary LLM for reasoning
    code_llm = llm_gpt4_1  # Specialized LLM for code generation

    tools = read_tools if not edit_mode else all_tools

    system_prompt = CODEBASE_EDIT_SYSTEM_PROMPT if edit_mode else CODEBASE_CHAT_SYSTEM_PROMPT

    prompt = ChatPromptTemplate.from_messages([
        ("system", system_prompt),
        ("placeholder", "{chat_history}"),
        ("human", "{input}"),
        ("placeholder", "{agent_scratchpad}")
    ])

    agent = create_tool_calling_agent(llm, tools, prompt)
    return AgentExecutor(
        agent=agent,
        tools=tools,
        verbose=True,
        max_iterations=10,
        early_stopping_method="generate"
    )

def create_edit_agent(repo_context: RepoContext) -> AgentExecutor:
    """
    Create specialized agent for code editing operations
    """
    llm = llm_gpt4_1  # Best for code generation

    edit_prompt = ChatPromptTemplate.from_messages([
        ("system", CODE_EDIT_SYSTEM_PROMPT),
        ("placeholder", "{chat_history}"),
        ("human", "{modification_request}"),
        ("placeholder", "{agent_scratchpad}")
    ])

    agent = create_tool_calling_agent(llm, edit_tools, edit_prompt)
    return AgentExecutor(
        agent=agent,
        tools=edit_tools,
        verbose=True,
        max_iterations=15,
        return_intermediate_steps=True
    )
```

### Streaming Implementation

#### WebSocket Handler
```python
@socketio.on('chat_message')
def handle_chat_message(data):
    """
    Handle real-time chat messages with streaming responses and change proposals
    """
    session_id = data['session_id']
    message = data['message']

    # Validate session and permissions
    session = get_chat_session(session_id)
    if not session or not validate_access(session, current_user):
        emit('error', {'message': 'Unauthorized'})
        return

    # Process message asynchronously
    async def process_and_stream():
        # Analyze query intent
        intent = analyze_query(message)

        if intent.is_modification_request and session.edit_mode:
            # Handle edit request
            async for chunk in edit_agent.astream(message, session.context):
                if chunk.get('type') == 'change_proposal':
                    emit('change_proposal', {
                        'session_id': session_id,
                        'proposal': chunk['proposal'],
                        'requires_approval': True
                    })
                else:
                    emit('response_chunk', {
                        'session_id': session_id,
                        'chunk': chunk,
                        'type': 'partial'
                    })
        else:
            # Handle read-only request
            async for chunk in chat_agent.astream(message, session.context):
                emit('response_chunk', {
                    'session_id': session_id,
                    'chunk': chunk,
                    'type': 'partial'
                })

        emit('response_complete', {
            'session_id': session_id,
            'message_id': save_message(session_id, response)
        })

    asyncio.create_task(process_and_stream())

@socketio.on('approve_changes')
def handle_change_approval(data):
    """
    Handle user approval of proposed changes
    """
    session_id = data['session_id']
    proposal_id = data['proposal_id']
    selected_changes = data.get('selected_changes', [])

    # Validate and apply changes
    try:
        result = apply_changes(session_id, proposal_id, selected_changes)
        emit('changes_applied', {
            'session_id': session_id,
            'commit_hash': result['commit_hash'],
            'applied_changes': result['changes']
        })
    except Exception as e:
        emit('error', {'message': f'Failed to apply changes: {str(e)}'})
```

## Security and Access Control

### Authentication
- Reuse existing JWT-based authentication
- Validate user access to specific repositories
- Support for organization-level permissions

### Authorization
```python
def validate_chat_access(user_id: str, repo_id: str, branch_id: str, edit_mode: bool = False) -> bool:
    """
    Validate user has access to chat with specific repository/branch
    """
    # Check repository access permissions
    # Validate branch access (if applicable)
    # Check organization membership
    # Verify subscription limits
    # For edit mode, check write permissions
    if edit_mode:
        return validate_write_access(user_id, repo_id, branch_id)
    return validate_read_access(user_id, repo_id, branch_id)

def validate_change_approval(user_id: str, session_id: str, proposal_id: str) -> bool:
    """
    Validate user can approve specific change proposal
    """
    # Verify session ownership
    # Check change proposal exists and is pending
    # Validate user has write permissions
    # Check if changes require additional approvals (for sensitive files)
```

### Data Privacy
- No storage of actual code content in chat logs
- Only store references and summaries
- **Change proposals stored temporarily with automatic cleanup**
- **Code changes tracked in Git history with proper attribution**
- Automatic session expiration
- Option to disable chat history
- **Sensitive file protection (config files, secrets, etc.)**

### Rate Limiting
```python
# Per-user rate limits
RATE_LIMITS = {
    "messages_per_minute": 10,
    "sessions_per_hour": 5,
    "tokens_per_day": 100000,
    # Edit-specific limits
    "change_proposals_per_hour": 20,
    "changes_applied_per_day": 50,
    "files_modified_per_session": 25
}
```

## Performance Considerations

### Caching Strategy
- **Session Cache**: Redis for active chat sessions
- **Query Cache**: Cache frequent query results
- **Context Cache**: Cache retrieved code context
- **Response Cache**: Cache similar responses
- **Change Proposal Cache**: Temporary storage for pending changes
- **Git Operation Cache**: Cache branch and commit information

### Optimization Techniques
- **Parallel Search**: Execute multiple search tools concurrently
- **Context Pruning**: Intelligent context window management
- **Lazy Loading**: Load detailed code content only when needed
- **Connection Pooling**: Efficient Neo4j connection management
- **Change Batching**: Group related modifications for efficiency
- **Incremental Analysis**: Only analyze changed files for impact assessment
- **Background Processing**: Pre-compute common change patterns

### Monitoring and Metrics
```python
# Key metrics to track
METRICS = [
    "response_time_p95",
    "query_success_rate",
    "user_satisfaction_score",
    "context_relevance_score",
    "llm_token_usage",
    "concurrent_sessions",
    "error_rate_by_type",
    # Edit-specific metrics
    "change_proposal_accuracy",
    "change_approval_rate",
    "change_rollback_rate",
    "code_quality_score_post_change",
    "edit_session_duration",
    "files_modified_per_session",
    "git_operation_success_rate"
]
```

## Deployment Strategy

### Infrastructure
- **Service**: Deploy as new microservice in existing architecture
- **Database**: Extend existing Neo4j instance
- **Cache**: Redis cluster for session management
- **Load Balancer**: Support for WebSocket connections
- **Git Storage**: Dedicated Git service for change management
- **Backup**: Automated backup of change proposals and session data

### Rollout Plan
1. **Phase 1**: Read-only chat functionality with internal testing
2. **Phase 2**: Limited edit capabilities for trusted users
3. **Phase 3**: Beta release with full editing features to select customers
4. **Phase 4**: General availability with comprehensive safety controls
5. **Phase 5**: Advanced editing features and enterprise integrations

### Safety Measures
- **Sandbox Environment**: Test changes in isolated branches
- **Automatic Backups**: Create restore points before major changes
- **Change Validation**: Syntax checking and basic quality gates
- **Rollback Mechanisms**: Quick revert capabilities for problematic changes
- **Approval Workflows**: Multi-level approval for sensitive modifications

## Future Enhancements

### Advanced Features
- **Code Generation**: Generate code based on natural language descriptions
- **Refactoring Suggestions**: AI-powered refactoring recommendations
- **Documentation Generation**: Auto-generate documentation from conversations
- **Code Review**: AI-assisted code review and suggestions
- **Multi-Repository Chat**: Chat across multiple related repositories
- **Collaborative Editing**: Multiple users working on the same codebase
- **Change Templates**: Pre-defined patterns for common modifications
- **Smart Conflict Resolution**: AI-assisted merge conflict resolution
- **Code Quality Gates**: Automated quality checks before applying changes
- **Learning from Feedback**: Improve suggestions based on user acceptance/rejection

### Integration Opportunities
- **IDE Plugins**: VS Code, IntelliJ integration
- **Slack/Teams Bots**: Chat with codebase from team channels
- **GitHub Integration**: Chat in PR comments and issues
- **Documentation Sites**: Embed chat widget in documentation

## Success Metrics

### Technical Metrics
- Response time < 3 seconds for 95% of queries
- 99.9% uptime for chat service
- Context relevance score > 0.8
- User query success rate > 90%
- **Change proposal accuracy > 85%**
- **Change approval rate > 70%**
- **Zero critical bugs introduced by AI changes**

### Business Metrics
- User engagement (messages per session)
- Feature adoption rate
- Customer satisfaction scores
- Reduction in developer onboarding time
- **Code modification efficiency improvement**
- **Reduction in manual coding time**
- **Developer productivity increase**

## User Control and Safety Features

### Interactive Change Approval
- **Diff Visualization**: Rich, side-by-side diff views with syntax highlighting
- **Selective Application**: Users can approve/reject individual changes within a proposal
- **Change Explanation**: AI provides detailed explanations for each modification
- **Impact Preview**: Show potential effects of changes on related code
- **Confidence Indicators**: Display AI confidence levels for each proposed change

### Safety Mechanisms
- **Sandbox Testing**: All changes tested in isolated environment first
- **Automatic Validation**: Syntax checking, linting, and basic quality gates
- **Rollback Points**: Automatic creation of restore points before changes
- **Change History**: Complete audit trail of all modifications
- **Emergency Stop**: Ability to halt and rollback problematic changes immediately

### User Experience Controls
- **Edit Mode Toggle**: Users can switch between read-only and edit modes
- **Permission Levels**: Granular control over what types of changes are allowed
- **Review Workflows**: Optional human review for sensitive changes
- **Batch Operations**: Group related changes for atomic application
- **Preview Mode**: See changes without applying them

## Conclusion

This design leverages the existing sophisticated code graph infrastructure to provide a powerful chat interface that not only helps developers understand codebases but also enables safe, AI-assisted code modifications. By building on proven components like Neo4j, LangChain, and vector search, we can deliver a robust, scalable solution that provides genuine value to developers.

The comprehensive safety measures, user control mechanisms, and incremental rollout plan ensure that the editing capabilities can be deployed safely in enterprise environments. The modular architecture allows for incremental development and testing, while the comprehensive security and performance considerations ensure the solution can scale to enterprise requirements.

Key differentiators:
- **Dual Mode Operation**: Seamless switching between read and edit modes
- **User-Controlled Changes**: All modifications require explicit user approval
- **Safety-First Design**: Multiple layers of protection against unintended changes
- **Enterprise-Ready**: Comprehensive security, auditing, and rollback capabilities
