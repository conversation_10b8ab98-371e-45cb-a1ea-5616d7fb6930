#!/usr/bin/env python3
"""
Test script for the new attachment system using admin service
"""
import os
from io import Bytes<PERSON>
from werkzeug.datastructures import FileStorage

# Set environment variable for admin service (comment out to test fallback)
os.environ["SERVICE_URL_ADMIN"] = "https://archie-service-admin-464705070478.us-central1.run.app"

# Set GCS bucket name for fallback
os.environ["GCS_BUCKET_NAME"] = "blitzy-os-internal"

from src.service.attachment_service import (
    create_attachment_metadata,
    upload_file_to_gcs,
    download_attachment_from_gcs,
    save_attachment_metadata
)
from common_models.db_client import get_db_session

def test_new_attachment_system():
    """Test the new attachment system with admin service integration"""
    print("🧪 Testing new attachment system with admin service...")
    
    # Create test file
    file_content = "This is a test file for the new attachment system using admin service."
    file_stream = BytesIO(file_content.encode('utf-8'))
    
    file_storage = FileStorage(
        stream=file_stream,
        filename="test-admin-attachment.txt",
        content_type="text/plain"
    )
    
    # Test parameters
    project_id = "test-project-123"
    company_id = "test-company-456"
    user_id = "test-user-789"
    
    try:
        print("📝 Step 1: Creating attachment metadata...")
        attachment = create_attachment_metadata(
            file=file_storage,
            project_id=project_id,
            uploaded_by_user_id=user_id,
            tech_spec_id=None,
            user_description="Test attachment for admin service integration"
        )
        print(f"   ✅ Created attachment metadata: {attachment.id}")
        print(f"   📊 File: {attachment.file_name}")
        print(f"   📊 Size: {attachment.file_size} bytes")
        print(f"   📊 MIME: {attachment.mime_type}")
        
        print("\n📤 Step 2: Uploading file to GCS via admin service...")
        gcs_path = upload_file_to_gcs(
            file=file_storage,
            company_id=company_id,
            project_id=project_id,
            attachment_id=attachment.id
        )
        print(f"   ✅ Uploaded to: {gcs_path}")
        
        # Update attachment with GCS path
        attachment.gcs_path = gcs_path
        
        print("\n💾 Step 3: Saving attachment metadata to database...")
        with get_db_session() as session:
            saved_attachment = save_attachment_metadata(attachment, session)
            session.commit()
        print(f"   ✅ Saved to database: {saved_attachment.id}")
        
        print("\n📥 Step 4: Testing file download...")
        downloaded_data = download_attachment_from_gcs(
            gcs_path=gcs_path,
            company_id=company_id
        )
        print(f"   ✅ Downloaded {len(downloaded_data)} bytes")
        
        # Verify content
        downloaded_content = downloaded_data.decode('utf-8')
        if downloaded_content == file_content:
            print("   ✅ Content verification: PASSED")
        else:
            print("   ❌ Content verification: FAILED")
            print(f"      Expected: {file_content}")
            print(f"      Got: {downloaded_content}")
        
        print("\n🎉 All tests completed successfully!")
        print(f"📊 Test Summary:")
        print(f"   - Attachment ID: {attachment.id}")
        print(f"   - GCS Path: {gcs_path}")
        print(f"   - File Structure: company_{company_id}/attachments_{project_id}/")
        print(f"   - Admin Service Integration: ✅")
        print(f"   - ServiceClient Binary Support: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_bucket_structure():
    """Test the new bucket structure format"""
    print("\n🧪 Testing bucket structure format...")
    
    test_cases = [
        {
            "company_id": "test-company-123",
            "project_id": "project-456",
            "attachment_id": "attachment-789",
            "filename": "document.pdf",
            "expected": "company_test-company-123/attachments_project-456/attachment-789.pdf"
        },
        {
            "company_id": "",  # Default company
            "project_id": "project-456", 
            "attachment_id": "attachment-789",
            "filename": "image.jpg",
            "expected": "attachments_project-456/attachment-789.jpg"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"   Test case {i}: company_id='{case['company_id']}'")
        
        # Simulate the file path creation logic
        file_extension = case['filename'].split('.')[-1] if '.' in case['filename'] else ''
        gcs_file_name = f"{case['attachment_id']}.{file_extension}" if file_extension else case['attachment_id']
        
        if case['company_id']:
            file_path = f"company_{case['company_id']}/attachments_{case['project_id']}/{gcs_file_name}"
        else:
            file_path = f"attachments_{case['project_id']}/{gcs_file_name}"
        
        if file_path == case['expected']:
            print(f"      ✅ PASSED: {file_path}")
        else:
            print(f"      ❌ FAILED:")
            print(f"         Expected: {case['expected']}")
            print(f"         Got: {file_path}")

if __name__ == "__main__":
    print("🚀 Starting attachment system tests with admin service integration...")
    
    # Test bucket structure
    test_bucket_structure()
    
    # Test full system (requires admin service to be running)
    print("\n" + "="*60)
    print("Note: The following test requires the admin service to be running")
    print("If admin service is not available, the test will fail gracefully")
    print("="*60)
    
    success = test_new_attachment_system()
    
    if success:
        print("\n🎉 All tests completed successfully!")
    else:
        print("\n⚠️  Some tests failed. Check the logs above.")
