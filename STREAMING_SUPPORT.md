# ServiceClient Streaming Support

## Overview

The ServiceClient now supports streaming for both synchronous and asynchronous operations, enabling efficient handling of large files and real-time data streams.

## Features Added

### 🔄 Synchronous Streaming Methods

#### `get_stream()`
```python
def get_stream(service_name: str, endpoint: str = "", params: Optional[Dict] = None,
               timeout: Optional[int] = None) -> requests.Response
```

**Use Case**: Download large files or stream data from services
**Example**:
```python
# Download a large file
response = client.get_stream("storage", "/v1/files/download/large-video.mp4")
with open("large-video.mp4", "wb") as f:
    for chunk in response.iter_content(chunk_size=8192):
        f.write(chunk)
```

#### `post_stream()`
```python
def post_stream(service_name: str, endpoint: str = "", 
               data_generator: Optional[Any] = None, json: Optional[Dict] = None,
               params: Optional[Dict] = None, headers: Optional[Dict] = None,
               timeout: Optional[int] = None) -> requests.Response
```

**Use Case**: Upload large files or stream data to services
**Example**:
```python
# Upload a large file in chunks
def file_chunks():
    with open("large-file.bin", "rb") as f:
        while chunk := f.read(8192):
            yield chunk

response = client.post_stream(
    service_name="storage",
    endpoint="/v1/files/upload",
    data_generator=file_chunks(),
    params={'file_path': 'company_123/large-file.bin'},
    headers={'Content-Type': 'application/octet-stream'},
    timeout=1200
)
```

### ⚡ Asynchronous Streaming Methods

#### `async_get_stream()`
```python
async def async_get_stream(service_name: str, endpoint: str = "", params: Optional[Dict] = None,
                          timeout: Optional[int] = None) -> aiohttp.ClientResponse
```

**Use Case**: Asynchronously download large files or stream data
**Example**:
```python
# Async download
response = await client.async_get_stream("storage", "/v1/files/download/large-dataset.csv")
with open("large-dataset.csv", "wb") as f:
    async for chunk in response.content.iter_chunked(8192):
        f.write(chunk)
```

#### `async_post_stream()`
```python
async def async_post_stream(service_name: str, endpoint: str = "", 
                           data_generator: Optional[Any] = None, json: Optional[Dict] = None,
                           params: Optional[Dict] = None, headers: Optional[Dict] = None,
                           timeout: Optional[int] = None) -> aiohttp.ClientResponse
```

**Use Case**: Asynchronously upload large files or stream data
**Example**:
```python
# Async upload with generator
async def async_file_chunks():
    with open("large-file.bin", "rb") as f:
        while chunk := f.read(8192):
            yield chunk

response = await client.async_post_stream(
    service_name="storage",
    endpoint="/v1/files/upload",
    data_generator=async_file_chunks(),
    params={'file_path': 'company_123/large-file.bin'},
    headers={'Content-Type': 'application/octet-stream'}
)
```

## Implementation Details

### Core Changes

1. **Enhanced `_make_request()` method**:
   - Added `stream: bool = False` parameter
   - Passes `stream` parameter to `requests.Session.request()`

2. **Enhanced `_make_async_request()` method**:
   - Added `stream: bool = False` parameter
   - Added `data: Optional[Union[str, bytes]] = None` parameter
   - Conditional response reading based on `stream` parameter

3. **New streaming methods**:
   - `get_stream()` and `post_stream()` for sync operations
   - `async_get_stream()` and `async_post_stream()` for async operations

### Benefits

✅ **Memory Efficient**: Stream large files without loading them entirely into memory
✅ **Real-time Processing**: Handle data streams as they arrive
✅ **Flexible**: Support both sync and async patterns
✅ **Backward Compatible**: Existing methods unchanged
✅ **Authenticated**: All streaming requests include proper authentication
✅ **Error Handling**: Consistent error handling with existing methods

## Usage Patterns

### 1. Large File Download
```python
# Sync
response = client.get_stream("storage", "/download/large-file.zip")
with open("large-file.zip", "wb") as f:
    for chunk in response.iter_content(chunk_size=8192):
        f.write(chunk)

# Async
response = await client.async_get_stream("storage", "/download/large-file.zip")
with open("large-file.zip", "wb") as f:
    async for chunk in response.content.iter_chunked(8192):
        f.write(chunk)
```

### 2. Large File Upload
```python
# Sync with generator
def file_generator():
    with open("large-upload.bin", "rb") as f:
        while chunk := f.read(8192):
            yield chunk

response = client.post_stream(
    "storage", "/upload",
    data_generator=file_generator(),
    headers={'Content-Type': 'application/octet-stream'}
)

# Async with async generator
async def async_file_generator():
    with open("large-upload.bin", "rb") as f:
        while chunk := f.read(8192):
            yield chunk

response = await client.async_post_stream(
    "storage", "/upload",
    data_generator=async_file_generator(),
    headers={'Content-Type': 'application/octet-stream'}
)
```

### 3. Real-time Data Streaming
```python
# Stream processing results
response = client.get_stream("analytics", "/stream/real-time-data")
for chunk in response.iter_content(chunk_size=1024):
    # Process real-time data chunk
    process_data_chunk(chunk)
```

## Testing

Comprehensive test suite added in `tests/test_service_client_streaming.py`:

- ✅ Method existence and signature validation
- ✅ Parameter passing verification
- ✅ Stream parameter support in core methods
- ✅ Sync and async streaming workflows
- ✅ Integration test scenarios
- ✅ Error handling validation

**Test Results**: All 12 streaming tests pass ✅

## Integration with Existing Services

The streaming functionality integrates seamlessly with existing service patterns:

```python
# Works with existing service registration
client = ServiceClient()  # Auto-registers services from environment

# Use with any registered service
response = client.get_stream("admin", "/v1/storage/download")
response = client.post_stream("storage", "/v1/files/upload", data_generator=chunks())

# Async operations
response = await client.async_get_stream("admin", "/v1/storage/download")
response = await client.async_post_stream("storage", "/v1/files/upload", data_generator=async_chunks())
```

## Performance Considerations

- **Chunk Size**: Use appropriate chunk sizes (8192 bytes is a good default)
- **Timeouts**: Set longer timeouts for large file operations
- **Memory Usage**: Streaming keeps memory usage constant regardless of file size
- **Network**: Streaming is more resilient to network interruptions

## Next Steps

The ServiceClient now supports:
1. ✅ Binary uploads (`post_binary`)
2. ✅ Streaming downloads (`get_stream`, `async_get_stream`)
3. ✅ Streaming uploads (`post_stream`, `async_post_stream`)
4. ✅ Full async support with aiohttp
5. ✅ Comprehensive test coverage

This completes the streaming support implementation for the ServiceClient! 🎉
