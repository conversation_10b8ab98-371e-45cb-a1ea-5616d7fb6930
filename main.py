from flask import Flask, jsonify

from src.api.routes.github_operations import github_bp
from src.api.routes.secret_manager import secret_bp
from src.error.base_error import BlitzyError

app = Flask(__name__)

app.register_blueprint(secret_bp)
app.register_blueprint(github_bp)


@app.errorhandler(BlitzyError)
def handle_api_error(error: BlitzyError):
    response = {
        "message": error.message,
    }

    return jsonify(response), error.status_code


if __name__ == "__main__":
    app.run(host="localhost", port=8081, debug=True)
