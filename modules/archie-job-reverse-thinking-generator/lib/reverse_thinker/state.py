from typing import TypedDict, Dict, List

from blitzy_platform_shared.code_generation.models import FileType


class ReverseThinkerState(TypedDict):
    current_file_info: FileType
    dependency_map: Dict[str, FileType]
    file_index: int
    json_retry_count: int
    resume: bool
    retry_count: int
    sorted_files_list: List[str]
    summary_of_changes: str


def get_state(state=ReverseThinkerState) -> ReverseThinkerState:
    return {
        "current_file_info": state["current_file_info"],
        "dependency_map": state["dependency_map"],
        "file_index": state["file_index"],
        "json_retry_count": state["json_retry_count"],
        "resume": state["resume"],
        "retry_count": state["retry_count"],
        "sorted_files_list": state["sorted_files_list"],
        "summary_of_changes": state["summary_of_changes"]
    }
