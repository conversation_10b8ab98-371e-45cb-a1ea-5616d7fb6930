from typing import List

from pydantic import BaseModel, Field
from blitzy_platform_shared.code_generation.models import UpdatedFile, ExcludedItem, FileOrFolderStatus, \
    ThinkingInternalImport, ThinkingExternalImport, ThinkingExport


class UpdatedFileThinking(BaseModel):
    new_internal_imports: List[ThinkingInternalImport] = Field(
        ...,
        description=(
            "NEW internal imports being added to this file during the update. Only includes imports "
            "that don't already exist in the file."
        )
    )
    new_external_imports: List[ThinkingExternalImport] = Field(
        ...,
        description=(
            "NEW external dependencies being introduced to this file. Only includes packages/modules "
            "not previously imported"
        )
    )
    new_exports: List[ThinkingExport] = Field(
        ...,
        description=(
            "NEW exports being added to this module's public API. Only includes exports that don't "
            "already exist. These additions expand what other modules can import from this file."
        )
    )


class UpdatedFileSchema(UpdatedFile, UpdatedFileThinking):
    status: FileOrFolderStatus


class DeletedFileSchema(ExcludedItem):
    status: FileOrFolderStatus
