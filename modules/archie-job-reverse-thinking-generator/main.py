import os
import json
from blitzy_utils.logger import logger
from typing import Dict, Any
from google.cloud.pubsub_v1 import Publisher<PERSON><PERSON>
from langgraph.graph import StateGraph

from blitzy_utils.consts import REPO_STRUCTURE_NAME, REPO_MAPPING_NAME
from blitzy_utils.common import publish_notification, get_repo_blitzy_folder_path, \
    get_repo_documentation_folder_path, get_tech_spec_name, get_existing_product_updated_tech_spec_name, \
    download_text_file_from_gcs_using_admin_service, upload_text_to_gcs_using_admin_service
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_platform_shared.document.utils import get_first_n_headings, parse_sections_at_heading_level
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.common.llms import llm_claude_4_sonnet_med_thinking_high_output, \
    llm_claude_4_sonnet_med_thinking_med_output, llm_claude_4_sonnet_low_thinking_low_output, llm_claude_4_sonnet_min_thinking_min_output

from lib.reverse_thinker.helper import ReverseThinkerHelper, rt_tools
from lib.reverse_thinker.state import ReverseThinkerState
from lib.reverse_thinker.prompts import HEADING_COUNT

EVENT_DATA = os.environ["EVENT_DATA"]
PROJECT_ID = os.environ["PROJECT_ID"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
PRIVATE_BLOB_NAME = os.environ["PRIVATE_BLOB_NAME"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
GENERATE_REVERSE_CODE_TOPIC = os.environ["GENERATE_REVERSE_CODE_TOPIC"]
GITHUB_SECRET_SERVER = os.environ["GITHUB_SECRET_SERVER"]
NEO4J_SERVER = os.environ["NEO4J_SERVER"]
NEO4J_USERNAME = os.environ["NEO4J_USERNAME"]
NEO4J_PASSWORD = os.environ["NEO4J_PASSWORD"]

ANTHROPIC_API_KEY = os.environ["ANTHROPIC_API_KEY"]
OPENAI_API_KEY = os.environ["OPENAI_API_KEY"]
VOYAGE_API_KEY = os.environ["VOYAGE_API_KEY"]
GOOGLE_API_KEY = os.environ["GOOGLE_API_KEY"]

LANGCHAIN_TRACING_V2 = os.environ["LANGCHAIN_TRACING_V2"]
LANGCHAIN_ENDPOINT = os.environ["LANGCHAIN_ENDPOINT"]
LANGCHAIN_API_KEY = os.environ["LANGCHAIN_API_KEY"]
LANGCHAIN_PROJECT = os.environ["LANGCHAIN_PROJECT"]

publisher = PublisherClient()
fallback_llms = [
    llm_claude_4_sonnet_med_thinking_med_output.bind_tools(
        tools=rt_tools
    ),
    llm_claude_4_sonnet_low_thinking_low_output.bind_tools(
        tools=rt_tools
    ),
    llm_claude_4_sonnet_min_thinking_min_output.bind_tools(
        tools=rt_tools
    )
]


def generate_reverse_thinking(event_data: Dict[str, Any], graph_builder: CodeGraphBuilder):
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    org_name = event_data.get('org_name', '')
    user_id = event_data.get('user_id', 'default')
    team_id = event_data.get('team_id', 'default')
    company_id = event_data.get('company_id', 'default')
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_id = event_data.get('branch_id', 'branch_id')
    branch_name = event_data.get('branch_name', 'main')
    head_commit_hash = event_data.get('head_commit_hash', '')
    dest_repo_name = event_data.get('dest_repo_name')
    dest_repo_id = event_data.get('dest_repo_id', 'repo_id')
    dest_branch_id = event_data.get('dest_branch_id', 'branch_id')
    dest_branch_name = event_data.get('dest_branch_name', 'main')
    is_new_dest_repo = event_data.get('is_new_dest_repo', False)
    tech_spec_id = event_data.get('tech_spec_id', "")
    code_gen_id = event_data.get('code_gen_id', '')
    job_type = event_data.get('job_type', '')
    resume = event_data.get('resume', False)
    git_project_repo_id = event_data.get('git_project_repo_id', '')

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "tech_spec_id": tech_spec_id,
        "code_gen_id": code_gen_id,
        "org_name": org_name,
        "repo_id": repo_id,
        "branch_name": branch_name,
        "phase": ProjectPhase.REVERSE_THINKING.value,
        "status": JobStatus.IN_PROGRESS.value,
        "user_id": user_id,
        "git_project_repo_id": git_project_repo_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    document_blob_name = get_repo_documentation_folder_path(
        company_id=company_id,
        team_id=team_id,
        user_id=user_id,
        repo_name=repo_name,
        repo_id=repo_id,
        branch_id=branch_id,
        blob_name=PRIVATE_BLOB_NAME
    )
    blitzy_blob_name = get_repo_blitzy_folder_path(
        company_id=company_id,
        team_id=team_id,
        user_id=user_id,
        repo_name=repo_name,
        repo_id=repo_id,
        branch_id=branch_id,
        blob_name=PRIVATE_BLOB_NAME
    )

    job_metadata = {
        'repo_name': repo_name,
        'repo_id': repo_id,
        'branch_id': branch_id,
        'branch_name': branch_name,
        'company_id': company_id,
        'user_id': user_id,
        'team_id': team_id,
        'project_id': project_id,
        'head_commit_hash': head_commit_hash,
        'document_blob_name': document_blob_name,
        'dest_repo_name': dest_repo_name,
        'dest_repo_id': dest_repo_id,
        'git_project_repo_id': git_project_repo_id,
        'dest_branch_id': dest_branch_id,
        'dest_branch_name': dest_branch_name,
        'is_new_dest_repo': is_new_dest_repo,
        'job_type': job_type
    }

    tech_spec_filename = get_existing_product_updated_tech_spec_name(
        head_commit_hash=head_commit_hash
    )
    if tech_spec_id:
        tech_spec_filename = get_tech_spec_name(
            tech_spec_id=tech_spec_id
        )

    download_blob_path = f"{document_blob_name}/{tech_spec_filename}"
    tech_spec = download_text_file_from_gcs_using_admin_service(
        file_path=download_blob_path,
        company_id=company_id,
    )

    summary_of_changes, tech_spec_n_plus = get_first_n_headings(
        text=tech_spec,
        n=HEADING_COUNT,
        heading_level=1
    )

    tech_spec_n_plus_level_two = parse_sections_at_heading_level(
        text=tech_spec_n_plus,
        heading_level=2
    )
    tech_spec_parsed = tech_spec_n_plus_level_two

    repo_structure_filename = f'{REPO_STRUCTURE_NAME} - {code_gen_id}.json'
    repo_structure_filepath = f"{blitzy_blob_name}/{repo_structure_filename}"
    repo_files_str = download_text_file_from_gcs_using_admin_service(
        file_path=repo_structure_filepath,
        company_id=company_id,
    )
    compressed_files_map = json.loads(repo_files_str)

    repo_map_filename = f"{REPO_MAPPING_NAME} - {code_gen_id}.json"
    state_metadata_filename = f"{REPO_MAPPING_NAME} - Reverse Thinking State Metadata - {code_gen_id}.json"
    file_schemas_filename = f"{REPO_MAPPING_NAME} - File Schemas - {code_gen_id}.json"
    sorted_files_filename = f"{REPO_STRUCTURE_NAME} - Sorted - {code_gen_id}.json"
    dependency_map_filename = f"{REPO_STRUCTURE_NAME} - Dependency Map - {code_gen_id}.json"

    rt_helper = ReverseThinkerHelper(
        generator_llm=llm_claude_4_sonnet_med_thinking_high_output.bind_tools(
            tools=rt_tools
        ),
        fallback_llms=fallback_llms,
        job_metadata=job_metadata,
        blob_name=blitzy_blob_name,
        bucket_name=GCS_BUCKET_NAME,
        compressed_files_map=compressed_files_map,
        head_commit_hash=head_commit_hash,
        github_server=GITHUB_SECRET_SERVER,
        state_metadata_filename=state_metadata_filename,
        file_schemas_filename=file_schemas_filename,
        repo_map_filename=repo_map_filename,
        graph_builder=graph_builder,
        tech_spec_parsed=tech_spec_parsed
    )

    rt_generator: StateGraph = rt_helper.create_graph()
    app = rt_generator.compile()

    initial_state = ReverseThinkerState(
        summary_of_changes=summary_of_changes,
        resume=resume
    )
    result = app.invoke(initial_state, {"recursion_limit": 10000})

    sorted_files_blob_name = f"{blitzy_blob_name}/{sorted_files_filename}"
    upload_text_to_gcs_using_admin_service(
        file_path=sorted_files_blob_name,
        company_id=company_id,
        data=json.dumps(result["files_list"]),
    )

    destination_dependency_map_filepath = f"{blitzy_blob_name}/{dependency_map_filename}"
    upload_text_to_gcs_using_admin_service(
        file_path=destination_dependency_map_filepath,
        company_id=company_id,
        data=json.dumps(result["dependency_map"])
    )

    if propagate:
        event_data["resume"] = False
        publish_notification(publisher, event_data, PROJECT_ID, GENERATE_REVERSE_CODE_TOPIC)

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "tech_spec_id": tech_spec_id,
        "code_gen_id": code_gen_id,
        "org_name": org_name,
        "repo_id": repo_id,
        "branch_name": branch_name,
        "phase": ProjectPhase.REVERSE_THINKING.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "git_project_repo_id": git_project_repo_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": repo_name,
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    return event_data


if __name__ == "__main__":
    logger.info(f"Generating reverse thinking for notification data: {EVENT_DATA}")
    event_data = json.loads(EVENT_DATA)
    repo_name = event_data.get('repo_name')
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', 'default')
    org_name = event_data.get('org_name', '')
    company_id = event_data.get('company_id', 'default')
    repo_id = event_data.get('repo_id', 'default')
    branch_id = event_data.get('branch_id', 'default')
    branch_name = event_data.get('branch_name', 'main')
    head_commit_hash = event_data.get('head_commit_hash', '')
    tech_spec_id = event_data.get('tech_spec_id', "")
    code_gen_id = event_data.get('code_gen_id', '')
    git_project_repo_id = event_data.get('git_project_repo_id', '')

    graph_builder = CodeGraphBuilder(
        uri=NEO4J_SERVER,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash
    )

    try:
        generate_reverse_thinking(event_data=event_data, graph_builder=graph_builder)
    except Exception as e:
        logger.error(f'Failed to generate reverse thinking: {e}')
        notification_data = {
            "projectId": project_id,
            "jobId": job_id,
            "tech_spec_id": tech_spec_id,
            "code_gen_id": code_gen_id,
            "org_name": org_name,
            "repo_id": repo_id,
            "branch_name": branch_name,
            "phase": ProjectPhase.REVERSE_THINKING.value,
            "status": JobStatus.FAILED.value,
            "user_id": user_id,
            "git_project_repo_id": git_project_repo_id,
            "metadata": {
                "propagate": propagate,
                "repo_name": repo_name
            }
        }
        publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)
        raise
    finally:
        graph_builder.close()
