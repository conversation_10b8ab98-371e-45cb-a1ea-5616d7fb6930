import os
import json
import uuid
from typing import List, Dict, Any

from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from google.cloud import storage, pubsub_v1
from github.Repository import Repository

from blitzy_utils.consts import CODE_STRUCTURE_NAME, GITHUB_REPO_PREFIX
from blitzy_utils.common import publish_notification, download_text_file_from_gcs_using_admin_service
from blitzy_utils.enums import ProjectPhase, JobStatus
from blitzy_utils.github import get_github_repo, create_github_commit, create_single_pull_request

from blitzy_platform_shared.code_graph.utils import is_source_adjacent_file, is_source_file
from blitzy_platform_shared.common.utils import archie_exponential_retry
from requests_toolbelt import user_agent

PROJECT_ID = os.environ["PROJECT_ID"]
PLATFORM_EVENTS_TOPIC = os.environ["PLATFORM_EVENTS_TOPIC"]
EVENT_DATA = os.environ["EVENT_DATA"]
GCS_BUCKET_NAME = os.environ["GCS_BUCKET_NAME"]
BLOB_NAME = os.environ["BLOB_NAME"]
GITHUB_SECRET_SERVER = os.environ["GITHUB_SECRET_SERVER"]
GITHUB_APP_ID = os.environ["GITHUB_APP_ID"]
GITHUB_CLIENT_ID = os.environ["GITHUB_CLIENT_ID"]
GITHUB_CLIENT_SECRET = os.environ["GITHUB_CLIENT_SECRET"]

storage_client = storage.Client()
publisher = pubsub_v1.PublisherClient()


def upload_code(event_data_str: str):
    event_data = json.loads(event_data_str)
    repo_name = event_data.get('repo_name')
    dest_repo_name = event_data.get('dest_repo_name', repo_name)
    project_id = event_data.get('project_id', '')
    job_id = event_data.get('job_id', '')
    propagate = event_data.get('propagate', True)
    user_id = event_data.get('user_id', '')
    tech_spec_id = event_data.get('tech_spec_id', '')
    code_gen_id = event_data.get('code_gen_id', '')
    repo_id = event_data.get('repo_id', '')
    git_project_repo_id = event_data.get('git_project_repo_id', '')
    company_id = event_data.get('company_id', '')

    branch_name = f"blitzy-{code_gen_id}"

    logger.info(f"Uploading {dest_repo_name} to GitHub")
    repo, _ = get_github_repo(
        repo_name=dest_repo_name, user_id=user_id, server=GITHUB_SECRET_SERVER,
        repo_id=repo_id, git_project_repo_id=git_project_repo_id,
    )
    pr_data = upload_files_to_github_repo(
        repo=repo, repo_name=repo_name, dest_repo_name=dest_repo_name,
        branch_name=branch_name, user_id=user_id, repo_id=repo_id,
        git_project_repo_id=git_project_repo_id, company_id=company_id, server=GITHUB_SECRET_SERVER
    )

    notification_data = {
        "projectId": project_id,
        "jobId": job_id,
        "phase": ProjectPhase.CODE_GENERATION.value,
        "status": JobStatus.DONE.value,
        "user_id": user_id,
        "tech_spec_id": tech_spec_id,
        "code_gen_id": code_gen_id,
        "git_project_repo_id": git_project_repo_id,
        "metadata": {
            "propagate": propagate,
            "repo_name": dest_repo_name,
            "repo_url": repo.html_url,
            "user_id": user_id,
            "pr_data": pr_data
        }
    }
    publish_notification(publisher, notification_data, PROJECT_ID, PLATFORM_EVENTS_TOPIC)

    return event_data


@archie_exponential_retry()
def upload_files_to_github_repo(
        repo_name: str, dest_repo_name: str, repo: Repository, branch_name: str, user_id: str,
        repo_id: str, git_project_repo_id: str, company_id: str, server: str
):
    bucket_name = GCS_BUCKET_NAME
    folder_prefix = f"{BLOB_NAME}/{repo_name}/"

    logger.info(f"Using admin service to walk bucket structure")
    bucket_results = get_bucket_structure_using_admin_service(bucket_name, company_id, prefix=folder_prefix)

    # Process the results from the service
    for result in bucket_results:
        path = result["path"]
        files = result["files"]
        logger.info(f"Processing path: {path}")
        for file_name in files:
            if (not is_source_file(file_path=file_name) and not is_source_adjacent_file(file_path=file_name)) or \
                    CODE_STRUCTURE_NAME in file_name or GITHUB_REPO_PREFIX in file_name:
                logger.info(f'Skipping file {file_name}')
                continue

            blob_path = f"{file_name}"
            content = download_text_file_from_gcs_using_admin_service(
                file_path=blob_path,
                company_id=company_id,
            )
            github_file_path = blob_path[len(folder_prefix):]

            create_github_commit(
                repo_name=dest_repo_name,
                repo_id=repo_id,
                branch_name=branch_name,
                base_branch=repo.default_branch,
                file_path=github_file_path,
                content=content,
                create_new_branch=True,
                is_new_repo=True,
                head_commit_hash="",
                git_project_repo_id=git_project_repo_id,
                user_id=user_id,
                server=server
            )
            logger.info(f'Processed file {file_name}')

    pr = create_single_pull_request(
        repo=repo,
        head_branch=branch_name,
        base_branch=repo.default_branch,
        pr_title=f"Autonomous product: {dest_repo_name} created by Blitzy",
        pr_body="Includes all files created by Blitzy Agents",
        user_id=user_id,
        git_project_repo_id=git_project_repo_id,
    )
    return pr if isinstance(pr, dict) else pr.raw_data


def get_bucket_structure_using_admin_service(bucket_name: str, company_id: str, prefix: str) -> List[
    Dict[str, Any]]:
    """
    Get bucket structure using the admin service bucket-walk API.

    :param bucket_name: Name of the GCS bucket (not used in service call, handled by service)
    :param company_id: Company ID for the bucket
    :param prefix: Optional prefix to filter the bucket walk
    :return: List of bucket walk results
    """
    logger.info(f"Getting bucket structure for company {company_id} with prefix '{prefix}' using admin service")

    params = {
        "company_id": company_id,
        "prefix": prefix,
    }

    with ServiceClient() as client:
        response = client.get(service_name="admin", endpoint="/v1/storage/bucket-walk", params=params)
        response.raise_for_status()
        response_data = response.json()

        # Extract the results from the API response
        return response_data.get("results", [])


if __name__ == "__main__":
    logger.info(f"Uploading code for notification data: {EVENT_DATA}")
    upload_code(event_data_str=EVENT_DATA)
