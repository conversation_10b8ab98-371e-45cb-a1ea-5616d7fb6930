import json
from typing import Dict, Any, Literal, List

from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage, BaseMessage
from langgraph.prebuilt import Tool<PERSON><PERSON>
from anthropic import BadRequest<PERSON>rror as AnthropicBadRequestError

from blitzy_utils.logger import logger
from blitzy_utils.enums import Backprop<PERSON>ommand
from blitzy_utils.scm import download_repository_to_disk
from blitzy_utils.common import download_text_file_from_gcs_using_admin_service, upload_text_to_gcs_using_admin_service

from blitzy_platform_shared.common.utils import archie_exponential_retry, clean_path, process_tool_call, \
    process_messages_with_tool_call
from blitzy_platform_shared.common.tools import read_file, ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION
from blitzy_platform_shared.common.consts import RETR<PERSON><PERSON><PERSON>_EXCEP<PERSON>ONS
from blitzy_platform_shared.code_graph.builder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er
from blitzy_platform_shared.code_graph.tools import get_folder_contents, search_files, search_folders, get_file_summary
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.document.prompts import SUMMARY_OF_CHANGES_INPUT, \
    TECH_SPEC_SECTIONS_INPUT
from blitzy_platform_shared.code_generation.tools import CREATE_FOLDER_TOOL_NAME, FOLDER_CREATED_SUCCESSFULLY, \
    FOLDER_UPDATED_SUCCESSFULLY, FILE_CREATED_SUCCESSFULLY, FILE_UPDATED_SUCCESSFULLY, FILE_DELETED_SUCCESSFULLY, \
    CREATE_FILE_TOOL_NAME, UPDATE_FILE_TOOL_NAME, create_file, update_file, delete_file, create_folder, update_folder, \
    mark_folder_complete, upload_file_schemas

from .state import ReverseMapperState, get_state
from .prompts import NRS_SYSTEM_PROMPT_TEMPLATE, NRS_PERSONA_PROMPTLET, COMMON_INPUTS_PROMPTLET, NRS_RULES_PROMPTLET, \
    DEST_REPO_NAME_INPUT, ASSIGNED_FOLDER_INPUT, URS_SYSTEM_PROMPT_TEMPLATE, URS_RULES_PROMPTLET, URS_PERSONA_PROMPTLET

rfm_node_tools = [
    get_tech_spec_section,
    get_file_summary,
    get_folder_contents,
    search_folders,
    search_files,
    create_file,
    read_file,
    update_file,
    delete_file,
    create_folder,
    update_folder,
    mark_folder_complete
]
rfm_tools = rfm_node_tools + [ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION]
rfm_tool_node = ToolNode(rfm_node_tools)


class ReverseMapperHelper:
    def __init__(
            self,
            repo_structure_llm: BaseChatModel,
            structure_llm: BaseChatModel,
            fallback_llms: List[BaseChatModel],
            job_metadata: Dict[str, Any],
            storage_client,
            bucket_name: str,
            blob_name: str,
            graph_builder: CodeGraphBuilder,
            file_mapping_filename: str,
            short_repo_structure_filename: str,
            state_metadata_filename: str,
            github_server: str,
            file_schemas_filepath: str
    ):
        self.repo_structure_llm = repo_structure_llm
        self.structure_llm = structure_llm
        self.fallback_llms = fallback_llms
        self.job_metadata = job_metadata
        self.storage_client = storage_client
        self.graph_builder = graph_builder
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.file_mapping_filename = file_mapping_filename
        self.short_repo_structure_filename = short_repo_structure_filename
        self.state_metadata_filename = state_metadata_filename
        self.github_server = github_server
        self.file_schemas_filepath = file_schemas_filepath

        self.company_id = self.job_metadata["company_id"]
        self.team_id = self.job_metadata["team_id"]
        self.user_id = self.job_metadata["user_id"]
        self.project_id = self.job_metadata["project_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.repo_id = self.job_metadata["repo_id"]
        self.branch_name = self.job_metadata["branch_name"]
        self.branch_id = self.job_metadata["branch_id"]
        self.head_commit_hash = self.job_metadata["head_commit_hash"]
        self.dest_repo_name = self.job_metadata["dest_repo_name"]
        self.dest_repo_id = self.job_metadata["dest_repo_id"]
        self.dest_branch_id = self.job_metadata["dest_branch_id"]
        self.dest_branch_name = self.job_metadata["dest_branch_name"]
        self.is_new_dest_repo = self.job_metadata["is_new_dest_repo"]
        self.git_project_repo_id = self.job_metadata["git_project_repo_id"]

        self.generator = self.create_graph()

    def create_graph(self) -> StateGraph:
        # Define the graph
        generator = StateGraph(ReverseMapperState)

        # Add nodes
        generator.add_node("setup", self.setup)
        generator.add_node("generate_new_folder_structure", self.generate_new_folder_structure)
        generator.add_node("generate_updated_folder_structure", self.generate_updated_folder_structure)

        generator.add_conditional_edges(
            "setup",
            self.setup_router,
            {
                "add_feature": "generate_updated_folder_structure",
                "refactor_create_repo": "generate_new_folder_structure",
                "refactor_existing_repo": "generate_updated_folder_structure"
            }
        )

        generator.add_conditional_edges(
            "generate_new_folder_structure",
            self.folder_router,
            {
                "continue": "generate_new_folder_structure",
                "end": END
            }
        )

        generator.add_conditional_edges(
            "generate_updated_folder_structure",
            self.folder_router,
            {
                "continue": "generate_updated_folder_structure",
                "end": END
            }
        )

        # Set the entry point
        generator.add_edge(START, "setup")

        return generator

    def setup(self, state: ReverseMapperState) -> Dict[str, Any]:
        state["repo_id"] = self.repo_id
        state["repo_name"] = self.repo_name
        state["head_commit_hash"] = self.head_commit_hash
        state["team_id"] = self.team_id
        state["user_id"] = self.user_id
        state["dest_repo_name"] = self.dest_repo_name
        state["dest_branch_id"] = self.dest_branch_id
        state["dest_branch_name"] = self.dest_branch_name
        state["dest_repo_id"] = self.dest_repo_id
        state["is_new_dest_repo"] = self.is_new_dest_repo

        if state["resume"]:
            # Restore state
            logger.info('Attempting to resume state')
            try:
                file_mapping_filepath = f"{self.blob_name}/{self.file_mapping_filename}"
                state["file_mapping"] = json.loads(download_text_file_from_gcs_using_admin_service(
                    file_path=file_mapping_filepath,
                    company_id=self.company_id,
                ))

                short_repo_structure_filepath = f"{self.blob_name}/{self.short_repo_structure_filename}"
                state["short_repo_structure"] = json.loads(download_text_file_from_gcs_using_admin_service(
                    file_path=short_repo_structure_filepath,
                    company_id=self.company_id,
                ))

                state_metadata_filepath = f"{self.blob_name}/{self.state_metadata_filename}"
                state_metadata = json.loads(download_text_file_from_gcs_using_admin_service(
                    file_path=state_metadata_filepath,
                    company_id=self.company_id,
                ))
                logger.info(f'Restored state: {state_metadata}')
            except Exception as e:
                logger.warning(f'Failed to resume state, starting afresh')
                state["resume"] = False
                return self.setup(state=state)
            state["pending_folders"] = state_metadata["pending_folders"]
            state["pending_folder_details"] = state_metadata["pending_folder_details"]
            state["visited_folders"] = state_metadata["visited_folders"]
            state["file_mapping"] = state_metadata["file_mapping"]
            state["file_schemas"] = state_metadata["file_schemas"]
            state["short_repo_structure"] = state_metadata["short_repo_structure"]
            state["current_folder_path"] = state_metadata["current_folder_path"]
        else:
            # Reset state, begin with root folder
            state["pending_folders"] = [""]
            state["pending_folder_details"] = {}
            state["visited_folders"] = []
            state["file_mapping"] = {}
            state["file_schemas"] = {}
            upload_file_schemas(
                file_path=self.file_schemas_filepath,
                company_id=self.company_id,
                file_schemas=state["file_schemas"]
            )
            state["short_repo_structure"] = {}
            state["current_folder_path"] = ""

        download_repository_to_disk(
            repo_name=self.repo_name,
            branch_name=self.branch_name,
            user_id=self.user_id,
            server=self.github_server,
            commit_hash=self.head_commit_hash,
            repo_id=self.repo_id,
            git_project_repo_id=self.git_project_repo_id,
        )

        return get_state(state=state)

    def setup_router(self, state: ReverseMapperState) -> Literal["add_feature", "refactor_create_repo", "refactor_existing_repo"]:
        if state["mode"] in [BackpropCommand.REFACTOR_CODE.value, BackpropCommand.CUSTOM.value]:
            if state["is_new_dest_repo"]:
                return "refactor_create_repo"
            else:
                return "refactor_existing_repo"
        else:
            return "add_feature"

    def folder_router(self, state: ReverseMapperState) -> Literal["continue", "end"]:
        if len(state["pending_folders"]) > 0:
            return "continue"
        else:
            return "end"

    def setup_add_feature(self, state: ReverseMapperState) -> Dict[str, Any]:
        return get_state(state=state)

    @archie_exponential_retry()
    def generate_new_folder_structure(self, state: ReverseMapperState) -> Dict[str, Any]:

        state["current_folder_path"] = state["pending_folders"].pop(0)
        folder_path = state["current_folder_path"]
        state["visited_folders"].append(folder_path)
        logger.info(f'generating new repo structure for folder: {"root" if folder_path == "" else folder_path}')

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": NRS_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=NRS_PERSONA_PROMPTLET,
                        inputs=COMMON_INPUTS_PROMPTLET,
                        rules=f"{NRS_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": SUMMARY_OF_CHANGES_INPUT.format(
                        summary=state["tech_spec_first_n"]
                    )
                },
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    )
                },
                {
                    "type": "text",
                    "text": DEST_REPO_NAME_INPUT.format(
                        dest_repo_name=state["dest_repo_name"]
                    )
                },
                {
                    "type": "text",
                    "text": ASSIGNED_FOLDER_INPUT.format(
                        folder_path=folder_path
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ])
        ]

        self.process_folder(
            state=state,
            messages=messages,
            folder_path=folder_path
        )

        logger.info(
            f'finished generating new repo structure for folder: {"root" if state["current_folder_path"] == "" else state["current_folder_path"]}')

        return get_state(state=state)

    @archie_exponential_retry()
    def process_folder(
        self,
        state: ReverseMapperState,
        messages: List[BaseMessage],
        folder_path: str
    ) -> Dict[str, Any]:
        try:
            orig_messages = messages.copy()
            llm = self.repo_structure_llm
            retry_count = 0
            while True:
                messages = orig_messages.copy()
                try:
                    response: AIMessage = llm.invoke(messages)

                    # logger.info(response)

                    while len(response.tool_calls):
                        # fill in the value of local variables
                        messages.append(response)
                        total_tokens = response.usage_metadata["total_tokens"]
                        # logger.info(total_tokens)
                        tool_calls = response.tool_calls
                        tools_config = {
                            "file_mapping": state["file_mapping"],
                            "tech_spec_parsed": state["tech_spec_parsed"],
                            "company_id": self.company_id,
                            "repo_id": self.repo_id,
                            "repo_name": self.repo_name,
                            "branch_name": self.branch_name,
                            "branch_id": self.branch_id,
                            "is_new_dest_repo": self.is_new_dest_repo,
                            "user_id": self.user_id,
                            "head_commit_hash": self.head_commit_hash,
                            "graph_builder": self.graph_builder,
                            "github_server": self.github_server,
                            "git_project_repo_id": self.git_project_repo_id,
                            "current_folder_path": folder_path,
                            "file_schemas_filepath": self.file_schemas_filepath
                        }
                        for tool_call in tool_calls:
                            tool_message = process_tool_call(
                                tool_call=tool_call,
                                tools_list=rfm_node_tools,
                                tools_config=tools_config
                            )
                            tool_name = tool_call["name"]
                            args = tool_call["args"]
                            if tool_message.content in [FOLDER_CREATED_SUCCESSFULLY, FOLDER_UPDATED_SUCCESSFULLY]:
                                target_folder_path = clean_path(args["dest_path"])
                                if tool_name == CREATE_FOLDER_TOOL_NAME:
                                    folder = {
                                        "dest_path": target_folder_path,
                                        "summary": args["summary"],
                                        "source_folders": args["source_folders"],
                                        "status": args["status"]
                                    }
                                else:
                                    folder = {
                                        "dest_path": target_folder_path,
                                        "key_changes": args["key_changes"],
                                        "status": args["status"]
                                    }
                                if target_folder_path not in state["visited_folders"] and target_folder_path not in state["pending_folders"]:
                                    logger.info(f'adding new pending folder: {target_folder_path}')
                                    state["pending_folders"].insert(0, target_folder_path)
                                    state["pending_folder_details"][target_folder_path] = folder
                                else:
                                    logger.warning(f'folder path already visited or pending: {folder_path}')
                            if tool_message.content in [FILE_CREATED_SUCCESSFULLY, FILE_UPDATED_SUCCESSFULLY, FILE_DELETED_SUCCESSFULLY]:
                                target_file_path = clean_path(args["dest_path"])
                                if tool_name == CREATE_FILE_TOOL_NAME:
                                    logger.info(f'adding created file path to repo structure: {target_file_path}')
                                    file = {
                                        "key_changes": args["key_changes"],
                                        "is_dependency_file": args["is_dependency_file"],
                                        "depends_on_files": args["depends_on_files"],
                                        "is_compiled": args["is_compiled"],
                                        "file_type": args["file_type"],
                                        "status": args["status"],
                                        "dest_path": target_file_path,
                                        "summary": args["summary"],
                                        "requirements": args["requirements"],
                                        "source_files": args["source_files"],
                                    }
                                elif tool_name == UPDATE_FILE_TOOL_NAME:
                                    logger.info(f'adding updated file path to repo structure: {target_file_path}')
                                    file = {
                                        "key_changes": args["key_changes"],
                                        "is_dependency_file": args["is_dependency_file"],
                                        "depends_on_files": args["depends_on_files"],
                                        "is_compiled": args["is_compiled"],
                                        "file_type": args["file_type"],
                                        "status": args["status"],
                                        "dest_path": target_file_path
                                    }
                                else:
                                    logger.info(f'adding deleted file path to repo structure: {target_file_path}')
                                    file = {
                                        "reason": args["reason"],
                                        "replaced_by": args["replaced_by"],
                                        "status": args["status"],
                                        "dest_path": target_file_path
                                    }
                                current_files_list = state["short_repo_structure"].get(state["current_folder_path"], [])
                                if target_file_path not in current_files_list:
                                    self.add_file_to_state(state=state, file=file, file_path=target_file_path)
                                else:
                                    logger.warning(f'file path already exists in repo structure: {target_file_path}')
                            messages = process_messages_with_tool_call(
                                tool_message=tool_message,
                                messages=messages,
                                total_tokens_before_tool=total_tokens,
                                llm=llm
                            )
                        # logger.info(f'sending tool response back to llm for file: {file_path}')
                        response: AIMessage = llm.invoke(messages)
                    break
                except AnthropicBadRequestError as e:
                    logger.warning(f'Anthropic bad request error, trying with fallback index {retry_count}: {e}')
                    if retry_count < len(self.fallback_llms):
                        llm = self.fallback_llms[retry_count]
                        retry_count += 1
                    else:
                        logger.error(f'Anthropic bad request error, all fallback llms failed: {e}')
                        raise e
        except RETRYABLE_EXCEPTIONS as e:
            logger.warning(f'encountered error, retrying: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise
        except Exception as e:
            logger.warning(f'encountered error, failing job: {e}')
            state["pending_folders"].insert(0, state["current_folder_path"])
            raise

        logger.info(f"finished processing folder: {folder_path}")

    def upload_state(self, state: ReverseMapperState) -> None:
        destination_file_mapping_filepath = f"{self.blob_name}/{self.file_mapping_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_file_mapping_filepath,
            company_id=self.company_id,
            data=json.dumps(state["file_mapping"])
        )

        destination_short_repo_structure_filepath = f"{self.blob_name}/{self.short_repo_structure_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_short_repo_structure_filepath,
            company_id=self.company_id,
            data=json.dumps(state["short_repo_structure"])
        )

        state_metadata = {
            "pending_folders": state["pending_folders"],
            "pending_folder_details": state["pending_folder_details"],
            "visited_folders": state["visited_folders"],
            "file_mapping": state["file_mapping"],
            "file_schemas": state["file_schemas"],
            "short_repo_structure": state["short_repo_structure"],
            "current_folder_path": state["current_folder_path"]
        }
        destination_state_metadata_filename_filepath = f"{self.blob_name}/{self.state_metadata_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=destination_state_metadata_filename_filepath,
            company_id=self.company_id,
            data=json.dumps(state_metadata)
        )

    @archie_exponential_retry()
    def generate_updated_folder_structure(self, state: ReverseMapperState) -> Dict[str, Any]:
        state["current_folder_path"] = state["pending_folders"].pop(0)
        folder_path = state["current_folder_path"]
        state["visited_folders"].append(folder_path)
        logger.info(f'generating updated repo structure for folder: {"root" if folder_path == "" else folder_path}')

        messages = [
            SystemMessage(content=[
                {
                    "type": "text",
                    "text": URS_SYSTEM_PROMPT_TEMPLATE.format(
                        agent_persona=URS_PERSONA_PROMPTLET,
                        inputs=COMMON_INPUTS_PROMPTLET,
                        rules=f"{URS_RULES_PROMPTLET}"
                    )
                }
            ]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": SUMMARY_OF_CHANGES_INPUT.format(
                        summary=state["tech_spec_first_n"]
                    )
                },
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    )
                },
                {
                    "type": "text",
                    "text": DEST_REPO_NAME_INPUT.format(
                        dest_repo_name=state["dest_repo_name"]
                    )
                },
                {
                    "type": "text",
                    "text": ASSIGNED_FOLDER_INPUT.format(
                        folder_path=folder_path
                    ),
                    "cache_control": {"type": "ephemeral"}
                }
            ])
        ]

        self.process_folder(
            state=state,
            messages=messages,
            folder_path=folder_path
        )

        logger.info(
            f'finished generating updated repo structure for folder: {"root" if state["current_folder_path"] == "" else state["current_folder_path"]}')

        return get_state(state=state)

    def add_file_to_state(self, state: ReverseMapperState, file, file_path):
        file_mapping_folder_path = state["file_mapping"].get(state["current_folder_path"], [])
        file_mapping_folder_path.append(file)
        state["file_mapping"][state["current_folder_path"]] = file_mapping_folder_path
        repo_structure_folder_path = state["short_repo_structure"].get(state["current_folder_path"], [])
        repo_structure_folder_path.append(file_path)
        state["short_repo_structure"][state["current_folder_path"]] = repo_structure_folder_path
        state["file_schemas"][file_path] = file
        upload_file_schemas(
            file_path=self.file_schemas_filepath,
            company_id=self.company_id,
            file_schemas=state["file_schemas"]
        )
