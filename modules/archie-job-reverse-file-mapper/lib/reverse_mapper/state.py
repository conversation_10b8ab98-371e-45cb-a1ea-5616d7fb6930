from typing import TypedDict, List, Dict, Any


class ReverseMapperState(TypedDict):
    current_folder_path: str
    dest_branch_id: str
    dest_branch_name: str
    dest_repo_id: str
    dest_repo_name: str
    file_mapping: Dict[str, List[Dict[str, Any]]]
    file_schemas: Dict[str, Dict[str, Any]]
    head_commit_hash: str
    is_new_dest_repo: bool
    mode: str
    pending_folders: List[str]
    pending_folder_details: Dict[str, Dict[str, Any]]
    repo_id: str
    repo_name: str
    resume: bool
    short_repo_structure: Dict[str, List[str]]
    tech_spec_first_n: str
    tech_spec_parsed: Dict[str, str]
    user_id: str
    visited_folders: List[str]


def get_state(state: ReverseMapperState):
    return {
        "current_folder_path": state["current_folder_path"],
        "dest_branch_id": state["dest_branch_id"],
        "dest_branch_name": state["dest_branch_name"],
        "dest_repo_id": state["dest_repo_id"],
        "dest_repo_name": state["dest_repo_name"],
        "file_mapping": state["file_mapping"],
        "file_schemas": state["file_schemas"],
        "head_commit_hash": state["head_commit_hash"],
        "is_new_dest_repo": state["is_new_dest_repo"],
        "mode": state["mode"],
        "pending_folders": state["pending_folders"],
        "pending_folder_details": state["pending_folder_details"],
        "repo_id": state["repo_id"],
        "repo_name": state["repo_name"],
        "resume": state["resume"],
        "short_repo_structure": state["short_repo_structure"],
        "tech_spec_first_n": state["tech_spec_first_n"],
        "tech_spec_parsed": state["tech_spec_parsed"],
        "user_id": state["user_id"],
        "visited_folders": state["visited_folders"]
    }
