from typing import Dict, List, Any, Set

import networkx as nx

from blitzy_utils.logger import logger

from .models import FileSchemaType, FileOrFolderStatus, ThinkingInternalImport, FileType
from .consts import FILE_PATH_NOT_FOUND, FILE_ERROR_RESPONSE


def prepend_line_numbers(file_content: str) -> str:
    """
    Prepends line numbers to each line of the file content, starting at 1.

    Args:
        file_content: Content of the file as a string

    Returns:
        String with line numbers prepended to each line
    """
    if not file_content:
        return file_content

    lines = file_content.split('\n')
    numbered_lines = [f"{i+1}: {line}" for i, line in enumerate(lines)]
    return '\n'.join(numbered_lines)


def create_dependency_sorted_list(file_schemas: Dict[str, Dict[str, Any]]) -> List[str]:
    """
    Creates a sorted list of files based on their dependencies.
    Files with no dependencies appear first, followed by files that depend on them.

    Args:
        file_schemas: Dictionary containing file information

    Returns:
        List of file paths sorted by dependency order
    """

    # Create a directed graph
    dep_graph = nx.DiGraph()

    # Add all files as nodes
    for file_path in file_schemas.keys():
        dep_graph.add_node(file_path)

    # Add edges based on internal imports
    deleted_files = []
    for file_path, file_info in file_schemas.items():
        if file_info["status"] == FileOrFolderStatus.DELETED.value:
            deleted_files.append(file_path)
            continue
        for dep_path in file_info["depends_on_files"]:
            # Add edge from dependency to file
            # If A depends on B, create edge B → A
            # This ensures B comes before A in topological sort
            if dep_path in file_schemas:  # Check if dependency exists
                dep_graph.add_edge(dep_path, file_path)
            else:
                logger.warning(f"Dependency {dep_path} not found in file_schemas")

    try:
        # Perform topological sort
        # This will raise NetworkXUnfeasible if there's a circular dependency
        sorted_files = list(nx.topological_sort(dep_graph))
        return sorted_files + deleted_files
    except nx.NetworkXUnfeasible:
        logger.error("Circular dependency found")
        raise ValueError("Circular dependency detected in the files")


def will_create_circular_dependency(
    file_path: str,
    source_file_path: str,
    file_schemas: FileSchemaType
) -> bool:
    """
    Checks if importing source_file_path into file_path will create a circular dependency.
    Uses DFS to traverse the dependency graph starting from source_file_path.

    Args:
        file_path: The file that wants to import
        source_file_path: The file being imported
        file_schemas: The complete file schemas dictionary

    Returns:
        True if importing would create a circular dependency, False otherwise
    """
    def find_cycle(current_path: List[str], visited: Set[str], current_file: str) -> bool:
        # If we've reached the original file_path, we have a cycle
        if current_file == file_path:
            logger.warning(f"Circular dependency detected: {' -> '.join(current_path + [current_file])}")
            return True

        # If already visited this file in another path, no cycle found from here
        if current_file in visited:
            return False

        visited.add(current_file)
        current_path.append(current_file)

        internal_imports: List[ThinkingInternalImport] = get_property(
            file_path=current_file,
            file_schemas=file_schemas,
            updated_file_property='new_internal_imports',
            default_property='internal_imports'
        )

        # Traverse each internal import
        for import_info in internal_imports:
            dep_path = import_info['source_file_path']
            if find_cycle(current_path.copy(), visited, dep_path):
                return True

        current_path.pop()
        return False

    # Start the search from source_file_path to see if it leads back to file_path
    visited = set()
    return find_cycle([], visited, source_file_path)


def get_property(
    file_path: str,
    file_schemas: FileSchemaType,
    updated_file_property: str,
    default_property: str
):
    file_schema = file_schemas.get(file_path)
    if not file_schema:
        logger.warning(f'Attempted to retrieve a property from a file schema that does not exist: {file_path}')
        return FILE_ERROR_RESPONSE.format(file_path=file_path, message=FILE_PATH_NOT_FOUND)
    file_status = file_schema["status"]

    if file_status == FileOrFolderStatus.UPDATED.value:
        return file_schema[updated_file_property]
    else:
        return file_schema[default_property]


def set_property(
    file_path: str,
    file_schemas: FileSchemaType,
    updated_file_property: str,
    default_property: str,
    value: Any
):
    file_schema = file_schemas.get(file_path)
    if not file_schema:
        logger.error(
            f'Attempted to set a property {default_property} / {updated_file_property} when file_schema not available')
        return file_schemas
    file_status = file_schema["status"]

    if file_status == FileOrFolderStatus.UPDATED.value:
        file_schema[updated_file_property] = value
    else:
        file_schema[default_property] = value
    file_schemas[file_path] = file_schema
    return file_schemas


def rebuild_files_map(file_schemas: FileSchemaType) -> Dict[str, List[FileType]]:
    """
    Rebuild files_map from file_schemas.

    Args:
        file_schemas: Dict mapping file paths to file dicts

    Returns:
        files_map: Dict mapping folder paths to lists of file dicts
    """
    files_map: Dict[str, List[FileType]] = {}

    for file_path, file_dict in file_schemas.items():
        # Extract folder path from file path
        # For root files like "Dockerfile", folder will be ""
        if "/" in file_path:
            folder_path = "/".join(file_path.split("/")[:-1])
        else:
            folder_path = ""  # Root folder

        # Initialize folder in files_map if not exists
        if folder_path not in files_map:
            files_map[folder_path] = []

        # Add file dict to the appropriate folder
        files_map[folder_path].append(file_dict)

    return files_map
