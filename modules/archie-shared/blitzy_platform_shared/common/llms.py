from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
from langchain_google_vertexai import ChatVertexAI

llm_o3_mini = ChatOpenAI(
    model="o3-mini", max_tokens=32000, timeout=3600, reasoning_effort="high"
)

llm_o3 = ChatOpenAI(
    model="o3-2025-04-16", max_tokens=None, timeout=3600, reasoning_effort="high"
)

llm_o4_mini = ChatOpenAI(
    model="o4-mini", max_tokens=None, timeout=3600, reasoning_effort="high"
)

llm_claude_3_5_sonnet = ChatAnthropic(
    model="claude-3-5-sonnet-20241022", max_tokens=8192, temperature=0, timeout=3600
)

llm_claude_3_7_sonnet_max_thinking_max_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 64000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_high_thinking_max_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 48000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_high_thinking_high_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 47999},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_med_thinking_max_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 32000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_med_thinking_high_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 32000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_med_thinking_med_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=32000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 31999},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_low_thinking_max_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 16000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_low_thinking_high_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 16000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_low_thinking_med_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=32000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 16000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_low_thinking_low_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=16000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 15999},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_min_thinking_max_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_min_thinking_high_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_min_thinking_med_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=32000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_min_thinking_low_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=16000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_min_thinking_min_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=8192,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_max_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=64000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_high_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=48000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_med_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=32000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_low_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=16000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_3_7_sonnet_min_output = ChatAnthropic(
    model="claude-3-7-sonnet-20250219",
    max_tokens=8192,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "output-128k-2025-02-19,token-efficient-tools-2025-02-19"
    },
)

llm_claude_4_sonnet_max_thinking_max_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 64000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_high_thinking_max_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 48000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_high_thinking_high_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 47999},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_med_thinking_max_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 32000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_med_thinking_high_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 32000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_med_thinking_med_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=32000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 31999},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_low_thinking_max_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 16000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_low_thinking_high_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 16000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_low_thinking_med_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=32000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 16000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_low_thinking_low_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 15999},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_min_thinking_max_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=64000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_min_thinking_high_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=48000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_min_thinking_med_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=32000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_min_thinking_low_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_min_thinking_min_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=8192,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 8000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_max_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=64000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_high_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=48000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_med_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=32000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_low_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=16000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_sonnet_min_output = ChatAnthropic(
    model="claude-sonnet-4-20250514",
    max_tokens=8192,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_1_opus_med_thinking_max_output = ChatAnthropic(
    model="claude-opus-4-1-20250805",
    max_tokens=32000,
    temperature=1,
    timeout=3600,
    thinking={"type": "enabled", "budget_tokens": 16000},
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)


llm_claude_4_1_opus_max_output = ChatAnthropic(
    model="claude-opus-4-1-20250805",
    max_tokens=32000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_claude_4_1_opus_med_output = ChatAnthropic(
    model="claude-opus-4-1-20250805",
    max_tokens=16000,
    temperature=0,
    timeout=3600,
    default_headers={
        "anthropic-beta": "interleaved-thinking-2025-05-14,computer-use-2025-01-24"
    },
    service_tier="auto",
)

llm_o1 = ChatOpenAI(model="o1", max_tokens=32768, reasoning_effort="high", timeout=3600)

llm_gpt4o = ChatOpenAI(
    model="gpt-4o-2024-11-20", max_tokens=32768, timeout=3600, temperature=0
)

llm_gpt4_1 = ChatOpenAI(
    model="gpt-4.1-2025-04-14", max_tokens=32768, timeout=3600, temperature=0
)

llm_gpt4_1_nomax = ChatOpenAI(
    model="gpt-4.1-2025-04-14", max_tokens=None, timeout=3600, temperature=0
)

llm_gemini_flash = ChatVertexAI(
    model="gemini-2.0-flash-001", temperature=0, max_tokens=65535
)

llm_gemini_2_5_pro = ChatVertexAI(
    model="gemini-2.5-pro",
    temperature=0,
    max_tokens=65535,
    thinking_budget=32768,
    location="us-central1",
)
