import logging
import async<PERSON>
import json
from typing import List, Union, Dict, Any
from functools import wraps

from thefuzz import process
from langchain_anthropic import Chat<PERSON>nt<PERSON>
from langchain_google_vertexai import ChatVertexAI
from langchain_openai import ChatOpenAI
from langchain_core.messages import ToolMessage, AIMessage, BaseMessage, SystemMessage, HumanMessage, ToolCall
from langchain_core.tools import BaseTool
from langchain_core.language_models.chat_models import BaseChatModel
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential, \
    before_sleep_log
from pydantic import ValidationError

from blitzy_utils.logger import logger

from .models import FormattingError
from .consts import MAX_TOKEN_LIMIT_KEYWORD, DEFAULT_MAX_RETRIES, \
    DEFAULT_MAX_WAIT, DEFAULT_MIN_WAIT, DEFAULT_MULTIPLIER, RETRYABLE_EXCEPTIONS, \
    CONTEXT_200K, CONTEXT_COMPRESSION_THRESHOLD
from .prompts import MESSAGE_SUMMARY_AGENT_PERSONA_PROMPT, MESSAGE_SUMMARY_SYSTEM_PROMPT_TEMPLATE, MESSAGE_SUMMARY_INPUTS, \
    MESSAGE_SUMMARY_RULES_PROMPTLET, MESSAGE_SEQUENCE_INPUT
from .llms import llm_gemini_2_5_pro


def get_formatted_tool_result_messages(
    tool_message_list: List[ToolMessage],
    total_tokens: int,
    llm: Union[ChatOpenAI, ChatAnthropic, ChatVertexAI],
    validate_token_count=True,
    model_context=CONTEXT_200K,
    is_error=False
) -> List[ToolMessage]:
    messages = []
    tool_call_tokens = 0

    for index, tool_message in enumerate(tool_message_list):

        tool_content: str = tool_message.content
        if validate_token_count:
            if tool_content:
                tool_call_tokens = llm.get_num_tokens(tool_content)
                logger.debug(f'Tool call added {tool_call_tokens} tokens')
                content_tokens = total_tokens + tool_call_tokens
                logger.debug(f'Total context window tokens so far: {content_tokens}')
                max_tokens = model_context - llm.max_tokens
                i = 0
                num_chars = [25000, 10000, 5000, 2500, 1000, 500]
                is_error = False
                while content_tokens >= max_tokens and i < len(num_chars):
                    num_char = num_chars[i]
                    content = f"WARNING: Tool result truncated, last {num_char} characters: " + tool_content[-num_char:]
                    tool_call_tokens = llm.get_num_tokens(content)
                    content_tokens = total_tokens + tool_call_tokens
                    max_tokens = model_context - llm.max_tokens
                    if content_tokens < max_tokens:
                        is_error = False
                        tool_content = content
                        break
                    is_error = True
                    i += 1
                if is_error:
                    logger.warning('Max token limit reached, asking the llm to abort tool calling')
                    tool_content = f"{MAX_TOKEN_LIMIT_KEYWORD}. Please wrap up and generate your final output now."
            else:
                tool_call_tokens = 0
                content_tokens = total_tokens
                tool_content = ""

        tool_content_block = {
            "type": "text",
            "text": tool_content
        }

        if is_error:
            tool_content_block["is_error"] = True

        messages.append(
            ToolMessage(
                content=[
                    tool_content_block
                ],
                tool_call_id=tool_message.tool_call_id,
                name=tool_message.name
            )
        )
    return messages


def process_messages_with_tool_call(
    tool_message: ToolMessage,
    messages: List[BaseMessage],
    total_tokens_before_tool: int,
    llm: Union[ChatOpenAI, ChatAnthropic, ChatVertexAI],
    model_context=CONTEXT_200K,
    is_error=False
):
    final_messages = messages.copy()

    tool_content = tool_message.content
    tool_call_tokens = llm.get_num_tokens(tool_content)
    total_tokens_after_tool = total_tokens_before_tool + tool_call_tokens

    if len(final_messages) < 4 or total_tokens_after_tool < (CONTEXT_COMPRESSION_THRESHOLD * model_context):
        return get_formatted_messages_with_tool_reponse(
            tool_message=tool_message,
            messages=final_messages,
            total_tokens_before_tool=total_tokens_before_tool,
            tool_call_tokens=tool_call_tokens,
            llm=llm,
            model_context=model_context,
            is_error=is_error
        )

    summary_start_msg_index = get_second_ai_or_human_message_index(messages=final_messages)
    if summary_start_msg_index == -1:
        logger.warning(f'Could not summarize context as second ai or human message index not found')
        return get_formatted_messages_with_tool_reponse(
            tool_message=tool_message,
            messages=final_messages,
            total_tokens_before_tool=total_tokens_before_tool,
            tool_call_tokens=tool_call_tokens,
            llm=llm,
            model_context=model_context,
            is_error=is_error
        )

    mid_msgs = final_messages[summary_start_msg_index:-1]
    mid_msg_content = ""
    for mi, mid_msg in enumerate(mid_msgs):
        mid_msg_content += f"Message {mi} of {len(mid_msgs)}\n"
        mid_msg_content += f"Message type: {mid_msg.type}\n"
        mid_msg_content += f"Message text\n{json.dumps(get_response_content(response=mid_msg, include_thinking=True))}\n"
        if mid_msg.type == "ai":
            assert isinstance(mid_msg, AIMessage)
            if len(mid_msg.tool_calls):
                mid_msg_content += f"Tool Calls\n"
            for i, msg_tool_call in enumerate(mid_msg.tool_calls):
                mid_msg_content += f"{i}: {json.dumps(msg_tool_call)}\n"
        mid_msg_content += f"\n"
    if not mid_msg_content or llm.get_num_tokens(json.dumps(mid_msg_content)) <= llm.max_tokens:
        logger.warning(f'Context compression is not supported for edge cases with ultra large final tool call')
        return get_formatted_messages_with_tool_reponse(
            tool_message=tool_message,
            messages=final_messages,
            total_tokens_before_tool=total_tokens_before_tool,
            tool_call_tokens=tool_call_tokens,
            llm=llm,
            model_context=model_context,
            is_error=is_error
        )

    summary_message = summarize_messages(
        messages_to_summarize=mid_msg_content
    )
    final_messages = messages[0:summary_start_msg_index] + \
        [HumanMessage(
            content=[
                {
                    "type": "text",
                    "text": summary_message
                }
            ]
        )] + \
        final_messages[-1:]

    return get_formatted_messages_with_tool_reponse(
        tool_message=tool_message,
        messages=final_messages,
        total_tokens_before_tool=total_tokens_before_tool,
        tool_call_tokens=tool_call_tokens,
        llm=llm,
        model_context=model_context,
        is_error=is_error
    )


def process_tool_call(
    tool_call: ToolCall,
    tools_list: List[BaseTool],
    tools_config: Dict[str, Any]
) -> ToolMessage:
    tool_name = tool_call["name"]
    tool_call["args"]["config"] = {}
    selected_tool = None
    for tool in tools_list:
        if tool.name == tool_name:
            selected_tool = tool
    if not selected_tool:
        logger.warning("Invalid tool called, tool name not found in tools list")
        return ToolMessage(
            content="Invalid tool / function called. Select the correct tool / function from the list of tools /functions provided to you.",
            tool_call_id=tool_call["id"],
            name=tool_call["name"]
        )

    try:
        return selected_tool.invoke(input=tool_call, config={
            "configurable": tools_config
        })
    except ValidationError as e:
        return ToolMessage(
            content=e.json(),
            tool_call_id=tool_call["id"],
            name=tool_call["name"]
        )


def get_formatted_messages_with_tool_reponse(
    tool_message: ToolMessage,
    messages: List[BaseMessage],
    total_tokens_before_tool: int,
    tool_call_tokens: int,
    llm: Union[ChatOpenAI, ChatAnthropic, ChatVertexAI],
    model_context=CONTEXT_200K,
    is_error=False
):
    final_messages = messages.copy()
    tool_content: str = tool_message.content
    if tool_content:
        logger.info(f'Tool call added {tool_call_tokens} tokens')
        content_tokens = total_tokens_before_tool + tool_call_tokens
        logger.info(f'Total context window tokens so far: {content_tokens}')
        max_tokens = model_context - llm.max_tokens
        i = 0
        num_chars = [25000, 10000, 5000, 2500, 1000, 500]
        is_error = False
        while content_tokens >= max_tokens and i < len(num_chars):
            num_char = num_chars[i]
            content = f"WARNING: Tool result truncated, last {num_char} characters: " + tool_content[-num_char:]
            tool_call_tokens = llm.get_num_tokens(content)
            content_tokens = total_tokens_before_tool + tool_call_tokens
            max_tokens = model_context - llm.max_tokens
            if content_tokens < max_tokens:
                is_error = False
                tool_content = content
                break
            is_error = True
            i += 1
        if is_error:
            logger.warning('Max token limit reached')
            tool_content = MAX_TOKEN_LIMIT_KEYWORD
    else:
        tool_call_tokens = 0
        content_tokens = total_tokens_before_tool
        tool_content = ""

    tool_content_block = {
        "type": "text",
        "text": tool_content
    }

    if is_error:
        tool_content_block["is_error"] = True

    final_messages.append(
        ToolMessage(
            content=[
                tool_content_block
            ],
            tool_call_id=tool_message.tool_call_id,
            name=tool_message.name
        )
    )
    return format_messages(messages=final_messages)


def get_second_ai_or_human_message_index(
    messages: List[BaseMessage]
):
    first_msg_found = False
    for i, msg in enumerate(messages):
        if i < 2:
            # skip the first two messages
            continue
        if msg.type == "ai" or msg.type == "human":
            if first_msg_found:
                return i
            else:
                first_msg_found = True
    return -1


def summarize_messages(messages_to_summarize: str):
    logger.info(f"summarizing messages to compress context")

    messages = [
        SystemMessage(content=[
            {
                "type": "text",
                "text": MESSAGE_SUMMARY_SYSTEM_PROMPT_TEMPLATE.format(
                    agent_persona=MESSAGE_SUMMARY_AGENT_PERSONA_PROMPT,
                    inputs=MESSAGE_SUMMARY_INPUTS,
                    rules=f"{MESSAGE_SUMMARY_RULES_PROMPTLET}"
                )
            }
        ]),
        HumanMessage(
            content=[
                {
                    "type": "text",
                    "text": MESSAGE_SEQUENCE_INPUT.format(
                        message_sequence=messages_to_summarize
                    )
                }
            ]
        )
    ]
    response: AIMessage = make_llm_request(llm=llm_gemini_2_5_pro, messages=messages)
    return get_response_content(response=response)


def format_messages(messages: List[BaseMessage]):
    # Process all messages except the last one
    for i, msg in enumerate(messages[:-1]):  # Skip the last message
        if msg.type != 'system':
            if isinstance(msg.content, list):
                for content_item in msg.content:
                    if isinstance(content_item, dict) and content_item.get('cache_control', ''):
                        content_item.pop('cache_control', None)
                        logger.debug('removed cache control')

    # Handle the last message separately
    if messages and len(messages):
        last_msg = messages[-1]
        if last_msg.type != 'system':
            if isinstance(last_msg.content, list):
                last_item = last_msg.content[-1]
                if isinstance(last_item, dict):
                    # For the last message, add cache_control
                    logger.debug('added cache control to last message')
                    last_item['cache_control'] = {"type": "ephemeral"}

    return messages


def get_response_content(response: BaseMessage, include_thinking=False) -> str:
    content = ""
    if isinstance(response.content, list):
        for item in response.content:
            if isinstance(item, dict):
                if item.get("type", "") == "text":
                    content += item["text"]
                elif include_thinking and item.get("type") == "thinking":
                    content += f"\n{item["thinking"]}\n"
            elif isinstance(item, str):
                content += item
    else:
        content = response.content
    if not content and not include_thinking:
        raise FormattingError
    return content

# Create a common exponential backoff retry decorator


def archie_exponential_retry(
    max_retries=DEFAULT_MAX_RETRIES,
    min_wait=DEFAULT_MIN_WAIT,
    max_wait=DEFAULT_MAX_WAIT,
    multiplier=DEFAULT_MULTIPLIER,
    exceptions=RETRYABLE_EXCEPTIONS
):
    """
    A decorator that implements exponential backoff retry logic for API calls.
    Supports both sync and async functions.
    """
    def decorator(func):
        # Check if the function is async
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                # For async functions, we need tenacity's retry_event_loop
                @retry(
                    retry=retry_if_exception_type(exceptions),
                    stop=stop_after_attempt(max_retries),
                    wait=wait_exponential(multiplier=multiplier, min=min_wait, max=max_wait),
                    before_sleep=before_sleep_log(logger.logger, logging.WARNING),
                    retry_error_callback=lambda retry_state: retry_state.outcome.result(),
                    reraise=True
                )
                async def _async_retry():
                    return await func(*args, **kwargs)

                return await _async_retry()
            return async_wrapper
        else:
            @wraps(func)
            @retry(
                retry=retry_if_exception_type(exceptions),
                stop=stop_after_attempt(max_retries),
                wait=wait_exponential(multiplier=multiplier, min=min_wait, max=max_wait),
                before_sleep=before_sleep_log(logger.logger, logging.WARNING)
            )
            def sync_wrapper(*args, **kwargs):
                return func(*args, **kwargs)

            return sync_wrapper
    return decorator


def get_fuzzy_path(
    path: str,
    potential_paths: List[str]
) -> str:
    if path not in potential_paths:
        fuzzy_path_choices = process.extract(
            query=path,
            choices=potential_paths,
            limit=1
        )
        if len(fuzzy_path_choices) and fuzzy_path_choices[0]:
            path = fuzzy_path_choices[0][0]
            logger.warning(f'fuzzy matched path: {path}')
    return path


def clean_path(path: str):
    if path.startswith('/'):
        path = path[1:]
    if path == ".":
        path = ""
    if path.endswith('/'):
        path = path[:-1]
    return path


def read_range(view_range: List[int], file_text: str, prepend_line_numbers=True):
    """
    Given a list view_range containing two 1-indexed integers representing line numbers,
    and a string representing the file's text, returns the portion that is being read
    with line numbers prepended to each line.

    If the second integer extends the length or is -1, return up to the end of the file.
    Negative indices count from the end of the file (-1 is last line, -2 is second to last, etc.)

    Args:
        view_range (list): A list containing two integers representing line numbers.
                          Can be 1-indexed positive numbers or negative indices.
        file_text (str): A string representing the file's text.

    Returns:
        str: The portion of the file that is being read with line numbers prepended.
    """
    # Split the file text into lines
    lines = file_text.split('\n')

    # Get the total number of lines
    total_lines = len(lines)

    # Check if the file is empty
    if total_lines == 0:
        return ""

    # Extract start and end line numbers
    start_line = view_range[0]
    end_line = view_range[1]

    # Special case: if file has only empty content (single empty line)
    if total_lines == 1 and lines[0] == '':
        return ""

    # Handle negative indices
    if start_line < 0:
        # Convert negative index to positive (e.g., -1 becomes last line)
        start_line = total_lines + start_line + 1

    if end_line < 0 and end_line != -1:
        # Convert negative index to positive, but preserve -1 as "to end"
        end_line = total_lines + end_line + 1

    # Convert to 0-indexed for Python list operations
    start_idx = start_line - 1

    # If end_line is -1 or exceeds total lines, set it to the last line
    if end_line == -1 or end_line > total_lines:
        end_idx = total_lines  # This will include all lines to the end
    else:
        end_idx = end_line  # Since end is exclusive in Python slicing, this is correct

    # Ensure start_idx is not negative and not beyond file
    start_idx = max(0, min(start_idx, total_lines - 1))

    # Ensure end_idx is not less than start_idx
    end_idx = max(start_idx, min(end_idx, total_lines))

    # Extract the requested lines
    requested_lines = lines[start_idx:end_idx]

    if prepend_line_numbers:
        # Prepend line numbers to each line
        numbered_lines = []
        for i, line in enumerate(requested_lines):
            # Calculate the actual line number (1-indexed)
            line_number = start_idx + i + 1
            numbered_lines.append(f"{line_number}: {line}")
        # Join the lines back together with newlines
        return '\n'.join(numbered_lines)
    else:
        return '\n'.join(requested_lines)


@archie_exponential_retry()
def make_llm_request(
    llm: BaseChatModel,
    messages: List[BaseMessage]
) -> AIMessage:
    return llm.invoke(messages)


@archie_exponential_retry()
async def amake_llm_request(
    llm: BaseChatModel,
    messages: List[BaseMessage]
) -> AIMessage:
    return await llm.ainvoke(messages)


def is_first_child_path(parent_path: str, child_path: str) -> bool:
    if not child_path:
        return False

    child_forward_slashes = len(child_path.split('/')) - 1
    if parent_path == "":
        return child_forward_slashes == 0

    parent_forward_slashes = len(parent_path.split('/')) - 1
    return child_forward_slashes == (parent_forward_slashes + 1)


def get_parent_path(file_path: str) -> str:
    return '/'.join(file_path.split('/')[:-1]) if file_path else ""
