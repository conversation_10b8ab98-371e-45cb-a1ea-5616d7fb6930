from set_env import PROJECT_ID, GRAPH_CODE_TOPIC

from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification

publisher = pubsub_v1.PublisherClient()

batch_index = 0
excluded_indexes = []

notification_data = {"repo_name": "astropy", "repo_id": "1001673303", "branch_id": "acb55d3a-e296-48f1-aaa7-2295428070ff", "branch_name": "astropy__astropy-7606", "company_id": "default", "user_id": "623eaeb5-f5c3-4e26-abdb-bdea6772da58", "team_id": "default", "job_id": "95e4fdd1-0f6b-4463-8078-6c1bf0468990",
                     "project_id": "95f51789-cda6-4232-9951-b733dc205362", "head_commit_hash": "3cedd79e6c121910220f8e6df77c54a0b344ea94", "prev_head_commit_hash": "", "propagate": True, "batch_index": 3, "total_batches": 7, "tech_spec_id": "5247a1ba-ae98-43ca-a6f2-e755e6817f81"}

total_batches = notification_data["total_batches"]

while batch_index < total_batches:
    if batch_index in excluded_indexes:
        batch_index += 1
        continue
    notification_data["batch_index"] = batch_index
    publish_notification(
        publisher=publisher,
        notification_data=notification_data,
        project_id=PROJECT_ID,
        topic_id=GRAPH_CODE_TOPIC
    )
    batch_index += 1
