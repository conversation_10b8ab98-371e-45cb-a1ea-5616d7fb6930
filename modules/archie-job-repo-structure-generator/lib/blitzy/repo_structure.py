import json
from typing import TypedDict, Dict, Any, List, Literal

from blitzy_utils.logger import logger
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, SystemMessage, HumanMessage
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode

from blitzy_utils.common import get_json_content, DictFileHelper

from blitzy_platform_shared.common.utils import archie_exponential_retry, get_response_content, \
    process_tool_call, process_messages_with_tool_call
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.common.consts import FormattingError

from lib.blitzy.prompts import REPO_SRC_FOLDER_STRUCTURE_SYSTEM_PROMPT, REPO_ROOT_STRUCTURE, \
    REPO_OTHER_FILE_STRUCTURE_SYSTEM_PROMPT, REPO_ROOT_STRUCTURE_INPUT, REPO_SRC_FILE_STRUCTURE_SYSTEM_PROMPT, REPO_FILE_SORTER_SYSTEM_PROMPT, \
    TECH_SPEC_SECTIONS_INPUT

REPO_ROOT_FILES = [
    ".gitignore",
    "README.md",
    "LICENSE"
]


class RepoStructureState(TypedDict):
    current_files_list_str: str
    files_list: List[str]
    src_folder_list: List[str]
    src_folder_index: int
    src_file_dict: Dict[str, List[str]]
    tech_spec: str
    src_folder_list_content: str
    tech_spec_parsed: Dict[str, str]


repo_structure_files_helper = DictFileHelper(files={})


@tool("get_src_subfolder_files_list", parse_docstring=True)
def get_src_subfolder_files_list(subfolder_name: str):
    """
        Gets a list of file paths for a subfolder inside src in the project's GitHub repository.
        Useful for retrieving paths of files that already exist inside a subfolder inside src in the GitHub repository.
        Not useful for retrieving the contents of the current subfolder or of folders outside src.

        Args:
            subfolder_name: The name of the subfolder inside the src folder for which files are being retrieved.
    """
    files_dict = repo_structure_files_helper.get_dict()
    logger.info(f'retrieving list of files for folder: {subfolder_name}')
    # logger.info(f'files dict: {files_dict}')
    file = files_dict.get(subfolder_name)
    return file if file else ""


repo_structure_tools = [get_src_subfolder_files_list, get_tech_spec_section]
repo_structure_tools_node = ToolNode(repo_structure_tools)


class RepoStructureGeneratorHelper:
    def __init__(self, generator_llm: BaseChatModel, validator_llm: BaseChatModel):
        self.generator_llm = generator_llm
        self.validator_llm = validator_llm
        self.generator = self.create_graph()
        self.repo_structure_files_helper = repo_structure_files_helper

    def create_graph(self) -> StateGraph:
        # Define the graph
        graph_generator = StateGraph(RepoStructureState)

        # Add nodes
        graph_generator.add_node("create_src_folder_structure", self.create_src_folder_structure)
        graph_generator.add_node("create_src_structure", self.create_src_structure)
        graph_generator.add_node("sort_listed_files", self.sort_listed_files)
        graph_generator.add_node("create_full_folder_structure", self.create_full_folder_structure)

        graph_generator.add_conditional_edges(
            "sort_listed_files",
            self.src_router,
            {
                "continue": "create_src_structure",
                "end": "create_full_folder_structure"
            }
        )

        # Set the entry point
        graph_generator.add_edge(START, "create_src_folder_structure")
        graph_generator.add_edge("create_src_folder_structure", "create_src_structure")
        graph_generator.add_edge("create_src_structure", "sort_listed_files")
        graph_generator.add_edge("create_full_folder_structure", END)
        return graph_generator

    @archie_exponential_retry()
    def create_src_folder_structure(self, state: RepoStructureState) -> Dict[str, Any]:
        logger.info('generating src folder structure')

        messages = [
            SystemMessage(content=[{
                "text": REPO_SRC_FOLDER_STRUCTURE_SYSTEM_PROMPT,
                "type": "text"
            }]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": REPO_ROOT_STRUCTURE_INPUT.format(repo_root_structure=REPO_ROOT_STRUCTURE),
                }
            ])
        ]

        response = self.generator_llm.invoke(messages)

        while len(response.tool_calls):
            # fill in the value of local variables
            messages.append(response)
            total_tokens = response.usage_metadata["total_tokens"]
            tool_calls = response.tool_calls
            tools_config = {
                "tech_spec_parsed": state["tech_spec_parsed"]
            }
            for tool_call in tool_calls:
                tool_message = process_tool_call(
                    tool_call=tool_call,
                    tools_list=repo_structure_tools,
                    tools_config=tools_config
                )
                messages = process_messages_with_tool_call(
                    tool_message=tool_message,
                    messages=messages,
                    total_tokens_before_tool=total_tokens,
                    llm=self.generator_llm
                )
            # logger.info(f'sending tool response back to llm for file: {file_path}')
            response: AIMessage = self.generator_llm.invoke(messages)

        content = get_response_content(response=response)

        state["src_folder_list_content"] = content

        json_content = get_json_content(content, strict=True)

        if not json_content:
            raise FormattingError

        # logger.info(json_content)

        # Update the state
        state["src_folder_list"] = json.loads(json_content)

        logger.info('src folder structure generated successfully')

        return {
            "current_files_list_str": state["current_files_list_str"],
            "files_list": state["files_list"],
            "src_folder_list": state["src_folder_list"],
            "src_folder_index": state["src_folder_index"],
            "src_file_dict": state["src_file_dict"],
            "tech_spec": state["tech_spec"],
            "src_folder_list_content": state["src_folder_list_content"],
            "tech_spec_parsed": state["tech_spec_parsed"]
        }

    def src_router(self, state: RepoStructureState) -> Literal["continue", "end"]:
        if state["src_folder_index"] >= len(state["src_folder_list"]):
            return "end"
        return "continue"

    @archie_exponential_retry()
    def create_src_structure(self, state: RepoStructureState) -> Dict[str, Any]:
        src_folders_list = state["src_folder_list"]
        current_src_folder_index = state["src_folder_index"]
        current_subfolder = src_folders_list[current_src_folder_index]
        logger.info(f'generating file structure for src/{current_subfolder}')

        messages = [
            SystemMessage(content=[{
                "text": REPO_SRC_FILE_STRUCTURE_SYSTEM_PROMPT,
                "type": "text"
            }]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": REPO_ROOT_STRUCTURE_INPUT.format(repo_root_structure=REPO_ROOT_STRUCTURE),
                },
                {
                    "type": "text",
                    "text": "list of all existing src subfolders: {src_folders_list}".format(src_folders_list=state["src_folder_list_content"]),
                },
                {
                    "type": "text",
                    "text": "your assigned src sub-folder name: {src_subfolder_name}".format(src_subfolder_name=current_subfolder)
                }
            ])
        ]

        response: AIMessage = self.generator_llm.invoke(messages)

        while len(response.tool_calls):
            # fill in the value of local variables
            messages.append(response)
            total_tokens = response.usage_metadata["total_tokens"]
            # logger.info(total_tokens)
            tool_calls = response.tool_calls
            tools_config = {
                "tech_spec_parsed": state["tech_spec_parsed"]
            }
            for tool_call in tool_calls:
                tool_message = process_tool_call(
                    tool_call=tool_call,
                    tools_list=repo_structure_tools,
                    tools_config=tools_config
                )
                messages = process_messages_with_tool_call(
                    tool_message=tool_message,
                    messages=messages,
                    total_tokens_before_tool=total_tokens,
                    llm=self.generator_llm
                )
            # logger.info(f'sending tool response back to llm for file: {file_path}')
            response: AIMessage = self.generator_llm.invoke(messages)

        content = get_response_content(response=response)

        state["current_files_list_str"] = get_json_content(content, strict=True)

        if not state["current_files_list_str"]:
            raise FormattingError

        logger.info(state["current_files_list_str"])

        return {
            "current_files_list_str": state["current_files_list_str"],
            "files_list": state["files_list"],
            "src_folder_list": state["src_folder_list"],
            "src_folder_index": state["src_folder_index"],
            "src_file_dict": state["src_file_dict"],
            "tech_spec": state["tech_spec"],
            "src_folder_list_content": state["src_folder_list_content"],
            "tech_spec_parsed": state["tech_spec_parsed"]
        }

    @archie_exponential_retry()
    def sort_listed_files(self, state: RepoStructureState) -> Dict[str, Any]:
        src_folders_list = state["src_folder_list"]
        current_src_folder_index = state["src_folder_index"]
        current_subfolder = src_folders_list[current_src_folder_index]
        logger.info(f'sorting listed files for src/{current_subfolder}')

        messages = [
            SystemMessage(content=[{
                "text": REPO_FILE_SORTER_SYSTEM_PROMPT,
                "type": "text"
            }]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": "your assigned src sub-folder name: {src_subfolder_name}".format(src_subfolder_name=current_subfolder)
                },
                {
                    "type": "text",
                    "text": "list of all files in that src subfolder: {src_subfolder_files}".format(src_subfolder_files=state["current_files_list_str"]),
                }
            ])
        ]

        response: AIMessage = self.validator_llm.invoke(messages)

        content = get_response_content(response=response)

        # logger.info(content)

        json_content_str = get_json_content(content, strict=True)

        if not json_content_str:
            raise FormattingError

        # logger.info(json_content_str)

        generated_files_list = json.loads(json_content_str)
        state["files_list"] += generated_files_list

        self.repo_structure_files_helper.files_dict[current_subfolder] = generated_files_list

        current_src_folder_index += 1
        state["src_folder_index"] = current_src_folder_index

        return {
            "current_files_list_str": state["current_files_list_str"],
            "files_list": state["files_list"],
            "src_folder_list": state["src_folder_list"],
            "src_folder_index": state["src_folder_index"],
            "src_file_dict": state["src_file_dict"],
            "tech_spec": state["tech_spec"],
            "src_folder_list_content": state["src_folder_list_content"],
            "tech_spec_parsed": state["tech_spec_parsed"]
        }

    @archie_exponential_retry()
    def create_full_folder_structure(self, state: RepoStructureState) -> Dict[str, Any]:
        logger.info('generating full folder structure')

        messages = [
            SystemMessage(content=[{
                "text": REPO_OTHER_FILE_STRUCTURE_SYSTEM_PROMPT,
                "type": "text"
            }]),
            HumanMessage(content=[
                {
                    "type": "text",
                    "text": TECH_SPEC_SECTIONS_INPUT.format(
                        tech_spec_sections=json.dumps(list(state["tech_spec_parsed"].keys()))
                    ),
                    "cache_control": {"type": "ephemeral"}
                },
                {
                    "type": "text",
                    "text": "src folder files list: {src_files_list}".format(src_files_list=json.dumps(state["files_list"])),
                }
            ])
        ]

        response = self.generator_llm.invoke(messages)

        while len(response.tool_calls):
            # fill in the value of local variables
            messages.append(response)
            total_tokens = response.usage_metadata["total_tokens"]
            # logger.info(total_tokens)
            tool_calls = response.tool_calls
            tools_config = {
                "tech_spec_parsed": state["tech_spec_parsed"]
            }
            for tool_call in tool_calls:
                tool_message = process_tool_call(
                    tool_call=tool_call,
                    tools_list=repo_structure_tools,
                    tools_config=tools_config
                )
                messages = process_messages_with_tool_call(
                    tool_message=tool_message,
                    messages=messages,
                    total_tokens_before_tool=total_tokens,
                    llm=self.generator_llm
                )
            # logger.info(f'sending tool response back to llm for file: {file_path}')
            response: AIMessage = self.generator_llm.invoke(messages)

        content = get_response_content(response=response)

        # logger.info(content)

        json_content = get_json_content(content)
        if not json_content:
            raise FormattingError
        other_files_list = json.loads(json_content)

        other_files_list += REPO_ROOT_FILES

        state["files_list"] += other_files_list
        # logger.info(state["files_list"])

        logger.info('repo folder structure generated successfully')

        return {
            "current_files_list_str": state["current_files_list_str"],
            "files_list": state["files_list"],
            "src_folder_list": state["src_folder_list"],
            "src_folder_index": state["src_folder_index"],
            "src_file_dict": state["src_file_dict"],
            "tech_spec": state["tech_spec"],
            "src_folder_list_content": state["src_folder_list_content"],
            "tech_spec_parsed": state["tech_spec_parsed"]
        }
