from google.cloud import pubsub_v1

from blitzy_utils.common import publish_notification
from blitzy_utils.enums import BackpropChangeMode

PROJECT_ID = 'blitzy-platform-stage'
REVERSE_DOCUMENT_TOPIC = 'generate-reverse-document'

publisher = pubsub_v1.PublisherClient()

notification_data = {"repo_name": "ABK-484", "repo_id": "1003438478", "branch_id": "d63f3c08-06e1-406a-8281-8c217666a9ee", "project_id": "0e868038-2e21-4e34-882e-08407fe1bf19", "job_id": "ed7ca2d6-05f0-47cc-8c72-07e31766bb3d",
                     "propagate": True, "user_id": "4a9793f3-b061-48bc-abb1-ea9030c5c259", "company_id": "default", "team_id": "default", "head_commit_hash": "dcc94f2c0181bb63b2b6f888cc7346811a0fdc95", "tech_spec_id": "6b38605d-da83-4cb3-81ab-e2538fc3551c"}

publish_notification(publisher, notification_data, PROJECT_ID, REVERSE_DOCUMENT_TOPIC)
