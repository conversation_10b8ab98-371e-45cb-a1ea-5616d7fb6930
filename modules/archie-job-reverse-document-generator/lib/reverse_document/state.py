from typing import TypedDict, List, Dict, Any

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder

from .models import DocumentSection


class ReverseDocumentState(TypedDict):
    branch_id: str
    branch_name: str
    company_id: str
    current_tech_spec: str
    current_tech_spec_sections: Dict[str, str]
    github_server: str
    graph_builder: CodeGraphBuilder
    head_commit_hash: str
    mode: str
    new_requirements: str
    parsed_sub_sections: Dict[str, str]
    previous_structured_sections: List[DocumentSection]
    previous_tech_spec: str
    repo_id: str
    git_project_repo_id: str
    repo_name: str
    root_folder_contents: str
    section_context: Dict[str, str]
    section_headings: List[str]
    section_index: int
    section_prompts: Dict[str, str]
    structured_sections: List[DocumentSection]
    summary_of_changes: str
    tech_spec_parsed: Dict[str, str]
    total_sections: int
    updated_tech_spec: str
    user_context: str
    user_id: str


def get_state(state: ReverseDocumentState) -> Dict[str, Any]:
    return {
        "branch_id": state["branch_id"],
        "branch_name": state["branch_name"],
        "company_id": state["company_id"],
        "current_tech_spec": state["current_tech_spec"],
        "current_tech_spec_sections": state["current_tech_spec_sections"],
        "github_server": state["github_server"],
        "graph_builder": state["graph_builder"],
        "head_commit_hash": state["head_commit_hash"],
        "mode": state["mode"],
        "new_requirements": state["new_requirements"],
        "parsed_sub_sections": state["parsed_sub_sections"],
        "previous_structured_sections": state["previous_structured_sections"],
        "previous_tech_spec": state["previous_tech_spec"],
        "repo_id": state["repo_id"],
        "repo_name": state["repo_name"],
        "root_folder_contents": state["root_folder_contents"],
        "section_context": state["section_context"],
        "section_headings": state["section_headings"],
        "section_index": state["section_index"],
        "section_prompts": state["section_prompts"],
        "structured_sections": state["structured_sections"],
        "summary_of_changes": state["summary_of_changes"],
        "tech_spec_parsed": state["tech_spec_parsed"],
        "total_sections": state["total_sections"],
        "updated_tech_spec": state["updated_tech_spec"],
        "user_context": state["user_context"],
        "user_id": state["user_id"],
        "git_project_repo_id": state["git_project_repo_id"]
    }
