import json
import os

from blitzy_utils.enums import BackpropChangeMode

EVENT_DATA = json.dumps({"repo_name": "ff-confirming-issue-after-conflictsPR-sync", "repo_id": "*********", "branch_id": "d647e072-2805-48f5-9651-7a2bf49e726f", "project_id": "96160abb-350f-4fa7-a630-727abc7116b2", "job_id": "67b1a9dc-f9ac-4cd1-ad16-a8e4fd9b249a",
                        "propagate": True, "user_id": "e48c74e3-fdcc-4e4d-be80-07204b116ece", "company_id": "default", "team_id": "default", "head_commit_hash": "a8386d80d13fa0b745b029ea5e64303a17ba17fc", "tech_spec_id": "6e03c189-7780-4c1f-bb56-3d801e75740a"})
os.environ["EVENT_DATA"] = EVENT_DATA

os.environ["PROJECT_ID"] = 'blitzy-platform-stage'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-platform-stage'
os.environ["PRIVATE_BLOB_NAME"] = 'private-src'
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'  # dummy value
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
os.environ["VOYAGE_API_KEY"] = "pa-5WI8Vwyw7YUpoWeX4ckHbDd7dN4w74S61zXHQKD0wTE"
os.environ["GEMINI_API_KET"] = "AIzaSyAA_WvkItpwfiL3D8czRWsT5dbA2iXmF_I"
os.environ["SERVICE_URL_ADMIN"] = "https://archie-service-admin-464705070478.us-central1.run.app"

NEO4J_SERVER = "neo4j://34.66.114.166:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "bavjoz-pamciB-6vifce"
os.environ["NEO4J_SERVER"] = NEO4J_SERVER
os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD
os.environ["MARKDOWN_SERVER"] = "https://archie-service-markdown-464705070478.us-central1.run.app/v1/mermaid/validate"

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
STAGE_GITHUB_SECRET_SERVER = "https://archie-secret-manager-480762617400.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-east1.run.app"
os.environ["GITHUB_SECRET_SERVER"] = STAGE_GITHUB_SECRET_SERVER

os.environ["LANGCHAIN_TRACING_V2"] = ""
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""
os.environ["LANGCHAIN_PROJECT"] = ""
