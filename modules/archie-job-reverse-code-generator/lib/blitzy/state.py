from typing import TypedDict, Dict, Any, List, Set


class ReverseCodeState(TypedDict):
    agent_log: str
    current_file_info: Dict[str, Any]
    current_session_cmds: List[str]
    file_index: int
    json_retry_count: int
    pr_data: Dict[str, Any]
    processed_files: Set[str]
    project_guide: str
    resume: bool
    retry_count: int
    target_file_path: str
    tech_spec_first_n: str


def get_state(state=ReverseCodeState) -> Dict[str, Any]:
    return {
        "agent_log": state["agent_log"],
        "current_file_info": state["current_file_info"],
        "current_session_cmds": state["current_session_cmds"],
        "file_index": state["file_index"],
        "json_retry_count": state["json_retry_count"],
        "pr_data": state["pr_data"],
        "processed_files": state["processed_files"],
        "project_guide": state["project_guide"],
        "resume": state["resume"],
        "retry_count": state["retry_count"],
        "target_file_path": state["target_file_path"],
        "tech_spec_first_n": state["tech_spec_first_n"]
    }
