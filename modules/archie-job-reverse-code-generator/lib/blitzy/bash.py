import os
import signal
import asyncio
from typing import Optional, Tuple
from asyncio.subprocess import Process, PIPE

from blitzy_utils.logger import logger

# Constants
DEFAULT_TIMEOUT = 600.0  # 10 minutes default timeout for compilation/tests
DEFAULT_OUTPUT_DELAY = 0.1  # 100ms polling interval
DEFAULT_MAX_OUTPUT_SIZE = 10 * 1024 * 1024  # 10MB max output
BASH_COMMAND = "/bin/bash"
SENTINEL = "<<COMMAND_COMPLETE>>"
DEFAULT_BASH_SESSION_KEY = "default"


class BashSession:
    """Manages a bash shell session for command execution."""

    def __init__(
        self,
        timeout: float = DEFAULT_TIMEOUT,
        output_delay: float = DEFAULT_OUTPUT_DELAY,
        max_output_size: int = DEFAULT_MAX_OUTPUT_SIZE,
        working_directory: Optional[str] = None,
    ):
        self._process: Optional[Process] = None
        self._started: bool = False
        self._timed_out: bool = False
        self._timeout = timeout
        self._output_delay = output_delay
        self._max_output_size = max_output_size
        self._working_directory = working_directory or os.getcwd()
        self._session_id = None

    async def start(self) -> Tuple[str, bool]:
        """Start the bash session."""
        if self._started:
            return "Bash session already started", False

        try:
            # Create subprocess with exec (not shell) for better control
            self._process = await asyncio.create_subprocess_exec(
                BASH_COMMAND,
                stdin=PIPE,
                stdout=PIPE,
                stderr=PIPE,
                start_new_session=True,  # Create new session to avoid TTY issues
                cwd=self._working_directory,
                env={**os.environ},  # Inherit environment variables
            )

            self._started = True
            self._timed_out = False
            self._session_id = f"bash_session_{id(self)}"

            # Set up the shell environment
            init_commands = [
                "export PS1=''",  # Remove prompt for cleaner output
                "export TERM=dumb",  # Simple terminal for predictable output
                f"cd /app/{self._working_directory}",  # Ensure we're in the right directory
            ]

            for cmd in init_commands:
                await self._execute_internal(cmd)

            logger.info(
                f"Started bash session {self._session_id} in {self._working_directory}"
            )
            return (
                f"Bash session started successfully in {self._working_directory}",
                False,
            )

        except Exception as e:
            logger.error(f"Failed to start bash session: {e}")
            self._started = False
            return f"Error starting bash session: {str(e)}", True

    async def stop(self) -> None:
        """Terminate the bash shell and clean up."""
        if not self._started or not self._process:
            return

        try:
            # Try graceful termination first
            self._process.terminate()
            # Wait for the process to actually terminate
            try:
                await asyncio.wait_for(self._process.wait(), timeout=1.0)
            except asyncio.TimeoutError:
                # Force kill if needed
                self._process.kill()
                await self._process.wait()
        except Exception as e:
            logger.warning(f"Error stopping bash session: {e}")

        self._started = False
        logger.info(f"Stopped bash session {self._session_id}")

    async def _execute_internal(self, command: str) -> str:
        """Execute a command without sentinel checking (for internal use)."""
        if not self._process or not self._process.stdin:
            raise RuntimeError("Process not initialized")

        self._process.stdin.write(command.encode() + b"\n")
        await self._process.stdin.drain()
        await asyncio.sleep(self._output_delay)
        return ""

    async def _read_stream_until_sentinel(
        self, reader: asyncio.StreamReader, sentinel: str, timeout_seconds: float
    ) -> Tuple[str, bool]:
        """Read from a stream until sentinel is found or timeout."""
        output_chunks = []
        sentinel_bytes = sentinel.encode("utf-8")
        buffer = b""
        total_size = 0

        async with asyncio.timeout(timeout_seconds):
            while True:
                # Read in chunks
                try:
                    chunk = await asyncio.wait_for(
                        reader.read(1024), timeout=self._output_delay
                    )
                except asyncio.TimeoutError:
                    # No data available, check if process died
                    if self._process.returncode is not None:
                        break
                    continue

                if not chunk:
                    # EOF reached
                    break

                buffer += chunk
                total_size += len(chunk)

                # Check for size limit
                if total_size > self._max_output_size:
                    raise RuntimeError(
                        f"Output exceeded maximum size of {self._max_output_size} bytes"
                    )

                # Check for sentinel in buffer
                if sentinel_bytes in buffer:
                    # Split at sentinel
                    before_sentinel, _ = buffer.split(sentinel_bytes, 1)
                    output_chunks.append(before_sentinel)
                    return (
                        b"".join(output_chunks).decode("utf-8", errors="replace"),
                        True,
                    )

                # If buffer is getting large, move some to output
                if len(buffer) > 8192:
                    output_chunks.append(buffer[:-1024])
                    buffer = buffer[-1024:]

        # Timeout or EOF without sentinel
        if buffer:
            output_chunks.append(buffer)
        return b"".join(output_chunks).decode("utf-8", errors="replace"), False

    async def execute(self, command: str) -> Tuple[str, str, bool]:
        """Execute a command in the bash shell."""
        if not self._started:
            return "", "Bash session not started. Please restart the session.", True

        if self._process.returncode is not None:
            return "", "Process has terminated. Please restart the session.", True

        try:
            # Create unique sentinels for stdout and stderr
            import uuid

            sentinel_id = uuid.uuid4().hex[:8]
            stdout_sentinel = f"{SENTINEL}_STDOUT_{sentinel_id}"
            stderr_sentinel = f"{SENTINEL}_STDERR_{sentinel_id}"

            # Execute command with separate sentinels for each stream
            # Using a subshell to ensure sentinels are printed after command completes
            wrapped_command = f"""
            (
                {command}
            )
            echo "{stdout_sentinel}"
            echo "{stderr_sentinel}" >&2
            """

            # Send the command
            self._process.stdin.write(wrapped_command.encode())
            await self._process.stdin.drain()

            # Read both streams concurrently
            stdout_task = asyncio.create_task(
                self._read_stream_with_continuous_output(
                    self._process.stdout, stdout_sentinel, self._timeout
                )
            )
            stderr_task = asyncio.create_task(
                self._read_stream_with_continuous_output(
                    self._process.stderr, stderr_sentinel, self._timeout
                )
            )

            # Wait for both
            stdout, stderr = await asyncio.gather(stdout_task, stderr_task)

            # Process results...
            return (
                stdout.strip(),
                stderr.strip(),
                self._check_for_errors(stdout, stderr),
            )

        except asyncio.TimeoutError:
            # Better cleanup on timeout
            await self._kill_process_group()
            self._timed_out = True
            return "", f"Command timed out after {self._timeout} seconds.", True

    async def _read_stream_with_continuous_output(
        self, reader: asyncio.StreamReader, sentinel: str, timeout_seconds: float
    ) -> str:
        """Read from stream continuously to prevent buffer deadlock."""
        output_chunks = []
        sentinel_bytes = sentinel.encode("utf-8")
        accumulated = b""

        end_time = asyncio.get_event_loop().time() + timeout_seconds

        while asyncio.get_event_loop().time() < end_time:
            try:
                # Non-blocking read with short timeout
                chunk = await asyncio.wait_for(reader.read(4096), timeout=0.1)

                if not chunk:  # EOF
                    break

                accumulated += chunk

                # Check for sentinel
                if sentinel_bytes in accumulated:
                    # Extract everything before sentinel
                    before_sentinel = accumulated.split(sentinel_bytes)[0]
                    output_chunks.append(before_sentinel)
                    break

                # Move data to output_chunks to prevent unlimited growth
                if len(accumulated) > 8192:
                    # Keep last 1024 bytes to handle sentinel split
                    output_chunks.append(accumulated[:-1024])
                    accumulated = accumulated[-1024:]

            except asyncio.TimeoutError:
                # No data available, check if process died
                if self._process.returncode is not None:
                    break
                continue
        else:
            # Timeout reached
            raise asyncio.TimeoutError(f"Timeout waiting for sentinel: {sentinel}")

        # Add any remaining data
        if accumulated and sentinel_bytes not in accumulated:
            output_chunks.append(accumulated)

        return b"".join(output_chunks).decode("utf-8", errors="replace")

    async def _kill_process_group(self):
        """Kill the entire process group."""
        try:
            if self._process and self._process.pid:
                # Since we used start_new_session=True, we can kill the entire group
                os.kill(self._process.pid, signal.SIGTERM)
                await asyncio.sleep(0.1)
                if self._process.returncode is None:
                    os.kill(self._process.pid, signal.SIGKILL)
        except ProcessLookupError:
            pass

    def _check_for_errors(self, stdout: str, stderr: str) -> bool:
        """Check if command output indicates an error."""
        error_patterns = [
            "command not found",
            "No such file or directory",
            "Permission denied",
            "syntax error",
            "fatal:",
            "error:",
            "Error:",
            "ERROR:",
            "Failed",
            "FAILED",
            "Traceback (most recent call last)",  # Python errors
            "Exception:",  # Java exceptions
            "Cannot find module",  # Node.js errors
            "npm ERR!",  # npm errors
            "mvn: command not found",  # Maven not found
            "Failed to execute goal",  # Maven build errors
        ]

        # Check both stdout and stderr for error patterns
        combined_output = f"{stdout}\n{stderr}".lower()

        # Check for error patterns
        for pattern in error_patterns:
            if pattern.lower() in combined_output:
                return True

        # Also check if stderr has substantial content (often indicates errors)
        # But exclude common non-error stderr outputs
        if stderr.strip():
            non_error_stderr_patterns = [
                "warning:",
                "note:",
                "info:",
                "downloading",
                "building",
                "compiling",
                "installing",
                "creating",
                "writing",
                "generating",
            ]
            stderr_lower = stderr.lower()
            if not any(
                pattern in stderr_lower for pattern in non_error_stderr_patterns
            ):
                # If stderr has content and it's not a known non-error pattern,
                # it might be an error
                return True

        return False


# Keep the handle_bash_tool_response function as is
async def handle_bash_tool_response(command: str, session: BashSession) -> str:
    """
    Execute bash commands or restart the bash session.

    Use this tool to:
    - Run shell commands for compilation, testing, file operations, etc.
    - Install dependencies with pip, npm, etc.
    - Navigate directories and manage files
    - Execute build scripts and run tests

    Args:
        command: The bash command to execute

    Returns:
        Command output or status message
    """
    # Execute command
    stdout, stderr, is_error = await session.execute(command)

    # Format output
    output_parts = []
    if stdout:
        output_parts.append(f"[stdout]\n\n{stdout}")
    if stderr and not is_error:
        # Include stderr even if not an error (some tools write to stderr normally)
        output_parts.append(f"[stderr]\n\n{stderr}")
    elif stderr and is_error:
        output_parts.append(f"Error: {stderr}")

    result = (
        "\n\n".join(output_parts)
        if output_parts
        else "Command completed with no output."
    )

    return result
