import json
from typing import Dict, Any, Literal, List
from langgraph.graph import START, StateGraph, END
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import (
    AIMessage,
    HumanMessage,
    SystemMessage,
    ToolMessage,
    BaseMessage,
)
from anthropic import BadRequestError as AnthropicBadRequestError
from github.PullRequest import PullRequest

from blitzy_utils.common import (
    download_text_file_from_gcs_using_admin_service,
    upload_text_to_gcs_using_admin_service,
)
from blitzy_utils.consts import (
    DEFAULT_MAX_RETRIES,
    DOCUMENTATION_FOLDER_PATH,
    PROJECT_GUIDE_NAME,
    TECH_SPECIFICATION_NAME,
)
from blitzy_utils.logger import logger
from blitzy_utils.scm import (
    create_all_pull_requests,
    setup_github_branch,
    download_repository_to_disk,
    get_head_commit_hash,
    create_github_commit,
    push_pull_latest_from_repository,
)
from blitzy_utils.disk import write_file_to_disk, get_cwd
from blitzy_utils.enums import <PERSON>prop<PERSON>ommand

from blitzy_platform_shared.common.utils import (
    archie_exponential_retry,
    clean_path,
    amake_llm_request,
    process_messages_with_tool_call,
    process_tool_call,
)
from blitzy_platform_shared.common.tools import ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION
from blitzy_platform_shared.code_graph.utils import (
    is_source_adjacent_file,
    is_source_file,
)
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.document.tools import get_tech_spec_section
from blitzy_platform_shared.document.prompts import (
    SUMMARY_OF_CHANGES_INPUT,
    TECH_SPEC_SECTIONS_INPUT,
)
from blitzy_platform_shared.code_generation.tools import (
    ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION,
    ANTHROPIC_TEXT_EDITOR_TOOL_NAME,
    ANTHROPIC_BASH_TOOL_NAME,
    ANTHROPIC_BASH_TOOL_DEFINITION,
)
from blitzy_platform_shared.code_generation.models import (
    AnthropicTextEditorCommand,
    FileSchemaType,
    FileOrFolderStatus,
)

from lib.blitzy.prompts import (
    CREATE_FILE_SYSTEM_PROMPT_TEMPLATE,
    AGENT_PERSONA_PROMPT,
    CREATE_FILE_INPUTS,
    CREATE_RULES_PROMPTLET,
    COMMON_RULES_PROMPTLET,
    ASSIGNED_FILE_PATH_INPUT,
    FILE_SUMMARY_INPUT,
    FILE_REQUIREMENTS_INPUT,
    FILE_CHANGES_INPUT,
    UPDATE_FILE_INPUTS,
    UPDATE_FILE_SYSTEM_PROMPT_TEMPLATE,
    UPDATE_RULES_PROMPTLET,
    FILE_DEPENDS_ON_INPUT,
    SOURCE_FILES_INPUT,
    INTERNAL_IMPORTS_INPUT,
    EXTERNAL_IMPORTS_INPUT,
    EXPORTS_INPUT,
    NEW_INTERNAL_IMPORTS_INPUT,
    NEW_EXTERNAL_IMPORTS_INPUT,
    NEW_EXPORTS_INPUT,
    CREATED_FILE_DEP_MAP_ADDENDUM,
    DEP_MAP_INPUT,
    UPDATED_FILE_DEP_MAP_ADDENDUM,
    SETUP_AGENT_PERSONA_PROMPT,
    SETUP_AGENT_SYSTEM_PROMPT_TEMPLATE,
    SETUP_INPUTS,
    SETUP_RULES_PROMPTLET,
    CWD_INPUT,
    FILE_VALIDATION_SYSTEM_PROMPT_TEMPLATE,
    VALIDATION_AGENT_PERSONA_PROMPT,
    VALIDATION_RULES_PROMPTLET,
    FILE_VALIDATION_INPUTS,
    IS_DEPENDENCY_FILE_INPUT,
    SESSION_COMMANDS_INPUT,
    AGENT_ACTION_LOGS_INPUT,
    IS_NEW_REPO_INPUT,
    FINAL_VALIDATOR_SYSTEM_PROMPT_TEMPLATE,
    FINAL_VALIDATOR_PERSONA_PROMPT,
    FINAL_VALIDATION_INPUTS,
    FINAL_VALIDATION_RULES_PROMPTLET,
    PROJECT_GUIDE_COMMIT_MESSAGE,
    TECH_SPEC_COMMIT_MESSAGE,
)

from .state import ReverseCodeState, get_state
from .tools import (
    handle_text_editor_view_command,
    handle_text_editor_str_replace_command,
    STR_REPLACE_UPDATE_FAILURE_PREFIX,
    mark_file_unchanged,
    handle_text_editor_insert_command,
    UNCHANGED_FILE_TOOL_NAME,
    mark_setup_complete,
    SETUP_COMPLETE_TOOL_NAME,
    ProcessMode,
    get_dependency_map,
    get_processed_files,
    mark_file_validated,
    VALIDATED_FILE_TOOL_NAME,
    BRANCH_VALIDATION_TOOL_NAME,
    mark_branch_validated,
    complete_file,
    COMPLETE_FILE_TOOL_NAME,
)
from .bash import BashSession, handle_bash_tool_response

AGENT_EMAIL = "<EMAIL>"
AGENT_USERNAME = "Blitzy Agent"

base_tools = [get_tech_spec_section]
tool_node_tools = base_tools + [
    mark_file_unchanged,
    mark_setup_complete,
    get_processed_files,
    get_dependency_map,
    mark_file_validated,
    mark_branch_validated,
    complete_file,
]

generator_tools = tool_node_tools + [
    ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION,
    ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION,
]
setup_tools = base_tools + [
    ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION,
    ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION,
    ANTHROPIC_BASH_TOOL_DEFINITION,
    get_dependency_map,
    mark_setup_complete,
]
validator_tools = base_tools + [
    ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION,
    ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION,
    ANTHROPIC_BASH_TOOL_DEFINITION,
    get_processed_files,
    get_dependency_map,
    mark_file_validated,
]
final_validator_tools = base_tools + [
    ANTHROPIC_TEXT_EDITOR_TOOL_DEFINITION,
    ANTHROPIC_WEB_SEARCH_TOOL_DEFINITION,
    ANTHROPIC_BASH_TOOL_DEFINITION,
    mark_branch_validated,
]


class ReverseCodeGeneratorHelper:
    def __init__(
        self,
        generator_llm: BaseChatModel,
        validator_llm: BaseChatModel,
        final_validator_llm: BaseChatModel,
        setup_llm: BaseChatModel,
        fallback_llms: List[BaseChatModel],
        job_metadata: Dict[str, Any],
        blob_name: str,
        bucket_name: str,
        storage_client,
        file_mapping: Dict[str, List[Dict[str, Any]]],
        head_commit_hash: str,
        github_server: str,
        state_metadata_filename: str,
        graph_builder: CodeGraphBuilder,
        file_schemas: FileSchemaType,
        files_list: List[str],
        dependency_map: Dict[str, Dict[str, Any]],
        tech_spec_parsed: Dict[str, str],
        tech_spec: str,
    ):
        self.graph_builder = graph_builder
        self.generator_llm = generator_llm
        self.validator_llm = validator_llm
        self.final_validator_llm = final_validator_llm
        self.setup_llm = setup_llm
        self.fallback_llms = fallback_llms
        self.blob_name = blob_name
        self.bucket_name = bucket_name
        self.storage_client = storage_client
        self.job_metadata = job_metadata
        self.head_commit_hash = head_commit_hash
        self.github_server = github_server
        self.state_metadata_filename = state_metadata_filename
        self.dependency_map = dependency_map
        self.tech_spec = tech_spec

        self.company_id = self.job_metadata["company_id"]
        self.repo_id = self.job_metadata["repo_id"]
        self.dest_repo_id = self.job_metadata["dest_repo_id"]
        self.branch_id = self.job_metadata["branch_id"]
        self.user_id = self.job_metadata["user_id"]
        self.repo_name = self.job_metadata["repo_name"]
        self.create_dest_repo = self.job_metadata["is_new_dest_repo"]
        self.dest_repo_name = self.repo_name
        if self.create_dest_repo:
            self.dest_repo_name = self.job_metadata["dest_repo_name"]
        self.dest_branch_name = self.job_metadata["dest_branch_name"]
        self.base_branch_name = self.job_metadata["branch_name"]
        self.tech_spec_parsed = tech_spec_parsed
        self.git_project_repo_id = self.job_metadata["git_project_repo_id"]

        self.files_map = file_mapping.copy()

        self.file_schemas = file_schemas
        self.files_list = files_list
        self.bash_session = None
        self.setup_log = ""
        self.project_guide = ""

    def create_graph(self) -> StateGraph:
        # Define the graph
        rc_generator = StateGraph(ReverseCodeState)

        # Add nodes
        rc_generator.add_node("setup", self.setup)
        rc_generator.add_node("setup_branch", self.setup_branch)
        rc_generator.add_node("setup_file", self.setup_file)
        rc_generator.add_node("create_file", self.create_file)
        rc_generator.add_node("update_file", self.update_file)
        rc_generator.add_node("delete_file", self.delete_file)
        rc_generator.add_node("validate_file", self.validate_file)
        rc_generator.add_node("post_process_file", self.post_process_file)
        rc_generator.add_node("validate_branch", self.validate_branch)
        rc_generator.add_node("teardown", self.teardown)

        rc_generator.add_conditional_edges(
            "setup_file",
            self.setup_router,
            {
                "create": "create_file",
                "update": "update_file",
                "delete": "delete_file",
                "end": "validate_branch",
            },
        )

        rc_generator.add_conditional_edges(
            "post_process_file",
            self.file_router,
            {"continue": "setup_file", "end": "validate_branch"},
        )

        rc_generator.add_conditional_edges(
            "teardown", self.file_router, {"continue": "setup_file", "end": END}
        )

        # Set the entry point
        rc_generator.add_edge(START, "setup")
        rc_generator.add_edge("setup", "setup_branch")
        rc_generator.add_edge("setup_branch", "setup_file")
        rc_generator.add_edge("create_file", "validate_file")
        rc_generator.add_edge("update_file", "validate_file")
        rc_generator.add_edge("delete_file", "validate_file")
        rc_generator.add_edge("validate_file", "post_process_file")
        rc_generator.add_edge("validate_branch", "teardown")
        return rc_generator

    def setup_router(
        self, state: ReverseCodeState
    ) -> Literal["create", "update", "delete", "end"]:
        if not state["current_file_info"] or state["file_index"] >= len(
            self.files_list
        ):
            return "end"
        if state["current_file_info"]["status"] == "CREATED":
            return "create"
        elif state["current_file_info"]["status"] == "DELETED":
            return "delete"
        else:
            return "update"

    def file_router(self, state: ReverseCodeState) -> Literal["continue", "end"]:
        if state["file_index"] >= len(self.files_list):
            return "end"
        return "continue"

    @archie_exponential_retry()
    async def setup(self, state: ReverseCodeState) -> Dict[str, Any]:
        state["pr_data"] = {}
        state["project_guide"] = ""

        if state["resume"]:
            # Restore state
            logger.info("Attempting to resume state")
            try:
                download_file_path = f"{self.blob_name}/{self.state_metadata_filename}"
                state_metadata: Dict[str, str] = json.loads(
                    download_text_file_from_gcs_using_admin_service(
                        file_path=download_file_path,
                        company_id=self.company_id,
                    )
                )

            except Exception as e:
                logger.warning(f"Failed to resume, running a fresh operation")
                state["resume"] = False
                return await self.setup(state=state)
            state["file_index"] = state_metadata["file_index"]
            state["current_file_info"] = state_metadata["current_file_info"]
            state["retry_count"] = state_metadata["retry_count"]
            state["json_retry_count"] = state_metadata["json_retry_count"]
            state["target_file_path"] = state_metadata["target_file_path"]
            state["agent_log"] = state_metadata["agent_log"]
            state["current_session_cmds"] = state_metadata["current_session_cmds"]
            state["processed_files"] = set(state_metadata["processed_files"])
        else:
            state["file_index"] = 0
            state["current_file_info"] = {}
            state["retry_count"] = 0
            state["json_retry_count"] = 0
            state["target_file_path"] = ""
            state["agent_log"] = ""
            state["current_session_cmds"] = []
            state["processed_files"] = set()

        delete_existing_branch = not state["resume"]
        setup_github_branch(
            repo_name=self.dest_repo_name,
            user_id=self.user_id,
            server=self.github_server,
            repo_id=self.dest_repo_id,
            branch_name=self.dest_branch_name,
            base_branch=self.base_branch_name,
            create_new_branch=True,
            delete_existing_branch=delete_existing_branch,
            git_project_repo_id=self.git_project_repo_id,
        )

        logger.info("Downloading all source branch files to local container")
        download_repository_to_disk(
            repo_name=self.repo_name,
            branch_name=self.base_branch_name,
            user_id=self.user_id,
            server=self.github_server,
            commit_hash=self.head_commit_hash,
            repo_id=self.repo_id,
            git_project_repo_id=self.git_project_repo_id,
        )
        logger.info("Source branch files downloaded successfully.")

        logger.info("Downloading all destination branch files to local container")
        dest_head_commit_hash = get_head_commit_hash(
            repo_name=self.dest_repo_name,
            user_id=self.user_id,
            server=self.github_server,
            branch_name=self.dest_branch_name,
            repo_id=self.dest_repo_id,
            git_project_repo_id=self.git_project_repo_id,
        )
        download_repository_to_disk(
            repo_name=self.dest_repo_name,
            branch_name=self.dest_branch_name,
            user_id=self.user_id,
            server=self.github_server,
            commit_hash=dest_head_commit_hash,
            repo_id=self.dest_repo_id,
            git_project_repo_id=self.git_project_repo_id,
        )
        logger.info("Destination branch files downloaded successfully.")

        await self.configure_local_vm()

        return get_state(state=state)

    async def configure_local_vm(self):
        await self.restart_bash_session()

        logger.info(f"Setting git config")
        config_command = (
            f'git config --global user.email "{AGENT_EMAIL}" && '
            f'git config --global user.name "{AGENT_USERNAME}"'
        )
        await handle_bash_tool_response(
            command=config_command, session=self.bash_session
        )

    @archie_exponential_retry()
    async def setup_branch(self, state: ReverseCodeState) -> Dict[str, Any]:
        if self.job_metadata["job_type"] == BackpropCommand.DOCUMENT_CODE.value:
            logger.info(f'Skipping validation for mode {self.job_metadata["job_type"]}')
            return get_state(state=state)

        logger.info(f"Setting up branch {self.dest_branch_name}")

        messages = [
            SystemMessage(
                content=[
                    {
                        "type": "text",
                        "text": SETUP_AGENT_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=SETUP_AGENT_PERSONA_PROMPT,
                            inputs=SETUP_INPUTS,
                            rules=SETUP_RULES_PROMPTLET,
                        ),
                        "cache_control": {"type": "ephemeral"},
                    }
                ]
            ),
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": SUMMARY_OF_CHANGES_INPUT.format(
                            summary=state["tech_spec_first_n"]
                        ),
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(
                                list(self.tech_spec_parsed.keys())
                            )
                        ),
                        "cache_control": {"type": "ephemeral"},
                    },
                    {
                        "type": "text",
                        "text": CWD_INPUT.format(
                            cwd=get_cwd(
                                repo_name=self.dest_repo_name,
                                branch_name=self.dest_branch_name,
                            )
                        ),
                    },
                    {
                        "type": "text",
                        "text": IS_NEW_REPO_INPUT.format(
                            is_new_repo=self.create_dest_repo
                        ),
                    },
                ]
            ),
        ]

        await self.process_file(
            state=state, messages=messages, llm=self.setup_llm, mode=ProcessMode.SETUP
        )

        logger.info(f"Branch {self.dest_branch_name} setup complete")

        return get_state(state=state)

    async def restart_bash_session(self) -> bool:
        if self.bash_session:
            await self.bash_session.stop()
        self.bash_session = BashSession(
            # f"{get_disk_path(path="", repo_name=self.dest_repo_name, branch_name=self.dest_branch_name)}"
            working_directory=get_cwd(
                repo_name=self.dest_repo_name, branch_name=self.dest_branch_name
            )
        )
        start_result, is_error = await self.bash_session.start()
        if is_error:
            logger.error(f"Bash session start Error: {start_result}")
        else:
            logger.info(f"Bash session restarted: {start_result}")
        return is_error

    async def setup_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path = self.files_list[state["file_index"]]
        state["current_file_info"] = self.file_schemas[file_path]
        state["target_file_path"] = clean_path(state["current_file_info"]["dest_path"])

        return get_state(state=state)

    @archie_exponential_retry()
    async def create_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path: str = state["target_file_path"]

        if not is_source_file(file_path=file_path) and not is_source_adjacent_file(
            file_path=file_path
        ):
            logger.warning(f"skipping creating new file: {file_path}")
            return get_state(state=state)

        file_summary = state["current_file_info"]["summary"]
        requirements = state["current_file_info"].get("requirements", "")
        key_changes = state["current_file_info"]["key_changes"]
        source_files = state["current_file_info"]["source_files"]
        internal_imports = state["current_file_info"]["internal_imports"]
        external_imports = state["current_file_info"]["external_imports"]
        exports = state["current_file_info"]["exports"]

        depends_on_files = state["current_file_info"]["depends_on_files"]
        is_dependency_file = state["current_file_info"]["is_dependency_file"]

        logger.info(f"creating new file: {file_path}")

        # logger.info(f'imported files: {imported_files}')

        system_rules = f"{COMMON_RULES_PROMPTLET}\n\n{CREATE_RULES_PROMPTLET}"
        base_human_msg = [
            {
                "type": "text",
                "text": SUMMARY_OF_CHANGES_INPUT.format(
                    summary=state["tech_spec_first_n"]
                ),
            },
            {
                "type": "text",
                "text": TECH_SPEC_SECTIONS_INPUT.format(
                    tech_spec_sections=json.dumps(list(self.tech_spec_parsed.keys()))
                ),
            },
            {"type": "text", "text": ASSIGNED_FILE_PATH_INPUT.format(path=file_path)},
            {"type": "text", "text": FILE_SUMMARY_INPUT.format(summary=file_summary)},
            {
                "type": "text",
                "text": SOURCE_FILES_INPUT.format(source_files=source_files),
            },
            {
                "type": "text",
                "text": FILE_REQUIREMENTS_INPUT.format(requirements=requirements),
            },
            {"type": "text", "text": FILE_CHANGES_INPUT.format(changes=key_changes)},
            {
                "type": "text",
                "text": FILE_DEPENDS_ON_INPUT.format(depends_on_files=depends_on_files),
            },
        ]
        if is_dependency_file:
            system_rules += f"\n\n{CREATED_FILE_DEP_MAP_ADDENDUM}"
            human_msg_content = base_human_msg + [
                {
                    "type": "text",
                    "text": DEP_MAP_INPUT.format(
                        dep_map=json.dumps(self.dependency_map)
                    ),
                    "cache_control": {"type": "ephemeral"},
                }
            ]
        else:
            human_msg_content = base_human_msg + [
                {
                    "type": "text",
                    "text": INTERNAL_IMPORTS_INPUT.format(
                        internal_imports=internal_imports
                    ),
                },
                {
                    "type": "text",
                    "text": EXTERNAL_IMPORTS_INPUT.format(
                        external_imports=external_imports
                    ),
                },
                {
                    "type": "text",
                    "text": EXPORTS_INPUT.format(exports=exports),
                    "cache_control": {"type": "ephemeral"},
                },
            ]

        messages = [
            SystemMessage(
                content=[
                    {
                        "type": "text",
                        "text": CREATE_FILE_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=AGENT_PERSONA_PROMPT,
                            inputs=CREATE_FILE_INPUTS,
                            rules=system_rules,
                        ),
                        "cache_control": {"type": "ephemeral"},
                    }
                ]
            ),
            HumanMessage(content=human_msg_content),
        ]

        await self.process_file(
            state=state,
            messages=messages,
            llm=self.generator_llm,
            mode=ProcessMode.GENERATE,
        )
        state["processed_files"].add(state["target_file_path"])

        logger.info(f"finished creating file: {state["target_file_path"]}")

        return get_state(state=state)

    async def process_file(
        self,
        state: ReverseCodeState,
        messages: List[BaseMessage],
        llm: BaseChatModel,
        mode: ProcessMode,
    ):
        if mode != ProcessMode.GENERATE:
            await self.restart_bash_session()
        orig_messages = messages.copy()
        while True:
            messages = orig_messages.copy()
            try:
                file_processed = False
                response: AIMessage = await amake_llm_request(
                    llm=llm, messages=messages
                )
                tool_calls = response.tool_calls
                while len(tool_calls):
                    messages.append(response)
                    total_tokens = response.usage_metadata["total_tokens"]
                    for tool_call in tool_calls:
                        tool_name = tool_call["name"]
                        is_error = False
                        args = tool_call.get("args", None)
                        if tool_name == ANTHROPIC_TEXT_EDITOR_TOOL_NAME:
                            if not args:
                                tool_result = f"Invalid tool call. The {ANTHROPIC_TEXT_EDITOR_TOOL_NAME} tool must always provide a valid args dict."
                            else:
                                command: AnthropicTextEditorCommand = args.get(
                                    "command", ""
                                )
                                if not command:
                                    tool_result = f"Invalid tool call. The {ANTHROPIC_TEXT_EDITOR_TOOL_NAME} tool must always define a valid command in the args dict."
                                    is_error = True
                                path: str = args.get("path", "")
                                if not path:
                                    is_error = True
                                    tool_result = "Invalid tool call. The str_replace command must always define a valid path in the args dict."
                                elif command == AnthropicTextEditorCommand.view.value:
                                    view_range = args.get("view_range", [1, -1])
                                    if not isinstance(view_range, list):
                                        logger.warning(
                                            f"View range is not a list: {view_range}"
                                        )
                                        is_error = True
                                        tool_result = "Invalid tool call. The view_range argument must be a valid list of numbers."
                                    else:
                                        logger.info(
                                            f"Handling text editor view request: {args}"
                                        )
                                        tool_result, is_error = (
                                            handle_text_editor_view_command(
                                                path=path,
                                                assigned_path=state["target_file_path"],
                                                file_mapping=self.files_map,
                                                company_id=self.company_id,
                                                repo_id=self.repo_id,
                                                branch_id=self.branch_id,
                                                repo_name=self.repo_name,
                                                dest_repo_name=self.dest_repo_name,
                                                is_new_dest_repo=self.create_dest_repo,
                                                branch_name=self.base_branch_name,
                                                dest_branch_name=self.dest_branch_name,
                                                graph_builder=self.graph_builder,
                                                view_range=view_range,
                                                mode=mode,
                                            )
                                        )
                                elif (
                                    command
                                    == AnthropicTextEditorCommand.str_replace.value
                                ):
                                    logger.info(
                                        f"Handling text editor str_replace request: {path}"
                                    )
                                    if not "old_str" in args or not "new_str" in args:
                                        tool_result = "Invalid tool call. The str_replace command must always provide both, new_str and old_str."
                                        is_error = True
                                    else:
                                        file_update_result, updated_path, is_error = (
                                            handle_text_editor_str_replace_command(
                                                path=path,
                                                assigned_path=state["target_file_path"],
                                                old_str=args["old_str"],
                                                new_str=args["new_str"],
                                                repo_name=self.repo_name,
                                                dest_repo_name=self.dest_repo_name,
                                                branch_name=self.base_branch_name,
                                                dest_branch_name=self.dest_branch_name,
                                                mode=mode,
                                            )
                                        )
                                        updated_path = clean_path(path=updated_path)
                                        if (
                                            STR_REPLACE_UPDATE_FAILURE_PREFIX
                                            in file_update_result
                                        ):
                                            is_error = True
                                            tool_result = file_update_result
                                        else:
                                            tool_result = f"File at path {updated_path} updated successfully"
                                elif command == AnthropicTextEditorCommand.create.value:
                                    split_path = path.split(":")
                                    if (
                                        len(split_path) != 2
                                        or split_path[0] != "dest_file"
                                    ):
                                        split_path = ["dest_file", path]
                                    updated_path = clean_path(split_path[1])
                                    file_text = args.get("file_text", "")
                                    if not file_text:
                                        logger.warning(
                                            f"Could not find file_text while creating file"
                                        )
                                        tool_result = f'Could not create file as no "file_text" was provided for: {path}. When using the create command, always provide a "file_text" argument with initial contents of the file that you can extend using the str_replace command'
                                        is_error = True
                                    else:
                                        write_file_to_disk(
                                            file_path=updated_path,
                                            file_text=file_text,
                                            repo_name=self.dest_repo_name,
                                            branch_name=self.dest_branch_name,
                                        )
                                        tool_result = f"File created successfully for path: {updated_path}"
                                elif command == AnthropicTextEditorCommand.insert.value:
                                    logger.info(
                                        f"Handling text editor insert request: {path}"
                                    )
                                    if (
                                        not "insert_line" in args
                                        or not "new_str" in args
                                    ):
                                        tool_result = "Invalid tool call. The insert command must always provide both, insert_line and new_str."
                                    else:
                                        file_update_result, updated_path, is_error = (
                                            handle_text_editor_insert_command(
                                                path=path,
                                                assigned_path=state["target_file_path"],
                                                insert_line=args["insert_line"],
                                                new_str=args["new_str"],
                                                repo_name=self.repo_name,
                                                dest_repo_name=self.dest_repo_name,
                                                branch_name=self.base_branch_name,
                                                dest_branch_name=self.dest_branch_name,
                                                mode=mode,
                                            )
                                        )
                                        updated_path = clean_path(path=updated_path)
                                        if (
                                            file_update_result
                                            == STR_REPLACE_UPDATE_FAILURE_PREFIX
                                            in file_update_result
                                        ):
                                            is_error = True
                                            tool_result = file_update_result
                                        else:
                                            tool_result = f"File at path {updated_path} updated successfully"
                                tool_message = ToolMessage(
                                    content=tool_result,
                                    name=ANTHROPIC_TEXT_EDITOR_TOOL_NAME,
                                    tool_call_id=tool_call["id"],
                                )
                        elif tool_name == ANTHROPIC_BASH_TOOL_NAME:
                            logger.info(f"Handling bash tool request: {args}")
                            if not args:
                                tool_result = f"Invalid tool call. The {ANTHROPIC_BASH_TOOL_NAME} tool must always provide a valid args dict."
                            else:
                                restart: bool = args.get("restart", False)
                                command: str = args.get("command", "")
                                if not command and not restart:
                                    tool_result = f"Invalid tool call. The {ANTHROPIC_BASH_TOOL_NAME} tool must always define a valid command or restart flag in the args dict."
                                if restart:
                                    await self.restart_bash_session()
                                    state["current_session_cmds"] = []
                                    tool_result = "Bash session restarted successfully."
                                else:
                                    state["current_session_cmds"].append(command)
                                    state["current_session_cmds"] = state[
                                        "current_session_cmds"
                                    ][-12:]
                                    tool_result = await handle_bash_tool_response(
                                        command=command, session=self.bash_session
                                    )
                            tool_message = ToolMessage(
                                content=tool_result,
                                name=ANTHROPIC_BASH_TOOL_NAME,
                                tool_call_id=tool_call["id"],
                            )
                        else:
                            if tool_name in [
                                COMPLETE_FILE_TOOL_NAME,
                                UNCHANGED_FILE_TOOL_NAME,
                                SETUP_COMPLETE_TOOL_NAME,
                                VALIDATED_FILE_TOOL_NAME,
                                BRANCH_VALIDATION_TOOL_NAME,
                            ]:
                                file_processed = True
                                if tool_name == SETUP_COMPLETE_TOOL_NAME:
                                    state["agent_log"] = args["updated_session_state"]
                                    state["setup_log"] = state["agent_log"]
                                if tool_name == VALIDATED_FILE_TOOL_NAME:
                                    state["agent_log"] = args["updated_session_state"]
                                if tool_name == BRANCH_VALIDATION_TOOL_NAME:
                                    state["agent_log"] = args["updated_session_state"]
                                    state["project_guide"] = args["project_guide"]
                                    needs_further_validation = args[
                                        "needs_further_validation"
                                    ]
                                    all_modules_code_compiled = args[
                                        "all_modules_code_compiled"
                                    ]
                                    all_modules_unit_tests_passed = args[
                                        "all_modules_unit_tests_passed"
                                    ]
                                    all_dependencies_installed = args[
                                        "all_dependencies_installed"
                                    ]
                                    all_modules_run = args["all_modules_run"]
                                    if (
                                        needs_further_validation
                                        or not (
                                            all_modules_code_compiled
                                            and all_modules_unit_tests_passed
                                            and all_dependencies_installed
                                            and all_modules_run
                                        )
                                        and state["json_retry_count"]
                                        < DEFAULT_MAX_RETRIES
                                    ):
                                        logger.info(f"Retrying validation")
                                        file_processed = False
                                        state["json_retry_count"] += 1
                                git_result = await self.sync_local_git_state()
                                logger.info(f"Local git sync result: {git_result}")
                            tools_config = {
                                "tech_spec_parsed": self.tech_spec_parsed,
                                "assigned_path": state["target_file_path"],
                                "dest_repo_name": self.dest_repo_name,
                                "branch_name": self.base_branch_name,
                                "dest_branch_name": self.dest_branch_name,
                                "head_commit_hash": self.head_commit_hash,
                                "create_dest_repo": self.create_dest_repo,
                                "repo_id": self.repo_id,
                                "user_id": self.user_id,
                                "github_server": self.github_server,
                                "processed_files": list(state["processed_files"]),
                                "dependency_map": self.dependency_map,
                                "git_project_repo_id": self.git_project_repo_id,
                            }
                            tool_message = process_tool_call(
                                tool_call=tool_call,
                                tools_list=tool_node_tools,
                                tools_config=tools_config,
                            )
                        messages = process_messages_with_tool_call(
                            tool_message=tool_message,
                            messages=messages,
                            total_tokens_before_tool=total_tokens,
                            llm=llm,
                            is_error=is_error,
                        )

                    # logger.info(f'sending tool response back to llm for file: {file_path}')
                    response: AIMessage = await amake_llm_request(
                        llm=llm, messages=messages
                    )
                    tool_calls = response.tool_calls

                if not file_processed:
                    if state["json_retry_count"] < DEFAULT_MAX_RETRIES:
                        logger.warning("failed to find code block, retrying")
                        state["json_retry_count"] += 1
                    else:
                        logger.error(
                            f'exhaused all retries due to missing output, skipping file: {state["target_file_path"]}'
                        )
                        state["retry_count"] = 0
                        state["json_retry_count"] = 0
                        break
                else:
                    state["json_retry_count"] = 0
                    state["retry_count"] = 0
                    break
            except AnthropicBadRequestError as e:
                if state["retry_count"] < DEFAULT_MAX_RETRIES:
                    logger.warning(f"Anthropic bad request error: {e}")
                    state["retry_count"] += 1
                    state["json_retry_count"] = 0
                else:
                    logger.error(f"Anthropic bad request error: {e}")
                    state["retry_count"] = 0
                    state["json_retry_count"] = 0
                    break

    async def sync_local_git_state(self) -> str:
        """Synchronize local repository from origin."""
        await self.restart_bash_session()

        # Step 1: Stash local changes
        stash_command = "git stash --include-untracked"
        stash_result = await handle_bash_tool_response(
            command=stash_command, session=self.bash_session
        )

        # Step 2: Pull commits from origin
        pull_result = push_pull_latest_from_repository(
            repo_name=self.dest_repo_name,
            branch_name=self.dest_branch_name,
            user_id=self.user_id,
            repo_id=self.dest_repo_id,
            server=self.github_server,
            git_project_repo_id=self.git_project_repo_id,
        )

        # Step 3: Restore local changes
        stash_command = "git stash list"
        stash_result = await handle_bash_tool_response(
            command=stash_command, session=self.bash_session
        )

        pop_result = None
        if stash_result and "stash@{0}" in stash_result:
            pop_command = "git stash pop"
            pop_result = await handle_bash_tool_response(
                command=pop_command, session=self.bash_session
            )

        # Step 4: Run git status and get the result
        status_command = "git status --porcelain"
        status_result = await handle_bash_tool_response(
            command=status_command, session=self.bash_session
        )

        # Compile the results
        result = (
            f"Git sync completed\n"
            f"---\n"
            f"Stash result: {stash_result}\n"
            f"Push pull result: {pull_result}\n"
        )

        if pop_result:
            result += f"Stash pop result: {pop_result}\n"

        result += f"---\nCurrent git status:\n{status_result}"

        return result

    @archie_exponential_retry()
    async def update_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path: str = state["target_file_path"]

        if not is_source_file(file_path=file_path) and not is_source_adjacent_file(
            file_path=file_path
        ):
            logger.warning(f"skipping updating file: {file_path}")
            return get_state(state=state)

        key_changes = state["current_file_info"]["key_changes"]
        depends_on_files = state["current_file_info"]["depends_on_files"]

        new_internal_imports = state["current_file_info"]["new_internal_imports"]
        new_external_imports = state["current_file_info"]["new_external_imports"]
        new_exports = state["current_file_info"]["new_exports"]

        is_dependency_file = state["current_file_info"]["is_dependency_file"]

        logger.info(f"updating new file: {file_path}")

        # logger.info(f'imported files: {imported_files}')

        system_rules = f"{COMMON_RULES_PROMPTLET}\n\n{UPDATE_RULES_PROMPTLET}"
        base_human_msg = [
            {
                "type": "text",
                "text": SUMMARY_OF_CHANGES_INPUT.format(
                    summary=state["tech_spec_first_n"]
                ),
            },
            {
                "type": "text",
                "text": TECH_SPEC_SECTIONS_INPUT.format(
                    tech_spec_sections=json.dumps(list(self.tech_spec_parsed.keys()))
                ),
            },
            {"type": "text", "text": ASSIGNED_FILE_PATH_INPUT.format(path=file_path)},
            {"type": "text", "text": FILE_CHANGES_INPUT.format(changes=key_changes)},
            {
                "type": "text",
                "text": FILE_DEPENDS_ON_INPUT.format(depends_on_files=depends_on_files),
            },
        ]
        if is_dependency_file:
            system_rules += f"\n\n{UPDATED_FILE_DEP_MAP_ADDENDUM}"
            human_msg_content = base_human_msg + [
                {
                    "type": "text",
                    "text": DEP_MAP_INPUT.format(
                        dep_map=json.dumps(self.dependency_map)
                    ),
                    "cache_control": {"type": "ephemeral"},
                }
            ]
        else:
            human_msg_content = base_human_msg + [
                {
                    "type": "text",
                    "text": NEW_INTERNAL_IMPORTS_INPUT.format(
                        new_internal_imports=new_internal_imports
                    ),
                },
                {
                    "type": "text",
                    "text": NEW_EXTERNAL_IMPORTS_INPUT.format(
                        new_external_imports=new_external_imports
                    ),
                },
                {
                    "type": "text",
                    "text": NEW_EXPORTS_INPUT.format(new_exports=new_exports),
                    "cache_control": {"type": "ephemeral"},
                },
            ]

        messages = [
            SystemMessage(
                content=[
                    {
                        "type": "text",
                        "text": UPDATE_FILE_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=AGENT_PERSONA_PROMPT,
                            inputs=UPDATE_FILE_INPUTS,
                            rules=system_rules,
                        ),
                        "cache_control": {"type": "ephemeral"},
                    }
                ]
            ),
            HumanMessage(content=human_msg_content),
        ]

        await self.process_file(
            state=state,
            messages=messages,
            llm=self.generator_llm,
            mode=ProcessMode.GENERATE,
        )
        state["processed_files"].add(state["target_file_path"])

        logger.info(f"finished updating file: {state["target_file_path"]}")

        return get_state(state=state)

    async def delete_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path = state["current_file_info"]["dest_path"]
        state["target_file_path"] = file_path
        return get_state(state=state)

    @archie_exponential_retry()
    async def validate_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        file_path: str = state["target_file_path"]

        logger.info(f"validating file: {file_path}")

        status = state["current_file_info"]["status"]
        is_compiled = state["current_file_info"].get("is_compiled", True)
        if (
            (
                not is_source_file(file_path=file_path)
                and not is_source_adjacent_file(file_path=file_path)
            )
            or (
                status == FileOrFolderStatus.DELETED.value
                or self.job_metadata["job_type"] == BackpropCommand.DOCUMENT_CODE
            )
            or not is_compiled
        ):
            logger.warning(f"skipping validating file: {file_path}")
            return get_state(state=state)

        key_changes = state["current_file_info"]["key_changes"]
        depends_on_files = state["current_file_info"]["depends_on_files"]
        is_dependency_file = state["current_file_info"]["is_dependency_file"]

        messages = [
            SystemMessage(
                content=[
                    {
                        "type": "text",
                        "text": FILE_VALIDATION_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=VALIDATION_AGENT_PERSONA_PROMPT,
                            inputs=FILE_VALIDATION_INPUTS,
                            rules=VALIDATION_RULES_PROMPTLET,
                        ),
                        "cache_control": {"type": "ephemeral"},
                    }
                ]
            ),
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": SUMMARY_OF_CHANGES_INPUT.format(
                            summary=state["tech_spec_first_n"]
                        ),
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(
                                list(self.tech_spec_parsed.keys())
                            )
                        ),
                    },
                    {
                        "type": "text",
                        "text": ASSIGNED_FILE_PATH_INPUT.format(path=file_path),
                    },
                    {
                        "type": "text",
                        "text": FILE_CHANGES_INPUT.format(changes=key_changes),
                    },
                    {
                        "type": "text",
                        "text": FILE_DEPENDS_ON_INPUT.format(
                            depends_on_files=depends_on_files
                        ),
                    },
                    {
                        "type": "text",
                        "text": IS_DEPENDENCY_FILE_INPUT.format(
                            is_dependency_file=is_dependency_file
                        ),
                    },
                    {
                        "type": "text",
                        "text": SESSION_COMMANDS_INPUT.format(
                            session_commands=json.dumps(state["current_session_cmds"])
                        ),
                    },
                    {
                        "type": "text",
                        "text": CWD_INPUT.format(
                            cwd=get_cwd(
                                repo_name=self.dest_repo_name,
                                branch_name=self.dest_branch_name,
                            )
                        ),
                    },
                    {
                        "type": "text",
                        "text": AGENT_ACTION_LOGS_INPUT.format(
                            agent_action_logs=state["agent_log"]
                        ),
                        "cache_control": {"type": "ephemeral"},
                    },
                ]
            ),
        ]

        await self.process_file(
            state=state,
            messages=messages,
            llm=self.validator_llm,
            mode=ProcessMode.VALIDATE,
        )

        logger.info(f"finished validating file: {state["target_file_path"]}")

        return get_state(state=state)

    @archie_exponential_retry()
    def post_process_file(self, state: ReverseCodeState) -> Dict[str, Any]:
        self.upload_state(state=state)

        state["file_index"] += 1

        return get_state(state=state)

    @archie_exponential_retry()
    def upload_state(self, state: ReverseCodeState):
        state_metadata = get_state(state=state)
        state_metadata["processed_files"] = list(state["processed_files"])
        state_metadata_filepath = f"{self.blob_name}/{self.state_metadata_filename}"
        upload_text_to_gcs_using_admin_service(
            file_path=state_metadata_filepath,
            company_id=self.company_id,
            data=json.dumps(state_metadata),
        )

    @archie_exponential_retry()
    async def validate_branch(self, state: ReverseCodeState):
        logger.info(f"validating branch: {self.dest_branch_name}")

        if self.job_metadata["job_type"] == BackpropCommand.DOCUMENT_CODE:
            logger.warning(f"skipping branch validation for {self.dest_branch_name}")
            return get_state(state=state)

        messages = [
            SystemMessage(
                content=[
                    {
                        "type": "text",
                        "text": FINAL_VALIDATOR_SYSTEM_PROMPT_TEMPLATE.format(
                            agent_persona=FINAL_VALIDATOR_PERSONA_PROMPT,
                            inputs=FINAL_VALIDATION_INPUTS,
                            rules=FINAL_VALIDATION_RULES_PROMPTLET,
                        ),
                        "cache_control": {"type": "ephemeral"},
                    }
                ]
            ),
            HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": SUMMARY_OF_CHANGES_INPUT.format(
                            summary=state["tech_spec_first_n"]
                        ),
                    },
                    {
                        "type": "text",
                        "text": TECH_SPEC_SECTIONS_INPUT.format(
                            tech_spec_sections=json.dumps(
                                list(self.tech_spec_parsed.keys())
                            )
                        ),
                    },
                    {
                        "type": "text",
                        "text": CWD_INPUT.format(
                            cwd=get_cwd(
                                repo_name=self.dest_repo_name,
                                branch_name=self.dest_branch_name,
                            )
                        ),
                    },
                    {
                        "type": "text",
                        "text": SESSION_COMMANDS_INPUT.format(
                            session_commands=json.dumps(state["current_session_cmds"])
                        ),
                    },
                    {
                        "type": "text",
                        "text": AGENT_ACTION_LOGS_INPUT.format(
                            agent_action_logs=state["agent_log"]
                        ),
                        "cache_control": {"type": "ephemeral"},
                    },
                ]
            ),
        ]

        await self.process_file(
            state=state,
            messages=messages,
            llm=self.final_validator_llm,
            mode=ProcessMode.VALIDATE,
        )

        logger.info(f"finished validating branch: {self.dest_branch_name}")

        return get_state(state=state)

    def teardown(self, state: ReverseCodeState) -> Dict[str, Any]:
        logger.info(f"Evaluating job end")

        # Commit project guide
        create_github_commit(
            repo_name=self.dest_repo_name,
            repo_id=self.dest_repo_id,
            branch_name=self.dest_branch_name,
            base_branch=self.base_branch_name,
            file_path=f"{DOCUMENTATION_FOLDER_PATH}/{PROJECT_GUIDE_NAME}.md",
            head_commit_hash=self.head_commit_hash,
            content=state["project_guide"],
            create_new_branch=True,
            is_new_repo=self.create_dest_repo,
            user_id=self.user_id,
            server=self.github_server,
            commit_message=PROJECT_GUIDE_COMMIT_MESSAGE,
            git_project_repo_id=self.git_project_repo_id,
        )

        # Commit tech spec
        create_github_commit(
            repo_name=self.dest_repo_name,
            repo_id=self.dest_repo_id,
            branch_name=self.dest_branch_name,
            base_branch=self.base_branch_name,
            file_path=f"{DOCUMENTATION_FOLDER_PATH}/{TECH_SPECIFICATION_NAME}.md",
            head_commit_hash=self.head_commit_hash,
            content=self.tech_spec,
            create_new_branch=True,
            is_new_repo=self.create_dest_repo,
            user_id=self.user_id,
            server=self.github_server,
            commit_message=TECH_SPEC_COMMIT_MESSAGE,
            git_project_repo_id=self.git_project_repo_id,
        )

        if self.base_branch_name != self.dest_branch_name:
            prs: List[PullRequest] = create_all_pull_requests(
                repo_name=self.dest_repo_name,
                repo_id=self.dest_repo_id,
                head_branch=self.dest_branch_name,
                user_id=self.user_id,
                server=self.github_server,
                base_branch=self.base_branch_name,
                is_new_repo=self.create_dest_repo,
                git_project_repo_id=self.git_project_repo_id,
            )
            state["pr_data"] = prs[0] if isinstance(prs[0], dict) else prs[0].raw_data
        return get_state(state=state)
