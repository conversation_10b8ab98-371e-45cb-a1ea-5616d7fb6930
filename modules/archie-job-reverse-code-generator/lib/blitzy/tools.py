import json
from typing import List, Dict, Any, <PERSON><PERSON>
from enum import Enum

from pydantic import BaseModel, Field
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig

from blitzy_utils.disk import read_file_from_disk, write_file_to_disk, get_path_handle, get_cwd
from blitzy_utils.logger import logger

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_platform_shared.code_graph.tools import _get_folder_contents, _get_source_folder_contents
from blitzy_platform_shared.common.utils import clean_path, read_range


STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE = f"""
    Error: Item not found or does not exist yet. Double check your path and if you haven't already, retry your request with the correct prefix based on the following examples:
    - "dest_file:test/file.py" (the new file for verifying your output, from the destination git branch)
    - "dest_folder:test" (other folder paths from the destination git branch shortlisted for changes)
    - "source_file:test/file.rb" (the original, unmodified file being referenced, from the source git branch and NOT from the destination branch being worked on)
    - "source_folder:src/test" (useful for referencing folder contents from the existing repository)
    Remember:Your path must be relative to the repository root and not relative to disk root.
    """
STR_REPLACE_UPDATE_FAILURE_PREFIX = "Error: Could not update"
STR_REPLACE_VIEW_FAILURE_PREFIX = "Error: Could not view"
STR_REPLACE_VIEWER_INCORRECT_FOLDER_PATH_RESPONSE = f"""
    Error: Could not view folder path invalid or not found. You may double check your path and try again with a correct path that you haven't tried so far.
    """
COMPLETE_FILE_TOOL_NAME = "complete_file"
UNCHANGED_FILE_TOOL_NAME = "mark_file_unchanged"
VALIDATED_FILE_TOOL_NAME = "mark_file_validated"
SETUP_COMPLETE_TOOL_NAME = "mark_setup_complete"
DEPENDENCY_MAP_TOOL_NAME = "get_dependency_map"
PROCESSED_FILES_TOOL_NAME = "get_processed_files"
BRANCH_VALIDATION_TOOL_NAME = "mark_branch_validated"


class ProcessMode(str, Enum):
    GENERATE = "GENERATE",
    VALIDATE = "VALIDATE",
    SETUP = "SETUP"


def handle_text_editor_view_command(
    path: str,
    assigned_path: str,
    file_mapping: Dict[str, List[Dict[str, Any]]],
    company_id: str,
    repo_id: str,
    branch_id: str,
    repo_name: str,
    dest_repo_name: str,
    is_new_dest_repo: bool,
    branch_name: str,
    dest_branch_name: str,
    graph_builder: CodeGraphBuilder,
    view_range: List[int],
    mode: ProcessMode
):
    is_error = False
    split_path = get_split_path(
        path=path,
        dest_repo_name=dest_repo_name,
        dest_branch_name=dest_branch_name
    )
    path_handle = get_path_handle(
        path=split_path[1],
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )
    if split_path[0] in ['dest_file', 'source_file']:
        if path_handle.is_dir():
            logger.warning(f'Attempted to retrieve a folder using file prefix, reconciling: {split_path[1]}')
            return try_get_source_or_dest_folder(
                split_path=split_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder,
                is_new_dest_repo=is_new_dest_repo,
                file_mapping=file_mapping,
                mode=mode
            )
        else:
            return try_get_source_or_dest_file(
                split_path=split_path,
                assigned_path=assigned_path,
                repo_name=repo_name,
                dest_repo_name=dest_repo_name,
                branch_name=branch_name,
                dest_branch_name=dest_branch_name,
                view_range=view_range,
                mode=mode
            )
    elif split_path[0] in ['dest_folder', 'source_folder']:
        if path_handle.is_file():
            logger.warning(f'Attempted to retrieve a file using folder prefix, reconciling: {split_path[1]}')
            return try_get_source_or_dest_file(
                split_path=split_path,
                assigned_path=assigned_path,
                repo_name=repo_name,
                dest_repo_name=dest_repo_name,
                branch_name=branch_name,
                dest_branch_name=dest_branch_name,
                view_range=view_range,
                mode=mode
            )
        else:
            return try_get_source_or_dest_folder(
                split_path=split_path,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder,
                is_new_dest_repo=is_new_dest_repo,
                file_mapping=file_mapping,
                mode=mode
            )
    else:
        is_error = True
        return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error


def get_split_path(path: str, dest_repo_name: str, dest_branch_name: str):
    split_path = path.split(':')
    if len(split_path) != 2:
        path_prefix = 'dest_file'
        return [
            path_prefix,
            clean_cwd_in_path(path=path, dest_repo_name=dest_repo_name, dest_branch_name=dest_branch_name)
        ]
    return [
        split_path[0],
        clean_cwd_in_path(path=split_path[1], dest_repo_name=dest_repo_name, dest_branch_name=dest_branch_name)
    ]


def clean_cwd_in_path(path: str, dest_repo_name: str, dest_branch_name: str) -> str:
    path = clean_path(path=path)
    cwd = get_cwd(
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )
    alt_cwd = f'app/{cwd}'
    if alt_cwd in path:
        path = path.replace(alt_cwd, "")
    elif cwd in path:
        path = path.replace(cwd, "")
    return clean_path(path=path)


def try_get_source_or_dest_folder(
    split_path: List[str],
    company_id: str,
    repo_id: str,
    branch_id: str,
    graph_builder: CodeGraphBuilder,
    is_new_dest_repo: bool,
    file_mapping: Dict[str, List[Dict[str, Any]]],
    mode: ProcessMode
) -> Tuple[str, bool]:
    prefix = split_path[0]
    if prefix == 'source_folder':
        folder_contents = _get_source_folder_contents(
            folder_path=split_path[1],
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            graph_builder=graph_builder
        )
        is_error = False
        if not folder_contents and mode == ProcessMode.GENERATE:
            logger.warning(
                f'Attempted to retrieve invalid source_folder, trying with dest_folder: {split_path[1]}')
            ret = _get_folder_contents(
                folder_path=split_path[1],
                include_pending_changes=True,
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder,
                is_new_dest_repo=is_new_dest_repo,
                file_mapping=file_mapping
            )
            if not ret:
                is_error = True
                # This prefix could have been assumed, so return global incorrect format response
                logger.warning(f'Could not find matching dest_folder: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            return ret, is_error
        return folder_contents, is_error
    else:
        ret = _get_folder_contents(
            folder_path=split_path[1],
            include_pending_changes=True,
            company_id=company_id,
            repo_id=repo_id,
            branch_id=branch_id,
            graph_builder=graph_builder,
            is_new_dest_repo=is_new_dest_repo,
            file_mapping=file_mapping
        )
        is_error = False
        if not ret and mode == ProcessMode.GENERATE:
            logger.warning(
                f'Attempted to retrieve invalid dest_folder, trying with source_folder: {split_path[1]}')

            folder_contents = _get_source_folder_contents(
                folder_path=split_path[1],
                company_id=company_id,
                repo_id=repo_id,
                branch_id=branch_id,
                graph_builder=graph_builder
            )
            if not folder_contents:
                is_error = True
                logger.warning(f'Could not find matching source_folder: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            return folder_contents, is_error
        return ret, is_error


def try_get_source_or_dest_file(
    split_path: List[str],
    assigned_path: str,
    repo_name: str,
    dest_repo_name: str,
    branch_name: str,
    dest_branch_name: str,
    view_range: List[int],
    mode: ProcessMode,
    prepend_line_numbers=True,
) -> Tuple[str, bool]:
    prefix = split_path[0]
    if prefix == 'dest_file':
        file_text = read_file_from_disk(
            file_path=split_path[1],
            repo_name=dest_repo_name,
            branch_name=dest_branch_name
        )
        if file_text == "" and split_path[1] != assigned_path and mode == ProcessMode.GENERATE:
            logger.warning(f'Attempted to view invalid dest_file, trying with source_file: {split_path[1]}')
            file_text = read_file_from_disk(
                file_path=split_path[1],
                repo_name=repo_name,
                branch_name=branch_name
            )
            if file_text == "":
                is_error = True
                logger.warning(f'Could not find a matching source_file: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            file_text = read_range(
                view_range=view_range,
                file_text=file_text,
                prepend_line_numbers=prepend_line_numbers
            )
            return file_text, is_error
        is_error = False
        file_text = read_range(
            view_range=view_range,
            file_text=file_text,
            prepend_line_numbers=prepend_line_numbers
        )
        return file_text, is_error
    else:
        file_text = read_file_from_disk(
            file_path=split_path[1],
            repo_name=repo_name,
            branch_name=branch_name
        )
        if file_text == "" and mode == ProcessMode.GENERATE:
            logger.warning(f'Attempted to view invalid source_file, trying with dest_file: {split_path[1]}')
            file_text = read_file_from_disk(
                file_path=split_path[1],
                repo_name=dest_repo_name,
                branch_name=dest_branch_name
            )
            if file_text == "" and split_path[1] != assigned_path:
                is_error = True
                logger.warning(f'Could not find a matching dest_file: {split_path[1]}')
                return STR_REPLACE_VIEWER_INCORRECT_FORMAT_RESPONSE, is_error
            is_error = False
            file_text = read_range(
                view_range=view_range,
                file_text=file_text,
                prepend_line_numbers=prepend_line_numbers
            )
            return file_text, is_error
        is_error = False
        file_text = read_range(
            view_range=view_range,
            file_text=file_text,
            prepend_line_numbers=prepend_line_numbers
        )
        return file_text, is_error


def handle_text_editor_str_replace_command(
    path: str,
    assigned_path: str,
    old_str: str,
    new_str: str,
    repo_name: str,
    dest_repo_name: str,
    branch_name: str,
    dest_branch_name: str,
    mode: ProcessMode
) -> Tuple[str, str, bool]:
    split_path = get_split_path(
        path=path,
        dest_repo_name=dest_repo_name,
        dest_branch_name=dest_branch_name
    )
    if split_path[0] != 'dest_file':
        split_path = [
            "dest_file",
            split_path[1]
        ]
    file_path = split_path[1]
    file_text, _ = try_get_source_or_dest_file(
        split_path=split_path,
        assigned_path=assigned_path,
        repo_name=repo_name,
        dest_repo_name=dest_repo_name,
        branch_name=branch_name,
        dest_branch_name=dest_branch_name,
        view_range=[1, -1],
        prepend_line_numbers=False,
        mode=mode
    )
    if not file_text:
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - dest_file path is invalid or not created yet, create it if this is the path assigned to you, or proceed without it.", split_path[1], True

    last_occurrence = file_text.rfind(old_str)
    if last_occurrence == -1:
        logger.warning(f'Could not perform file replacement for file: {file_path}')
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - old_str not found in file text.", split_path[1], True

    # Perform the replacement
    updated_text = file_text[:last_occurrence] + new_str + file_text[last_occurrence + len(old_str):]

    write_file_to_disk(
        file_path=split_path[1],
        file_text=updated_text,
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )

    return updated_text, split_path[1], False


def handle_text_editor_insert_command(
    path: str,
    assigned_path: str,
    insert_line: int,
    new_str: str,
    repo_name: str,
    dest_repo_name: str,
    branch_name: str,
    dest_branch_name: str,
    mode: ProcessMode
) -> Tuple[str, str, bool]:
    split_path = get_split_path(
        path=path,
        dest_repo_name=dest_repo_name,
        dest_branch_name=dest_branch_name
    )
    if split_path[0] != 'dest_file':
        split_path = [
            "dest_file",
            split_path[1]
        ]
    file_path = split_path[1]
    file_text, _ = try_get_source_or_dest_file(
        split_path=split_path,
        assigned_path=assigned_path,
        repo_name=repo_name,
        dest_repo_name=dest_repo_name,
        branch_name=branch_name,
        dest_branch_name=dest_branch_name,
        view_range=[1, -1],
        prepend_line_numbers=False,
        mode=mode
    )
    if not file_text:
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - dest_file path is invalid or not created yet, create it if this is the path assigned to you, or proceed without it.", split_path[1], True

    # Split file into lines for insertion
    file_text_lines = file_text.split("\n")
    n_lines_file = len(file_text_lines)

    # Validate insert_line (assuming insert_line is passed instead of old_str)
    if insert_line < 0 or insert_line > n_lines_file:
        logger.warning(f'Invalid insert_line parameter for file: {file_path}')
        return f"Error: {STR_REPLACE_UPDATE_FAILURE_PREFIX} {path} - Invalid insert_line: {insert_line}. Should be within [0, {n_lines_file}].", split_path[1], True

    # Split new_str into lines and perform insertion
    new_str_lines = new_str.split("\n")
    updated_text_lines = (
        file_text_lines[:insert_line]
        + new_str_lines
        + file_text_lines[insert_line:]
    )

    # Join lines back into text
    updated_text = "\n".join(updated_text_lines)

    write_file_to_disk(
        file_path=split_path[1],
        file_text=updated_text,
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )

    return updated_text, split_path[1], False


class ConfigInput(BaseModel):
    config: RunnableConfig


class FileUnchangedInput(ConfigInput):
    file_path: str = Field(description="Your assigned file path to mark unchanged")


@tool(args_schema=FileUnchangedInput)
def mark_file_unchanged(
    file_path: str,
    config: RunnableConfig
) -> str:
    """
    Marks a file unchanged, when no changes are needed.
    """
    dest_repo_name = config["configurable"]["dest_repo_name"]
    dest_branch_name = config["configurable"]["dest_branch_name"]
    split_path = get_split_path(
        path=file_path,
        dest_repo_name=dest_repo_name,
        dest_branch_name=dest_branch_name
    )
    if split_path[0] != 'dest_file':
        split_path = [
            "dest_file",
            split_path[1]
        ]
    file_path = split_path[1]
    logger.info(f'Marking file unchanged: {file_path}')
    file_text = read_file_from_disk(
        file_path=file_path,
        repo_name=dest_repo_name,
        branch_name=dest_branch_name
    )
    if file_text == "":
        return f"Error: Invalid file path, double check your file path: {file_path}"
    return f"File at {file_path} marked unchanged successfully"


class CompleteFileInput(ConfigInput):
    file_path: str = Field(description="Path of the file being marked completed")


@tool(args_schema=CompleteFileInput)
def complete_file(
    file_path: str,
    config: RunnableConfig
) -> str:
    """
    Marks an assigned file complete. Useful for submitting your changes to files.
    """

    logger.info(f'File marked completed: {file_path}')

    return f"File at {file_path} marked completed successfully"


@tool(args_schema=ConfigInput)
def get_dependency_map(
    config: RunnableConfig
) -> str:
    "Fetches a map of all new or updated third-party or external dependencies in the project."
    logger.info(f'Fetching dependency map')
    dependency_map: Dict[str, Any] = config["configurable"]["dependency_map"]
    return json.dumps(dependency_map)


@tool(args_schema=ConfigInput)
def get_processed_files(
    config: RunnableConfig
) -> str:
    "Fetches a list of all processed files so far that are expected to exist in your working directory."
    logger.info(f'Fetching processed files')
    processed_files: List[str] = config["configurable"]["processed_files"]
    return json.dumps(processed_files)


class SetupFolderInfo(BaseModel):
    folder_path: str = Field(
        description="Parent folder containing dependency files, \"\" for root"
    )
    dependencies_installed: bool = Field(
        description="Whether you were able to install the dependencies successfully"
    )
    unit_tests_passed: bool = Field(
        description="True if there are unit tests, you were able to run them, and they passed. False otherwise"
    )
    code_compiled: bool = Field(
        description="Whether you were able to compile the code"
    )


class SetupCompleteInput(BaseModel):
    processed_folders: List[SetupFolderInfo] = Field(
        description="Detailed list of all folders containing dependency files and representing different components"
    )
    setup_successful: bool = Field(
        description="Whether you would consider the overall setup as successful"
    )
    updated_session_state: str = Field(
        description="Detailed state of the current session, listing all commands that were executed and the results observed."
    )
    setup_guide: str = Field(
        description="Markdown document containing detailed instructions and commands that you used to setup the repository, install all dependencies, build and run the codebase and tests."
    )


@tool(args_schema=SetupCompleteInput)
def mark_setup_complete(
    processed_folders: List[str],
    setup_successful: bool,
    updated_session_state: str,
    setup_guide: str
) -> str:
    "Marks setup complete for a given set of folders, and updates the session state with a new summary for the next agent."
    logger.info(
        f'Setup was marked complete with success: {setup_successful} for files: {processed_folders}, session state: {updated_session_state}, setup guide: {setup_guide}')
    return "Setup marked complete successfully"


class FileValidationInput(BaseModel):
    file_path: str = Field(
        description="File path that was validated"
    )
    all_dependencies_installed_successfully: bool = Field(
        description="Whether you were able to install all dependencies successfully and fix any issues encountered."
    )
    file_unit_tests_passed: bool = Field(
        description="True if you were able to create and run ad-hoc or actual unit tests for this file, and make them all pass. False otherwise"
    )
    module_test_suite_passed: bool = Field(
        description="True if you were able to run the full suite of unit tests for the entire module or codebase and make them all pass. False otherwise"
    )
    file_compiled: bool = Field(
        description="Whether you were able to compile your assigned file"
    )
    module_compiled: bool = Field(
        description="Whether you were able to compile the code for the entire module or codebase, including your assigned file"
    )
    updated_session_state: str = Field(
        description="Detailed state of the current session, listing all commands that were executed and the results observed, to help the next agent."
    )


@tool(args_schema=FileValidationInput)
def mark_file_validated(
    file_path: str,
    all_dependencies_installed_successfully: bool,
    file_unit_tests_passed: bool,
    module_test_suite_passed: bool,
    file_compiled: bool,
    module_compiled: bool,
    updated_session_state: str
) -> str:
    "Marks a file as validation completed."
    logger.info(
        f"""
        File: {file_path} marked validated.
        All dependencies installed: {all_dependencies_installed_successfully}
        File unit tests passed: {file_unit_tests_passed}.
        Module test suite passed: {module_test_suite_passed}
        File compiled: {file_compiled}
        Module compiled: {module_compiled}
        Updated session state: {updated_session_state}
        """
    )
    return "File marked validated successfully"


class BranchValidationInput(BaseModel):
    all_dependencies_installed: bool = Field(
        description="Whether you were able to install all dependencies successfully and fix any issues encountered."
    )
    all_modules_unit_tests_passed: bool = Field(
        description="True if you were able to run all unit tests for all modules or the entire codebase, and they passed. False otherwise"
    )
    all_modules_code_compiled: bool = Field(
        description="Whether you were able to compile the code for all modules or the entire codebase successfully"
    )
    all_modules_run: bool = Field(
        description="Whether you were able to run all modules or the entire codebase successfully"
    )
    updated_session_state: str = Field(
        description="Detailed state of the current session, listing all commands that were executed and any further validation needed, to help the next agent."
    )
    needs_further_validation: bool = Field(
        description="Whether another agent needs continue validating this project and fixing issues"
    )
    project_guide: str = Field(
        description="Detailed markdown-based project guide according to the provided template."
    )


@tool(args_schema=BranchValidationInput)
def mark_branch_validated(
    all_dependencies_installed: bool,
    all_modules_unit_tests_passed: bool,
    all_modules_code_compiled: bool,
    all_modules_run: bool,
    updated_session_state: str,
    needs_further_validation: bool,
    project_guide: str
) -> str:
    "Marks branch validation complete, and indicates whether further validation is needed. Captures a markdown-based Project Guide detailing out a project and the tasks remaining for production readiness."
    logger.info(
        f'Deps installed: {all_dependencies_installed}, Unit tests passed: {all_modules_unit_tests_passed}, code compiled: {all_modules_code_compiled}, code runs: {all_modules_run}, new state: {updated_session_state}, needs more validation: {needs_further_validation}')
    return "Branch marked validated successfully"
