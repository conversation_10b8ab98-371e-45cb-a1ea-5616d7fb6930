import json
import asyncio
from set_env import EVENT_DATA
from main import generate_reverse_code
from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder
from blitzy_utils.logger import logger

from set_env import EVENT_DATA, NEO4J_SERVER, NEO4J_USERNAME, NEO4J_PASSWORD
import json


if __name__ == "__main__":
    logger.info(f"Generating reverse code for notification data: {EVENT_DATA}")
    event_data = json.loads(EVENT_DATA)
    company_id = event_data.get('company_id', 'default')
    repo_id = event_data.get('repo_id', 'repo_id')
    branch_id = event_data.get('branch_id', 'main')
    head_commit_hash = event_data.get('head_commit_hash', '')
    graph_builder = CodeGraphBuilder(
        uri=NEO4J_SERVER,
        username=NEO4J_USERNAME,
        password=NEO4J_PASSWORD,
        company_id=company_id,
        repo_id=repo_id,
        branch_id=branch_id,
        head_commit_hash=head_commit_hash
    )
    try:
        asyncio.run(generate_reverse_code(event_data=event_data, graph_builder=graph_builder))
    except Exception as e:
        logger.error(f'Failed to generate reverse code: {e}')
        raise
