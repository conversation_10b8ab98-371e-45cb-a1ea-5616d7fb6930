# PowerShell script for building Windows Docker image
param(
    [string]$ImagePath,
    [string]$CredentialsPath
)

Write-Host "Building Windows Docker image with tag: $ImagePath"

# For Windows builds, we'll handle authentication at runtime instead of build time
# This avoids the complexity of Windows credential handling during Docker build

try {
    # Build the Docker image without credentials
    docker build -f Dockerfile.windows -t $ImagePath .

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully built Windows Docker image: $ImagePath"
        Write-Host ""
        Write-Host "NOTE: Private packages from Artifact Registry are not included in the Windows build."
        Write-Host "The application will need to handle missing dependencies or install them at runtime."
    } else {
        Write-Error "Failed to build Windows Docker image"
        exit 1
    }
} catch {
    Write-Error "Error during build: $_"
    exit 1
}