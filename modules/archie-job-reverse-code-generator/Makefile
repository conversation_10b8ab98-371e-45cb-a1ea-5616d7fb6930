PORT ?= 8080
IMAGE_TAG ?= latest
ARTIFACTORY_REGION ?= us-east1
PROJECT_ID_DEV ?= blitzy-os-dev
REPOSITORY ?= gcf-artifacts
IMAGE_NAME ?= archie-job-reverse-code-generator
IMAGE_NAME_WINDOWS ?= archie-job-reverse-code-generator-windows
SERVICE_ACCOUNT_NAME ?= <EMAIL>
ENV ?= dev

# Full image paths
IMAGE_PATH := $(ARTIFACTORY_REGION)-docker.pkg.dev/$(PROJECT_ID_DEV)/$(REPOSITORY)/$(IMAGE_NAME):$(IMAGE_TAG)
IMAGE_PATH_WINDOWS := $(ARTIFACTORY_REGION)-docker.pkg.dev/$(PROJECT_ID_DEV)/$(REPOSITORY)/$(IMAGE_NAME_WINDOWS):$(IMAGE_TAG)

# OS detection for cross-platform support
ifeq ($(OS),Windows_NT)
    SHELL := powershell.exe
    .SHELLFLAGS := -NoProfile -Command
endif

all: build

install-deployment-utils:
	python -m pip install --upgrade pip keyrings.google-artifactregistry-auth toml
	@echo "Installing deployment utils"
	pip install --extra-index-url https://us-east1-python.pkg.dev/${PROJECT_ID_DEV}/python-us-east1/simple deployment-utils

init:
	pip install -r requirements.txt

build:
	@if [ "$(GITHUB_ACTIONS)" = "true" ]; then \
         DOCKER_BUILDKIT=1 docker build \
            --secret id=google_credentials,src=$$GOOGLE_APPLICATION_CREDENTIALS \
            -t $(IMAGE_PATH) .; \
    else \
        DOCKER_BUILDKIT=1 docker build \
            --secret id=google_credentials,src=$$SERVICE_ACCOUNT_KEY_PATH \
            -t $(IMAGE_PATH) .; \
    fi
	@echo "Building docker image with tag $(IMAGE_PATH)"

build-windows:
ifeq ($(OS),Windows_NT)
	@if (Test-Path env:GITHUB_ACTIONS) { docker build --build-arg GOOGLE_APPLICATION_CREDENTIALS_JSON="$(Get-Content $env:GOOGLE_APPLICATION_CREDENTIALS -Raw)" -f Dockerfile.windows -t $(IMAGE_PATH_WINDOWS) . } else { docker build --build-arg GOOGLE_APPLICATION_CREDENTIALS_JSON="$(Get-Content $env:SERVICE_ACCOUNT_KEY_PATH -Raw)" -f Dockerfile.windows -t $(IMAGE_PATH_WINDOWS) . }
	@Write-Host "Building Windows docker image with tag $(IMAGE_PATH_WINDOWS)"
else
	@echo "Error: Windows build must be run on Windows"
	@exit 1
endif

clean:
	docker rmi -f $(docker images -f "dangling=true" -q)

clean-windows:
ifeq ($(OS),Windows_NT)
	@docker images -f "dangling=true" -q | ForEach-Object { docker rmi -f $$_ }
else
	@echo "Error: Windows clean must be run on Windows"
	@exit 1
endif

deploy:
	@if [ -n "$(tag)" ]; then \
		echo "Deploying with tag from command line: $(tag)"; \
		deploy-to-cloud-run --type job --image-tag=$(tag) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	else \
		echo "Deploying with IMAGE_PATH: $(IMAGE_PATH)"; \
		deploy-to-cloud-run --type job --image-tag=$(IMAGE_PATH) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	fi

deploy-windows:
	@if [ -n "$(tag)" ]; then \
		echo "Deploying Windows image with tag from command line: $(tag)"; \
		deploy-to-cloud-run --type job --image-tag=$(tag) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	else \
		echo "Deploying Windows image with IMAGE_PATH: $(IMAGE_PATH_WINDOWS)"; \
		deploy-to-cloud-run --type job --image-tag=$(IMAGE_PATH_WINDOWS) --yaml-file=$(YAML_FILE) --vars-file=env_config/env-$(ENV).yaml --skip-confirmation; \
	fi

.PHONY: init deploy build build-windows clean clean-windows deploy-windows