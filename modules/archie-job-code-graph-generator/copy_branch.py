import os

from blitzy_platform_shared.code_graph.builder import CodeGraphBuilder

NEO4J_SERVER = os.environ["NEO4J_SERVER"]
NEO4J_USERNAME = os.environ["NEO4J_USERNAME"]
NEO4J_PASSWORD = os.environ["NEO4J_PASSWORD"]

company_id = 'default'
repo_id = ''
branch_id = ''
head_commit_hash = ''
graph_builder = CodeGraphBuilder(
    uri=NEO4J_SERVER,
    username=NEO4J_USERNAME,
    password=NEO4J_PASSWORD,
    company_id=company_id,
    repo_id=repo_id,
    branch_id=branch_id,
    head_commit_hash=head_commit_hash
)

print(graph_builder.copy_branch(
    source_branch_id='c13a06f0-810b-48ca-b5a5-a37919e6dafe',
    target_branch_id='',
    company_id=company_id,
    repo_id=repo_id
))
