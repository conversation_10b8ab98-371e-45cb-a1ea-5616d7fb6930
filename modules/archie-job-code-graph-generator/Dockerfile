# Use Ubuntu 24.04 LTS as base image
FROM ubuntu:24.04

# Install Python 3.12, Java 21, git, and other dependencies
RUN apt-get update && apt-get install -y \
    python3.12 \
    python3.12-venv \
    python3-pip \
    python-is-python3 \
    git \
    curl \
    xz-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Install keyring for artifact registry auth
RUN pip install --break-system-packages keyrings.google-artifactregistry-auth

# Copy requirements.txt and install Python dependencies
COPY requirements.txt .

RUN --mount=type=secret,id=google_credentials \
    export GOOGLE_APPLICATION_CREDENTIALS=/run/secrets/google_credentials && \
    pip install --break-system-packages -r requirements.txt

# Copy your application code
COPY . .

# Command to run your application
CMD ["python", "main.py"]