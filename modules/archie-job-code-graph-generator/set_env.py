import json
import os

from blitzy_utils.enums import BackpropCommand

EVENT_DATA = json.dumps({"repo_name": "Express.Parent.AI", "repo_id": "*********", "branch_id": "5e5a132b-8229-4672-9d8a-7f23a47b9fe5", "branch_name": "main", "company_id": "eb66ed39-3573-4d87-98cd-61392c61f2c7", "user_id": "03037d08-442f-43a5-9b50-5288e96a6604", "team_id": "4c7df2d0-ebd4-488c-94e8-ebb26def1c88",
                         "job_id": "f47c0e01-f736-4939-b677-0fe3b648671f", "project_id": "394c75d6-aa48-4d71-b3ba-34540e7165cf", "head_commit_hash": "7d954607d2c03b49a89d377bffac4686426dffb6", "prev_head_commit_hash": "", "propagate": False, "batch_index": 54, "total_batches": 63, "tech_spec_id": "89b41df1-565b-45f9-a81b-d8ac86d1c189", "resume": False})
os.environ["EVENT_DATA"] = EVENT_DATA

os.environ["GRAPH_CODE_TOPIC"] = "generate-code-graph"
os.environ["GENERATE_REVERSE_DOCUMENT_TOPIC"] = "generate-reverse-document"
os.environ["PROJECT_ID"] = 'blitzy-os-dev'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-os-internal'
os.environ["PRIVATE_BLOB_NAME"] = 'private-src'
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'
os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
os.environ["VOYAGE_API_KEY"] = "pa-5WI8Vwyw7YUpoWeX4ckHbDd7dN4w74S61zXHQKD0wTE"
os.environ["GOOGLE_API_KEY"] = "AIzaSyAA_WvkItpwfiL3D8czRWsT5dbA2iXmF_I"

NEO4J_SERVER = "neo4j://34.66.114.166:7687"
NEO4J_USERNAME = "neo4j"
NEO4J_PASSWORD = "bavjoz-pamciB-6vifce"
os.environ["NEO4J_SERVER"] = NEO4J_SERVER
os.environ["NEO4J_USERNAME"] = NEO4J_USERNAME
os.environ["NEO4J_PASSWORD"] = NEO4J_PASSWORD

DEV_GITHUB_SECRET_SERVER = "https://archie-secret-manager-464705070478.us-central1.run.app"
PROD_GITHUB_SECRET_SERVER = "https://archie-secret-manager-648803317587.us-east1.run.app"
os.environ["GITHUB_SECRET_SERVER"] = DEV_GITHUB_SECRET_SERVER

os.environ["LANGCHAIN_TRACING_V2"] = ""
os.environ["LANGCHAIN_ENDPOINT"] = ""
os.environ["LANGCHAIN_API_KEY"] = ""
os.environ["LANGCHAIN_PROJECT"] = ""
