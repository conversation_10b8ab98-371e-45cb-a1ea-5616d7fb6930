#!/usr/bin/env python3
import argparse
import json
import os
import subprocess
import sys
import tempfile
import yaml
from typing import Dict, Any
from pathlib import Path

try:
    from google.cloud import secretmanager
    SECRET_MANAGER_AVAILABLE = True
except ImportError:
    SECRET_MANAGER_AVAILABLE = False


def resolve_file_path(file_path: str) -> str:
    """Resolve file path relative to current working directory."""
    # Convert to Path object
    path = Path(file_path)

    if not path.is_absolute():
        path = Path.cwd() / path

    return str(path)


def fetch_secrets_from_gsm(project_id: str, secret_name: str) -> Dict[str, Any]:
    """
    Fetch and merge secrets from both common-secret and app-specific secrets.
    Common secrets are loaded first, then app-specific secrets override/extend them.
    secret_name parameter is used as the app-specific secret name.
    """
    if not SECRET_MANAGER_AVAILABLE:
        print("Warning: Google Secret Manager not available")
        return {}
    
    merged_secrets = {}
    client = secretmanager.SecretManagerServiceClient()
    
    # First, fetch base secrets (common environment variables)
    print("🔧 Fetching common secrets...")
    try:
        base_name = f"projects/{project_id}/secrets/archie-common-secret/versions/latest"
        base_response = client.access_secret_version(request={"name": base_name})
        base_secrets = yaml.safe_load(base_response.payload.data.decode("UTF-8"))
        
        if isinstance(base_secrets, dict):
            merged_secrets.update(base_secrets)
            print(f"✅ Fetched {len(base_secrets)} common secrets from GSM")
        else:
            print("⚠️  Common secret exists but contains no valid data")
    except Exception as e:
        print(f"❌ Error fetching common secret: {str(e)}")
        return {}
    
    # Then, fetch app-specific secrets (these will override base secrets if there are conflicts)
    print(f"🔧 Fetching app-specific secrets for '{secret_name}'...")
    try:
        app_name_secret = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
        app_response = client.access_secret_version(request={"name": app_name_secret})
        app_secrets = yaml.safe_load(app_response.payload.data.decode("UTF-8"))
        
        if isinstance(app_secrets, dict):
            # Check for conflicts and report them
            conflicts = set(merged_secrets.keys()) & set(app_secrets.keys())
            if conflicts:
                print(f"🔄 Overriding common secrets with app-specific values for: {', '.join(sorted(conflicts))}")
            
            merged_secrets.update(app_secrets)
            print(f"✅ Fetched {len(app_secrets)} app-specific secrets from GSM")
        else:
            print(f"⚠️  App-specific secret '{secret_name}' exists but contains no valid data")
    except Exception as e:
        print(f"❌ Error fetching app-specific secret '{secret_name}': {str(e)}")
        # Don't return empty dict here - we still have base secrets
    
    print(f"📊 Total merged secrets: {len(merged_secrets)} variables")
    return merged_secrets


def load_variables(variables_file: str) -> Dict[str, Any]:
    """Load variables from either JSON or YAML file."""
    # Resolve the full path
    if not variables_file:
        return {}
        
    full_path = resolve_file_path(variables_file)

    if not os.path.exists(full_path):
        print(f"Error: Variables file not found: {full_path}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

    try:
        with open(full_path, 'r') as f:
            if variables_file.endswith('.json'):
                return json.load(f)
            elif variables_file.endswith(('.yml', '.yaml')):
                return yaml.safe_load(f)
            else:
                print("Error: Variables file must be either JSON or YAML format")
                sys.exit(1)
    except Exception as e:
        print(f"Error loading variables file {full_path}: {e}")
        sys.exit(1)


def render_template(template: str, variables: Dict[str, Any], inject_env: bool = False) -> str:
    """Render the template with the provided variables."""
    result = str(template)
    
    # Replace placeholders
    for key, value in variables.items():
        placeholder = f"{{{{{key}}}}}"
        if placeholder in result:
            result = result.replace(placeholder, str(value))
    
    # Inject env vars in GSM mode
    if inject_env:
        skip = {'ENVIRONMENT', 'PORT', 'DEPLOYMENT_REGION', 'REGION', 'IMAGE_TAG', 'ARTIFACTORY_REGION', 'REPOSITORY', 'IMAGE_NAME'}
        
        env_vars = {k: str(v) for k, v in variables.items() if k not in skip}
        
        if env_vars:
            try:
                # Parse YAML
                doc = yaml.safe_load(result)
                
                # Navigate to containers and add env vars
                containers = doc.get('spec', {}).get('template', {}).get('spec', {}).get('containers', [])
                if not containers:
                    containers = doc.get('spec', {}).get('template', {}).get('spec', {}).get('taskTemplate', {}).get('spec', {}).get('template', {}).get('spec', {}).get('containers', [])
                
                if containers:
                    container = containers[0]
                    if 'env' not in container:
                        container['env'] = []

                    # Add new env vars to existing list
                    for name, value in env_vars.items():
                        container['env'].append({'name': name, 'value': value})
                # Convert back to YAML
                result = yaml.dump(doc, default_flow_style=False, sort_keys=False)
                
            except yaml.YAMLError as e:
                print(f"Warning: Could not parse YAML for env injection: {e}")
                print("Falling back to original template")
    
    return result


def prompt_confirmation(resource_type: str = 'service') -> bool:
    """Prompt user for confirmation."""
    while True:
        response = input(f"\nDo you want to proceed with the {resource_type} deployment? [y/N]: ").lower().strip()
        if response in ['y', 'yes']:
            return True
        elif response in ['', 'n', 'no']:
            return False
        print("Please respond with 'y' or 'n'")


def main() -> None:
    """Main entry point for the deploy-to-cloud-run command."""
    # Print current working directory for debugging.
    print(f"Current working directory: {os.getcwd()}")

    # Setup command line argument parser
    parser = argparse.ArgumentParser(description="Deploy to Cloud Run")
    parser.add_argument("--type", choices=['service', 'job'], default='service',
                        help="Type of Cloud Run resource to deploy (default: service)")
    parser.add_argument("--image-tag", help="Image tag to deploy (overrides value in vars file)")
    parser.add_argument("--yaml-file", help="Path to service YAML file", required=True)
    parser.add_argument("--vars-file", help="Path to variables file (JSON or YAML)")
    parser.add_argument("--region", help="GCP region for deployment (overrides value in vars file)")
    parser.add_argument("--dry-run", action="store_true", help="Show rendered YAML without deploying")
    parser.add_argument("--skip-confirmation", action="store_true",
                        help="Skip confirmation prompt and proceed with deployment")
    parser.add_argument("--show-deployment-configuration", action="store_true",
                        help="Show deployment configuration")
    parser.add_argument("--show-rendered", action="store_true", help="Show rendered YAML template")
    parser.add_argument("--project-id", help="GCP Project ID (GSM mode)")
    parser.add_argument("--app-name", help="Application name (GSM mode)")
    args = parser.parse_args()

    # Determine mode and load variables
    if args.vars_file:
        print("📁 Legacy mode: Using vars file only")
        variables = load_variables(args.vars_file)
        inject_env = False
    elif args.project_id and args.app_name:
        print("🔐 GSM mode: Using Google Secret Manager only")
        variables = {
            'PROJECT_ID': args.project_id,
            'SERVICE_NAME': args.app_name
        }
        
        # Fetch secrets from GSM (base + app-specific)
        merged_secrets = fetch_secrets_from_gsm(args.project_id, args.app_name)
        if merged_secrets:
            variables.update(merged_secrets)
            print(f"📊 Final variable count: {len(variables)}")
        else:
            print("⚠️  No secrets were fetched successfully")
        
        inject_env = True
    else:
        print("Error: Either --vars-file OR (--project-id + --app-name ) required")
        sys.exit(1)

    # Set required variables
    if args.image_tag:
        variables["IMAGE_TAG"] = args.image_tag
    elif "IMAGE_TAG" not in variables:
        print("Error: IMAGE_TAG must be provided either via --image-tag argument or in the variables file")
        sys.exit(1)

    if args.region:
        variables["REGION"] = args.region
    elif "DEPLOYMENT_REGION" not in variables:
        print("Error: REGION must be provided either via --region argument or in the variables file with environment variable name `DEPLOYMENT_REGION`")
        sys.exit(1)

    yaml_file = resolve_file_path(args.yaml_file)
    if not os.path.exists(yaml_file):
        print(f"Error: YAML file not found: {yaml_file}")
        print(f"Current working directory: {os.getcwd()}")
        sys.exit(1)

    try:
        with open(yaml_file, "r") as file:
            content = file.read()

        updated_content = render_template(content, variables, inject_env=inject_env)

        if args.show_deployment_configuration:
            print("\nDeployment Configuration:")
            for key, value in variables.items():
                source = "command line" if (key == "IMAGE_TAG" and args.image_tag) or (
                        key == "REGION" and args.region) else "vars file"
                print(f"- {key}: {value} (source: {source})")

        if args.show_rendered or args.dry_run:
            print("\nRendered YAML:")
            print("---")
            print(updated_content)
            print("---")

        if args.dry_run:
            print("\nDry run completed. No deployment performed.")
            return

        # Prompt for confirmation unless --skip-confirmation is used
        if not args.skip_confirmation:
            if not prompt_confirmation(args.type):
                print("\nDeployment cancelled.")
                return
            print(f"\nProceeding with {args.type} deployment...")
        else:
            print(f"\nSkipping confirmation and proceeding with {args.type} deployment...")

        # Write to temporary file.
        with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as temp_file:
            temp_file.write(updated_content)
            temp_yaml_path = temp_file.name

        print("\nStarting deployment...")
        if args.type == 'service':
            cmd = f"gcloud run services replace {temp_yaml_path} --region {variables['DEPLOYMENT_REGION']}"
        else:  # job
            cmd = f"gcloud run jobs replace {temp_yaml_path} --region {variables['DEPLOYMENT_REGION']}"

        subprocess.run(cmd, shell=True, check=True)

        print("\nDeployment completed successfully")

    except subprocess.CalledProcessError as e:
        print(f"Error during deployment: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        if "temp_yaml_path" in locals():
            try:
                os.unlink(temp_yaml_path)
            except Exception as e:
                print(f"Warning: Could not delete temporary file {temp_yaml_path}: {e}")


if __name__ == "__main__":
    main()