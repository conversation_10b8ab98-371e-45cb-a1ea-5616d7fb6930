from src.process_event import process_payload

payload = {'projectId': '3c705ef3-5c19-440e-a51e-ae91080117f6', 'jobId': 'b228d1f0-1841-4d98-8648-1068abd7224a',
           'tech_spec_id': '6e78f1cb-30e7-4dcf-90df-e42b49ea808a', 'org_name': '', 'repo_id': '1011128959',
           'branch_name': 'master', 'branch_id': '3e9a6efc-c0f1-4cd7-94a9-d868ceab8c98',
           'head_commit_hash': 'b6aa5adb888c9c89b3c5405bd641d7f88797159c',
           'prev_head_commit_hash': '236905bffa93a1d997ffa285b3b092a14f2edc73', 'phase': 'CODE_DOWNLOAD',
           'status': 'DONE', 'user_id': '6f72d8e4-4a82-4f38-88a2-ac87fdcc0e9e', 'team_id': 'default',
           'company_id': 'default', 'graph_needs_update': False, 'copy_tech_spec': False,
           'repo_name': 'BlitzyClientPortalParent', 'metadata': {'propagate': True}}

if __name__ == '__main__':
    process_payload(payload)
