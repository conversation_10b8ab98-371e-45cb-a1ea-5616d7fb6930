from src.error.base_error import BlitzyError


class UserNotFoundError(BlitzyError):
    """Base class for all user-related errors"""

    def __init__(self, message: str = None, status_code: int = 400):
        super().__init__(message=message, status_code=status_code)


class UserDatabaseError(BlitzyError):
    """Raised when there's a database error during user creation"""

    def __init__(self, message: str = "Database error during user creation"):
        super().__init__(message, status_code=500)


class UserUpdateError(BlitzyError):
    """Failed to update user information."""

    def __init__(self, message: str = None, status_code: int = 500):
        super().__init__(message=message, status_code=status_code)


class SecretNotFoundError(BlitzyError):
    """Failed to update user information."""

    def __init__(self, message: str = None, status_code: int = 404):
        super().__init__(message=message, status_code=status_code)


class InstallationIDNotFoundError(BlitzyError):
    """Raised when there's no installation id found in the payload"""

    def __init__(self, message: str = "Installation id not found in the payload"):
        super().__init__(message, status_code=400)


class InvalidInstallationActionError(BlitzyError):
    """Raised when there's invalid installation action"""

    def __init__(self, message: str = "Invalid installation action"):
        super().__init__(message, status_code=400)


class InvalidGithubInstallationIDError(BlitzyError):
    """Raised when there's invalid installation id"""

    def __init__(self, message: str = "Invalid installation id"):
        super().__init__(message, status_code=400)
