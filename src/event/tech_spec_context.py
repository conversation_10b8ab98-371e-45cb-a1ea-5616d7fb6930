from typing import Any, Dict

from blitzy_utils.common import generate_technical_spec_document_path
from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from common_models.db_client import get_db_session
from common_models.models import (Project, ProjectRun, ProjectRunStatus,
                                  ReportStatus, Status, TechnicalSpec)

from flask_utils.base_error import BaseError
from src.consts import GCS_BUCKET_NAME, check_service_error
from src.event.code_graph_event import attempt_branch_lock_release
from src.service.project_run_service import update_project_run_by_tech_spec_id
from src.service.tech_spec_document_context_service import \
    get_tech_spec_document_context
from src.service.tech_spec_service import (get_tech_spec_by_id,
                                           update_tech_spec_by_id)


def process_code_download_event(project_info: Project, job_id: str, status: Status, metadata: Dict[str, Any],
                                payload: Dict[str, Any]):
    if status != Status.DONE:
        logger.info("Status is not done. Skipping tech spec context extraction.")
        return

    branch_id = payload["branch_id"]
    head_commit_hash = payload["head_commit_hash"]
    tech_spec_id = payload["tech_spec_id"]
    copy_tech_spec = payload.get("copy_tech_spec", False)

    logger.info(f"Extracting Tech spec context for project {project_info.id}")
    graph_needs_update = payload["graph_needs_update"]

    if graph_needs_update:
        logger.info(f"Graph needs update for project {project_info.id}. This means new tech spec will be generated.")
        logger.info("Skipping. Tech spec copy.")
        return

    tech_spec_context = get_tech_spec_document_context(branch_id, head_commit_hash)
    if not tech_spec_context:
        logger.info(f"Tech spec context not found for project {project_info.id}. Skipping.")
        return
    logger.info(f"Found Tech spec context for project {project_info.id}")

    source_tech_spec_payload = tech_spec_context.context_metadata
    source_path = generate_technical_spec_document_path(source_tech_spec_payload, GCS_BUCKET_NAME)
    source_company = source_tech_spec_payload.get("company_id", "")

    tech_spec = get_tech_spec_by_id(tech_spec_id)
    if not tech_spec:
        logger.warning(f"Tech spec {tech_spec_id} does not exist. Event won't be processed.")
        return

    if not copy_tech_spec:
        destination_tech_spec_payload = tech_spec.job_metadata
        destination_path = generate_technical_spec_document_path(destination_tech_spec_payload, GCS_BUCKET_NAME)
        destination_company = destination_tech_spec_payload.get("company_id", "")
        logger.info(f"Copying tech spec from {source_path} to {destination_path}")

        try:
            response = copy_tech_spec_using_admin_service(source_company, source_path, destination_company,
                                                          destination_path)
            logger.info(f"API Response {response}")
            logger.info(f"Successfully copied tech spec from {source_path} to {destination_path}")
        except Exception as e:
            logger.error(f"Error during tech spec copy for project {project_info.id}: {str(e)}")
            logger.error(f"Failed to copy from {source_path} to {destination_path}")
            return

        source_pdf_path = source_path.replace(".md", ".pdf")
        destination_pdf_path = destination_path.replace(".md", ".pdf")

        # We already have a failsafe which let's us generate PDF if it's not generated during this phase.
        # On clicking download button on UI if PDF doesn't exists backend sends notification to generate new PDF.
        try:
            # Try to copy PDF file if it exists, but don't fail if it doesn't.
            copy_tech_spec_using_admin_service(source_company, source_pdf_path, destination_company,
                                                            destination_pdf_path)
        except BaseError as e:
            logger.warning(f"Error during tech spec PDF copy for project {project_info.id}: {str(e)}")
            logger.warning(f"Failed to copy from {source_pdf_path} to {destination_pdf_path}, skipping...")

    else:
        message = (f"This is error condition. Copying tech spec is set to True but no tech spec is found. "
                   f"Mostly this happens when platform didn't find anything to create tech-spec.")
        logger.error(message)
        return

    with get_db_session() as session:
        tech_spec_payload = {
            TechnicalSpec.status: status,
            TechnicalSpec.pdf_report_status: ReportStatus.READY,
        }

        update_tech_spec_by_id(tech_spec_id, tech_spec_payload, session=session)

        project_run_status = ProjectRunStatus[payload["status"]]
        job_payload = {
            ProjectRun.status: project_run_status,
            ProjectRun.job_metadata: metadata,
        }

        update_project_run_by_tech_spec_id(tech_spec.id, job_payload, session=session)
        attempt_branch_lock_release(branch_id, session)
        session.commit()
        logger.info(f"Tech spec and job metadata updated for the project {project_info.id}")


def copy_tech_spec_using_admin_service(source_company, source_path, destination_company, destination_path):
    with ServiceClient() as client:
        payload = {
            "sourceCompany": source_company,
            "sourcePath": source_path,
            "destinationCompany": destination_company,
            "destinationPath": destination_path,
        }

        endpoint = f"/v1/storage/copy"
        response = client.post("admin", endpoint, json=payload, timeout=180)
        check_service_error(response)
        return response.json()
