import {useCallback, useEffect, useState} from 'react';
import {useNavigate, useSearchParams} from 'react-router-dom';
import {useToast} from '@/hooks/use-toast.ts';
import iconGithub from '../panel/workspace/icons/github.svg';
import iconAzureDevOps from '../panel/workspace/icons/azure-devops.svg';
import {saveGithubInstallationInfo} from '@/lib/backend.ts';
import {logGAEvent} from '@/lib/utils';
import {useGitInstallationsContext} from '@/context/git-installations-context';

interface Props {
    handleClose: () => void;
}

function getGitPlatform(): 'github' | 'azure-devops' {
    return 'github';
}

export function GithubApprovalPane({handleClose}: Props) {
    const navigate = useNavigate();
    const {toast} = useToast();
    const [searchParams, setSearchParams] = useSearchParams();
    const {githubStatus, checkGitInstallations} = useGitInstallationsContext();

    // Extract OAuth parameters
    const installationID = searchParams.get('installation_id') || '';
    const setupAction = searchParams.get('setup_action');
    const code = searchParams.get('code');

    // Compute hasOAuthParams immediately to prevent race conditions
    const hasOAuthParams = !!(code && setupAction);

    // Clean URL immediately after extracting params to prevent reuse on refresh
    useEffect(() => {
        if (hasOAuthParams) {
            // Clear the search params to prevent reuse on refresh
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.delete('code');
            newSearchParams.delete('installation_id');
            newSearchParams.delete('setup_action');
            setSearchParams(newSearchParams, {replace: true});
        }
    }, []); // Run only once on mount

    // State management
    const [installationStatus, setInstallationStatus] = useState<
        'SUCCESS' | 'FAILED' | null
    >(null);
    const [isProcessingInstall, setIsProcessingInstall] = useState(false);
    const [oauthProcessingStarted, setOauthProcessingStarted] = useState(false);

    const gitPlatform: 'github' | 'azure-devops' = getGitPlatform();

    // Handle redirect after successful installation
    const handleSuccessfulInstallation = useCallback(() => {
        setInstallationStatus('SUCCESS');

        // Small delay to show success message before redirect
        setTimeout(() => {
            handleClose();

            const projectID = localStorage.getItem(
                'github-connect-redirect-to-project-id',
            );
            const redirectToIntegrationTab = localStorage.getItem(
                'github-connect-redirect-to-integration-tab',
            );

            if (projectID) {
                localStorage.removeItem(
                    'github-connect-redirect-to-project-id',
                );
                navigate(`/workspace/project/${projectID}/status`, {
                    replace: true,
                });
            } else if (redirectToIntegrationTab === 'true') {
                localStorage.removeItem(
                    'github-connect-redirect-to-integration-tab',
                );
                navigate('/workspace/settings/integrations', {replace: true});
            } else {
                navigate('/workspace/projects', {replace: true});
            }
        }, 1000);
    }, [handleClose, navigate]);

    // Process OAuth callback
    const processOAuthCallback = useCallback(async () => {
        // Mark that OAuth processing has started
        setOauthProcessingStarted(true);

        // Validate parameters
        if (
            !code ||
            !setupAction ||
            (setupAction === 'install' && !installationID)
        ) {
            toast({
                variant: 'destructive',
                duration: 10000,
                description: `Invalid search parameters: ${searchParams.toString()}`,
            });
            setInstallationStatus('FAILED');
            return;
        }

        try {
            setIsProcessingInstall(true);

            // Save the installation info to backend
            await saveGithubInstallationInfo(code, installationID, setupAction);

            if (setupAction === 'install') {
                // Wait for GitHub's systems to sync
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Refresh the installation status from context
                await checkGitInstallations();

                // Status will be checked in the useEffect below
                // Don't check githubStatus here due to stale closure issues
            } else if (setupAction === 'request') {
                // For 'request' action, we don't need to check status
                setIsProcessingInstall(false);
                handleSuccessfulInstallation();
            }

            // Log successful installation
            logGAEvent('github_installation_success', {
                installation_id: installationID,
                setup_action: setupAction,
            });
        } catch (error) {
            console.error('Error processing OAuth callback:', error);
            setIsProcessingInstall(false);
            setInstallationStatus('FAILED');
            toast({
                variant: 'destructive',
                duration: 10000,
                title: 'There was a problem saving Github installation information.',
            });
        }
    }, [
        code,
        setupAction,
        installationID,
        toast,
        searchParams,
        checkGitInstallations,
        handleSuccessfulInstallation,
    ]);

    // Watch for GitHub status changes after processing installation
    useEffect(() => {
        if (
            isProcessingInstall &&
            setupAction === 'install' &&
            githubStatus === 'ACTIVE'
        ) {
            setIsProcessingInstall(false);
            handleSuccessfulInstallation();
        }
    }, [
        isProcessingInstall,
        githubStatus,
        setupAction,
        handleSuccessfulInstallation,
    ]);

    // Timeout failsafe for processing
    useEffect(() => {
        if (isProcessingInstall) {
            const timeout = setTimeout(() => {
                if (isProcessingInstall) {
                    console.warn('Installation processing timed out');
                    setIsProcessingInstall(false);
                    setInstallationStatus('FAILED');
                    toast({
                        variant: 'destructive',
                        duration: 10000,
                        title: 'GitHub installation timed out',
                        description:
                            'Please try again or check the integrations page.',
                    });
                }
            }, 15000); // 15 second timeout

            return () => clearTimeout(timeout);
        }
    }, [isProcessingInstall, toast]);

    // Process OAuth callback when component mounts with OAuth parameters
    useEffect(() => {
        if (
            hasOAuthParams &&
            !isProcessingInstall &&
            installationStatus === null
        ) {
            processOAuthCallback();
        }
    }, [
        hasOAuthParams,
        isProcessingInstall,
        installationStatus,
        processOAuthCallback,
    ]);

    // Fallback: Check initial status only if no OAuth parameters (runs only once on mount)
    useEffect(() => {
        // Only run fallback check if we don't have OAuth params AND OAuth processing hasn't started
        if (!hasOAuthParams && !oauthProcessingStarted) {
            const checkInitialStatus = async () => {
                try {
                    await checkGitInstallations();

                    // Wait for context to update
                    setTimeout(() => {
                        // Only set status if OAuth processing still hasn't started
                        if (
                            !hasOAuthParams &&
                            !oauthProcessingStarted &&
                            installationStatus === null
                        ) {
                            if (githubStatus === 'ACTIVE') {
                                setInstallationStatus('SUCCESS');
                                setTimeout(() => {
                                    handleClose();
                                    navigate('/workspace/projects', {
                                        replace: true,
                                    });
                                }, 1000);
                            } else {
                                setInstallationStatus('FAILED');
                            }
                        }
                    }, 500);
                } catch (error) {
                    console.error('Error checking installation status:', error);
                    // Only set failed if no OAuth processing happened
                    if (
                        !hasOAuthParams &&
                        !oauthProcessingStarted &&
                        installationStatus === null
                    ) {
                        setInstallationStatus('FAILED');
                    }
                }
            };

            checkInitialStatus();
        }
    }, []); // Empty dependency array - runs only once on mount

    // Handle retry
    const handleTryAgain = useCallback(() => {
        setInstallationStatus(null);
        setIsProcessingInstall(false);
        setOauthProcessingStarted(false);

        if (hasOAuthParams) {
            processOAuthCallback();
        } else {
            // Trigger fallback check
            checkGitInstallations();
        }
    }, [hasOAuthParams, processOAuthCallback, checkGitInstallations]);

    // Render loading spinner for processing state
    const renderLoadingSpinner = () => (
        <div className="w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin mb-4"></div>
    );

    return (
        <div className="bg-black/20 fixed inset-0 flex flex-col items-center justify-center z-50">
            <div className="bg-white min-h-[100dvh] sm:min-h-[260px] p-[24px] w-full sm:w-[608px] flex flex-col justify-center items-center rounded-none sm:rounded-[24px] relative shadow-[0px_20px_24px_-4px_rgba(16,24,40,0.10),0px_8px_8px_-4px_rgba(16,24,40,0.04)]">
                <div className="flex flex-col items-center justify-center">
                    {/* Loading spinner for processing state */}
                    {(installationStatus === null || isProcessingInstall) &&
                        renderLoadingSpinner()}

                    <img
                        src={
                            gitPlatform === 'github'
                                ? iconGithub
                                : iconAzureDevOps
                        }
                        alt="Git platform"
                        className="mb-[24px] w-[64px] h-[64px]"
                    />

                    <h2 className="mb-[4px] text-[20px] sm:text-[24px] text-center font-semibold leading-[130%] text-black">
                        {installationStatus === null
                            ? `Connecting ${gitPlatform === 'github' ? 'GitHub' : 'Azure DevOps'}...`
                            : installationStatus === 'SUCCESS'
                              ? `${gitPlatform === 'github' ? 'GitHub' : 'Azure DevOps'} connected successfully!`
                              : `${gitPlatform === 'github' ? 'GitHub' : 'Azure DevOps'} connection failed`}
                    </h2>

                    <p className="text-[14px] text-center leading-[130%] text-[#999]">
                        {installationStatus === null
                            ? 'This will only take a few seconds - You will be redirected automatically'
                            : installationStatus === 'SUCCESS'
                              ? 'Redirecting...'
                              : 'Unable to connect - Please check your credentials and try again'}
                    </p>

                    {installationStatus === 'FAILED' && (
                        <div className="mt-[24px] flex flex-row gap-[12px]">
                            <button
                                className="secondary-button w-max"
                                onClick={handleClose}>
                                Cancel
                            </button>
                            <button
                                className="primary-button w-max"
                                onClick={handleTryAgain}>
                                Try again
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
