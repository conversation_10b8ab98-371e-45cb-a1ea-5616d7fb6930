import {ProjectState, ProjectType} from '@/lib/entity-types';
import {useProjectContext} from '@/context/project-context';
import {useCommitStatus} from '../hooks/commit-status-hook';
import './detail-human-tasks.css';
import {projectGuideUrl} from '@/lib/utils';

interface Props {
    projectType?: ProjectType;
    jobStatus?: ProjectState;
}

function shouldHide(jobStatus: ProjectState | undefined) {
    if (
        !location.pathname.includes('tech-spec') ||
        ['QUEUED', 'IN_PROGRESS'].includes(
            jobStatus?.technicalSpec?.status ?? '',
        )
    )
        return true;
    const isCodeGenerated = jobStatus?.codeGeneration?.status === 'DONE';
    const isTechSpecCompleted = ['DONE', 'SUBMITTED'].includes(
        jobStatus?.technicalSpec?.status ?? '',
    );
    const isCodeMerged = jobStatus?.codeGeneration?.commitStatus === 'MERGED';

    return !(isTechSpecCompleted && isCodeGenerated && isCodeMerged);
}

export function DetailHumanTasks({jobStatus}: Props) {
    const {project} = useProjectContext();
    const {id: codeGenId} = jobStatus?.codeGeneration ?? {};
    const {status} = useCommitStatus(project?.id ?? '', codeGenId ?? '');
    if (shouldHide(jobStatus)) return null;

    return (
        <div className="flex items-center gap-[12px]">
            <div className="w-[48px] h-[48px] min-w-[48px] min-h-[48px] flex items-center justify-center rounded-full ai-build-circle-gradient">
                <span className="w-[42px] h-[42px] text-[16px] font-semibold bg-white flex items-center justify-center rounded-full">
                    80%
                </span>
            </div>
            <div>
                <h3 className="text-[16px] font-semibold">Blitzy AI-built</h3>
                <a
                    className="text-[16px]"
                    href={projectGuideUrl(
                        status?.blitzyBranchUrl,
                        project?.gitSink ?? project?.gitSource,
                    )}
                    target="_blank">
                    View pending human tasks ↗
                </a>
            </div>
        </div>
    );
}
