from datetime import datetime, timezone, timedelta
from random import randint
import uuid

from src.api.models import (
    SubscriptionUsageModel,
    ValidationResultModel,
    Benefits,
    EntityType,
    SubscriptionType,
    SubscriptionBenefitsModel, Subscription, SubscriptionStatus
)

def generate_mock_usage_data(entity_id: str, entity_type: EntityType) -> SubscriptionUsageModel:
    """Generate mock subscription usage data"""
    """Generate mock aggregate metering data"""
    # Base multipliers for different entity types
    multipliers = {
        "USER": 1,
        "TEAM": 5,
        "COMPANY": 20,
        "PROJECT": 3
    }

    multiplier = multipliers.get(entity_type.value, 1)

    # Generate mock values based on entity type
    files_onboarded = int(50 * multiplier)
    lines_onboarded = int(10000 * multiplier)
    files_touched = int(15 * multiplier)
    lines_added = int(500 * multiplier)
    lines_edited = int(300 * multiplier)
    lines_removed = int(100 * multiplier)
    lines_generated = lines_added + lines_edited + lines_removed

    # Calculate hours saved (roughly 1 hour per 100 lines generated)
    hours_saved = round(lines_generated / 100, 1)

    return SubscriptionUsageModel(
        entity_id=entity_id,
        entity_type=entity_type,
        subscription_id=f"sub_{entity_id[:8]}",  # Mock subscription ID
        files_onboarded=files_onboarded,
        lines_onboarded=lines_onboarded,
        files_touched=files_touched,
        lines_added=lines_added,
        lines_edited=lines_edited,
        lines_removed=lines_removed,
        lines_generated=lines_generated,
        hours_saved=hours_saved
    )


def generate_mock_subscription_status() -> Subscription:
    """Generate mock subscription status"""

    current_time = datetime.now(timezone.utc)

    return Subscription(
        stripe_customer_id=f"cus_{uuid.uuid4().hex[:14]}",
        stripe_subscription_id=f"sub_{uuid.uuid4().hex[:14]}",
        plan_type=SubscriptionType.PRO,  # Using the enum
        start_date=current_time - timedelta(days=30),
        end_date=current_time + timedelta(days=335),
        trial_start_date=current_time - timedelta(days=37),
        trial_end_date=current_time - timedelta(days=30),
        current_period_start_date=current_time - timedelta(days=5),
        current_period_end_date=current_time + timedelta(days=25),
        status=SubscriptionStatus.ACTIVE,  # Using the enum
        remaining_runs=randint(25, 100),
        is_trialing=False,
        has_trial_received=True
    )


def generate_mock_validation_result(entity_type: EntityType, entity_id: str,
                                    allowed: bool = True) -> ValidationResultModel:
    """Generate mock validation result"""
    return ValidationResultModel(
        allowed=allowed,
        entity_type=entity_type,
        entity_id=entity_id,
        reason="Usage within limits" if allowed else "Usage limit exceeded",
        current_usage=23 if allowed else 105,
        limit=100
    )


def generate_mock_benefits() -> Benefits:
    """Generate mock benefits data"""
    return Benefits(
        line_limit=100000,
        onboarding_limit=50,
        chat_limit=1000,
        cost_per_line=0.05
    )


def generate_mock_benefits_response(company_id: str) -> SubscriptionBenefitsModel:
    """Generate mock benefits response"""
    return SubscriptionBenefitsModel(
        company_id=company_id,
        plan_type=SubscriptionType.ENTERPRISE,
        benefits=generate_mock_benefits(),
        updated_at=datetime.now(),
        updated_by=f"admin_{uuid.uuid4().hex[:6]}"
    )