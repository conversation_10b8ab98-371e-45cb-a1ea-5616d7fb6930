"""
Azure DevOps utility functions for archie-github-handler.

This module contains Azure DevOps-specific utility functions for handling
organizations, projects, repositories, and branches.
"""


import requests
from blitzy_utils.common import blitzy_exponential_retry_service
from blitzy_utils.logger import logger
from common_models.models import VersionControlSystem

from src.azure.azure_app_service import AzureAppService
from src.service.azure_service import fetch_azure_secret_for_user


@blitzy_exponential_retry_service()
def get_azure_projects_by_user_and_org(user_info, org_id):
    """
    Get Azure DevOps projects for a user and organization.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID

    Returns:
        List of project dictionaries with id, name, description, and other details
    """
    try:
        logger.info(f"Getting Azure projects for user {user_info.id} in org {org_id}")

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        # Resolve organization ID to organization name
        azure_service = AzureAppService()
        org_name = azure_service.resolve_organization_id(org_id, access_token)

        logger.info(f"Resolved org_id {org_id} to org_name {org_name}")

        # Call Azure DevOps API to get projects using organization name
        url = f"https://dev.azure.com/{org_name}/_apis/projects?api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        projects = []

        for project in data.get("value", []):
            project_data = {
                "id": project.get("id"),
                "name": project.get("name"),
                "description": project.get("description", ""),
                "url": project.get("url"),
                "state": project.get("state"),
                "visibility": project.get("visibility"),
            }
            projects.append(project_data)

        logger.info(
            f"Found {len(projects)} Azure projects for user {user_info.id} in org {org_name} (ID: {org_id})"
        )
        return projects

    except Exception as e:
        logger.error(
            f"Error retrieving Azure projects for user {user_info.id} in org {org_id}: {str(e)}"
        )
        raise


@blitzy_exponential_retry_service()
def get_azure_repos_by_user_org_and_project(user_info, org_id, project_id):
    """
    Get Azure DevOps repositories for a user, organization, and project.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID
        project_id: Azure DevOps project ID

    Returns:
        List of repository dictionaries with id, name, and other details
    """
    try:
        logger.info(
            f"Getting Azure repositories for user {user_info.id} in org {org_id}, project {project_id}"
        )

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        # Resolve organization ID to organization name
        azure_service = AzureAppService()
        org_name = azure_service.resolve_organization_id(org_id, access_token)

        logger.info(f"Resolved org_id {org_id} to org_name {org_name}")

        # Call Azure DevOps API to get repositories for the specific project using organization name
        url = f"https://dev.azure.com/{org_name}/{project_id}/_apis/git/repositories?api-version=6.0"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        repositories = []

        for repo in data.get("value", []):
            repo_data = {
                "id": repo.get("id"),
                "name": repo.get("name"),
                "url": repo.get("url"),
                "defaultBranch": repo.get("defaultBranch"),
                "size": repo.get("size", 0),
                "project": {"id": project_id},
            }
            repositories.append(repo_data)

        logger.info(
            f"Found {len(repositories)} Azure repositories for user {user_info.id} "
            f"in org {org_name} (ID: {org_id}), project {project_id}"
        )
        return repositories

    except Exception as e:
        logger.error(
            f"Error retrieving Azure repositories for user {user_info.id} "
            f"in org {org_id}, project {project_id}: {str(e)}"
        )
        raise


@blitzy_exponential_retry_service()
def get_azure_branches_by_repo_id(user_info, org_id, project_id, repo_id):
    """
    Get Azure DevOps branches for a repository, excluding locked branches.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID
        project_id: Azure DevOps project ID
        repo_id: Azure DevOps repository ID

    Returns:
        List of branch dictionaries with name, commit info, and other details (excludes locked branches)
    """
    try:
        logger.info(
            f"Getting Azure branches for user {user_info.id} in org {org_id}, project {project_id}, repo {repo_id}"
        )

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        # Resolve organization ID to organization name
        azure_service = AzureAppService()
        org_name = azure_service.resolve_organization_id(org_id, access_token)

        logger.info(f"Resolved org_id {org_id} to org_name {org_name}")

        # Call Azure DevOps API to get branches for the specific repository using organization name
        url = (
            f"https://dev.azure.com/{org_name}/{project_id}/_apis/git/repositories/"
            f"{repo_id}/refs?filter=heads/&api-version=6.0"
        )
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        branches = []
        filtered_count = 0

        for ref in data.get("value", []):
            # Azure DevOps returns refs like "refs/heads/main", we want just "main"
            ref_name = ref.get("name", "")
            if ref_name.startswith("refs/heads/"):
                branch_name = ref_name[11:]  # Remove "refs/heads/" prefix

                # Check if this branch is locked
                # The isLocked field is only present when the branch is locked
                is_locked = ref.get("isLocked", False)
                locked_by = ref.get("isLockedBy")

                if is_locked:
                    logger.info(f"Filtering out locked branch: {branch_name} (locked by: {locked_by})")
                    filtered_count += 1
                    continue

                branch_data = {
                    "name": branch_name,
                    "commit": {"sha": ref.get("objectId"), "url": ref.get("url")},
                    "protected": False,  # TODO: Add protection status if needed
                }
                branches.append(branch_data)

        logger.info(
            f"Found {len(branches)} unlocked Azure branches for user {user_info.id} "
            f"in org {org_name} (ID: {org_id}), project {project_id}, repo {repo_id}"
            f" (filtered out {filtered_count} locked branches)"
        )
        return branches

    except Exception as e:
        logger.error(
            f"Error retrieving Azure branches for user {user_info.id} "
            f"in org {org_id}, project {project_id}, repo {repo_id}: {str(e)}"
        )
        raise


@blitzy_exponential_retry_service()
def get_azure_branches_by_repo_with_context(user_info, org_id, project_id, repo_id):
    """
    Get Azure DevOps branches for a repository with full context.

    Args:
        user_info: User information object with id and other details
        org_id: Azure DevOps organization ID
        project_id: Azure DevOps project ID
        repo_id: Azure DevOps repository ID

    Returns:
        List of branch dictionaries with name, commit info, and other details
    """
    try:
        logger.info(
            f"Getting Azure branches for user {user_info.id} in org {org_id}, "
            f"project {project_id}, repo {repo_id}"
        )

        # Get Azure access token for the user
        token_data = fetch_azure_secret_for_user(
            user_info.id, VersionControlSystem.AZURE_DEVOPS
        )
        access_token = token_data.accessToken

        # Resolve organization ID to organization name
        azure_service = AzureAppService()
        org_name = azure_service.resolve_organization_id(org_id, access_token)

        logger.info(f"Resolved org_id {org_id} to org_name {org_name}")

        # Call Azure DevOps API to get branches for the specific repository using organization name
        url = (
            f"https://dev.azure.com/{org_name}/{project_id}/_apis/git/repositories/"
            f"{repo_id}/refs?filter=heads/&api-version=6.0"
        )
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        branches = []

        for ref in data.get("value", []):
            # Azure DevOps returns refs like "refs/heads/main", we want just "main"
            ref_name = ref.get("name", "")
            if ref_name.startswith("refs/heads/"):
                branch_name = ref_name[11:]  # Remove "refs/heads/" prefix

                branch_data = {
                    "name": branch_name,
                    "commit": {"sha": ref.get("objectId"), "url": ref.get("url")},
                    "protected": False,  # TODO: Add protection status if needed
                }
                branches.append(branch_data)

        logger.info(
            f"Found {len(branches)} Azure branches for user {user_info.id} "
            f"in org {org_name} (ID: {org_id}), project {project_id}, repo {repo_id}"
        )
        return branches

    except Exception as e:
        logger.error(
            f"Error retrieving Azure branches for user {user_info.id} "
            f"in org {org_id}, project {project_id}, repo {repo_id}: {str(e)}"
        )
        raise
