import base64
import json
import tempfile
import uuid
from dataclasses import dataclass
from typing import Dict, Optional

import google
from blitzy_utils.logger import logger
from flask import Blueprint, jsonify, request
from flask_utils.decorators import validate_request
from google.auth.transport.requests import Request
from kubernetes import client
from kubernetes.client import (V1<PERSON><PERSON>r, V1EnvFromSource, V1EnvVar, V1Job,
                               V1JobSpec, V1ObjectMeta, V1PodSpec,
                               V1PodTemplateSpec, V1SecretEnvSource,
                               V1SecretVolumeSource, V1Volume, V1VolumeMount)

from src.api.models import CreateJobInput, ExecutionType
from src.consts import (GCP_SERVICE_ACCOUNT_SECRET_NAME, GKE_CLUSTER_NAME,
                        GKE_ZONE, PROJECT_ID, WINDOWS_JOB_IMAGE,
                        WINDOWS_JOB_IMAGE_PULL_POLICY, WINDOW<PERSON>_JOB_LIMITS_CPU,
                        WINDOWS_JOB_LIMITS_MEMORY, WINDOWS_JOB_NAMESPACE,
                        WINDOWS_JOB_REQUESTS_CPU, WINDOWS_JOB_REQUESTS_MEMORY)

job_bp = Blueprint("job", __name__, url_prefix="/v1/job")

ARCHIE_JOB_BASE_SECRET = "archie-job-base"
ARCHIE_JOB_NAME = "archie-job"


@dataclass
class JobConfiguration:
    """Configuration class for Kubernetes job parameters"""
    job_name: str
    image: str
    requests_cpu: str
    requests_memory: str
    limits_cpu: str
    limits_memory: str
    command: Optional[list] = None
    args: Optional[list] = None
    env_vars: Optional[Dict[str, str]] = None
    is_windows: bool = True


class KubernetesJobManager:
    """Manages Kubernetes job operations"""

    def create_job(self, config: JobConfiguration) -> str:
        """Create a Kubernetes job with the given configuration"""
        api_client = self._create_fresh_gke_client()
        batch_v1 = client.BatchV1Api(api_client)

        env_list = self._prepare_environment_variables(config.env_vars)
        job_spec = self._build_job_spec(config, env_list, config.is_windows)
        logger.debug(f"Job spec: {job_spec.to_str()}")

        results = batch_v1.create_namespaced_job(namespace=WINDOWS_JOB_NAMESPACE, body=job_spec)
        return f"Job {config.job_name} created successfully"

    def _create_fresh_gke_client(self):
        """
        Create a fresh GKE client with current authentication token.

        Why we are creating a fresh client:
        We are running this on cloud run service. As of now, gcloud or google SDK can create kubeconfig with limited
        expiration time. So, if the token expires, the kubeconfig will be invalid. So, we are creating a fresh client.
        """

        credentials, project = google.auth.default()
        scoped_credentials = self._get_scoped_credentials(credentials)

        cluster_endpoint, ca_cert_path = self._get_cluster_config(scoped_credentials)

        configuration = client.Configuration()
        configuration.host = f"https://{cluster_endpoint}"
        configuration.ssl_ca_cert = ca_cert_path
        configuration.api_key_prefix["authorization"] = "Bearer"

        # Always refresh credentials to get fresh token
        auth_req = Request()
        scoped_credentials.refresh(auth_req)
        configuration.api_key["authorization"] = scoped_credentials.token

        return client.ApiClient(configuration)

    def _get_scoped_credentials(self, credentials):
        """Get scoped credentials for GKE"""
        if hasattr(credentials, "with_scopes"):
            return credentials.with_scopes([
                "https://www.googleapis.com/auth/cloud-platform",
                "https://www.googleapis.com/auth/userinfo.email",
            ])
        return credentials

    def _get_cluster_config(self, credentials):
        """Get cluster configuration"""
        from google.cloud import container_v1
        cluster_client = container_v1.ClusterManagerClient(credentials=credentials)
        cluster_path = f"projects/{PROJECT_ID}/locations/{GKE_ZONE}/clusters/{GKE_CLUSTER_NAME}"
        cluster = cluster_client.get_cluster(name=cluster_path)

        ca_cert = base64.b64decode(cluster.master_auth.cluster_ca_certificate)
        ca_filename = tempfile.NamedTemporaryFile(delete=False, suffix=".crt")
        ca_filename.write(ca_cert)
        ca_filename.close()
        return cluster.endpoint, ca_filename.name

    def _prepare_environment_variables(self, env_vars: Optional[Dict[str, str]]) -> list:
        """Prepare environment variables for the container"""
        env_list = []
        if env_vars:
            for key, value in env_vars.items():
                if isinstance(value, (dict, list)):
                    value_str = json.dumps(value)
                else:
                    value_str = str(value)
                env_list.append(client.V1EnvVar(name=key, value=value_str))
        return env_list

    def _build_job_spec(self, config: JobConfiguration, env_list: list,
                        is_windows: bool = True) -> V1Job:
        """Build the Kubernetes job specification"""

        mount_path = "/var/secrets/google" if not is_windows else "C:\\secrets\\google"
        credentials_path = f"{mount_path}/service-account.json"

        env_list_with_creds = env_list.copy()
        env_list_with_creds.append(
            V1EnvVar(
                name="GOOGLE_APPLICATION_CREDENTIALS",
                value=credentials_path
            )
        )

        # Alternative: Load all secrets as env vars using envFrom
        env_from = [
            V1EnvFromSource(
                secret_ref=V1SecretEnvSource(
                    name=ARCHIE_JOB_BASE_SECRET
                )
            )
        ]

        pod_spec = V1PodSpec(
            containers=[
                V1Container(
                    name="job-container",
                    image=config.image,
                    command=config.command,
                    args=config.args,
                    env=env_list_with_creds,
                    env_from=env_from,
                    image_pull_policy=WINDOWS_JOB_IMAGE_PULL_POLICY,
                    resources=client.V1ResourceRequirements(
                        requests={
                            "cpu": config.requests_cpu,
                            "memory": f"{config.requests_memory}Gi"
                        },
                        limits={
                            "cpu": config.limits_cpu,
                            "memory": f"{config.limits_memory}Gi"
                        }
                    ),
                    volume_mounts=[
                        V1VolumeMount(
                            name="google-service-account",
                            mount_path=mount_path,
                            read_only=True
                        )
                    ]
                )
            ],
            volumes=[
                V1Volume(
                    name="google-service-account",
                    secret=V1SecretVolumeSource(
                        secret_name=GCP_SERVICE_ACCOUNT_SECRET_NAME
                    )
                )
            ],
            restart_policy="Never"
        )

        if is_windows:
            pod_spec.node_selector = {"kubernetes.io/os": "windows"}
            pod_spec.tolerations = [
                client.V1Toleration(
                    key="node.kubernetes.io/os",
                    operator="Equal",
                    value="windows",
                    effect="NoSchedule"
                )
            ]

        return V1Job(
            api_version="batch/v1",
            kind="Job",
            metadata=V1ObjectMeta(name=config.job_name),
            spec=V1JobSpec(
                template=V1PodTemplateSpec(spec=pod_spec),
                backoff_limit=3,
                ttl_seconds_after_finished=604800  # 7 Days in seconds
            )
        )


# Global instance
job_manager = KubernetesJobManager()


@job_bp.route("/windows/trigger", methods=["POST"])
def trigger_windows_job():
    """Handle Pub/Sub push messages to trigger Windows jobs"""
    envelope = request.get_json()
    if not isinstance(envelope, dict) or "message" not in envelope:
        msg = "invalid Pub/Sub message format"
        logger.error(f"error: {msg}")
        return f"Bad Request: {msg}", 400

    pubsub_message = envelope["message"]
    logger.info(f"Received Pub/Sub message: {pubsub_message}")
    try:
        if isinstance(pubsub_message, dict) and "data" in pubsub_message:
            decoded_result = base64.b64decode(pubsub_message["data"]).decode("utf-8").strip()
            payload = json.loads(decoded_result)
            logger.info(f"Processing payload: {payload}")
            if payload.get("execution_type") != ExecutionType.WINDOWS.value:
                logger.error(f"Invalid execution type: {payload.get('execution_type')}")
                return "Event processed successfully", 200

            message_payload = CreateJobInput(**payload)
            config = _create_job_config_from_input_payload(message_payload)

            result = job_manager.create_job(config)
            logger.info(f"Job {config.job_name} created successfully: {result}")
    except Exception as e:
        logger.error(f"Failed to process payload {e}")
    return "Event processed successfully", 200


@job_bp.route("/create-job", methods=["POST"])
@validate_request(CreateJobInput)
def create_gke_job(payload: CreateJobInput):
    """Create a GKE job via direct API call"""
    config = _create_job_config_from_input_payload(payload)
    result = job_manager.create_job(config)
    return jsonify({"status": "success", "message": result})


def _create_job_config_from_input_payload(payload: CreateJobInput):
    config = JobConfiguration(
        job_name=generate_job_name(payload.job_name),
        image=payload.image if payload.image else WINDOWS_JOB_IMAGE,
        requests_cpu=payload.windows_job_requests_cpu,
        requests_memory=payload.windows_job_requests_memory,
        limits_cpu=payload.windows_job_limits_cpu,
        limits_memory=payload.windows_job_limits_memory,
        command=payload.command,
        args=payload.args,
        env_vars=payload.env_vars,
        is_windows=is_windows_job(payload.execution_type.value)
    )
    return config


def _create_job_config_from_payload(payload: Dict[str, str]) -> JobConfiguration:
    """Create job configuration from payload"""
    env_vars = payload.get("env_vars", {})
    if not env_vars:
        logger.warning("No env_vars provided. Using default env_vars.")
        env_vars = {k: v for k, v in payload.items()}

    job_name = generate_job_name(payload.get("job_name"))
    return JobConfiguration(
        job_name=job_name,
        image=payload.get("image", WINDOWS_JOB_IMAGE),
        requests_cpu=payload.get("windows_job_requests_cpu", WINDOWS_JOB_REQUESTS_CPU),
        requests_memory=payload.get("windows_job_requests_memory", WINDOWS_JOB_REQUESTS_MEMORY),
        limits_cpu=payload.get("windows_job_limits_cpu", WINDOWS_JOB_LIMITS_CPU),
        limits_memory=payload.get("windows_job_limits_memory", WINDOWS_JOB_LIMITS_MEMORY),
        env_vars=env_vars,
        is_windows=is_windows_job("execution_type")
    )


def is_windows_job(execution_type: str) -> bool:
    """Check if the payload is a Windows job"""
    return execution_type == ExecutionType.WINDOWS.value


def generate_job_name(job_name: str) -> str:
    """Generate a unique job name"""
    if not job_name:
        job_name = ARCHIE_JOB_NAME
    return f"{job_name}-{uuid.uuid4().hex[:8]}"
