import os
import traceback
from urllib.parse import urlencode

import stripe as stripe_lib
from blitzy_utils.logger import logger
from flask import Blueprint, jsonify, request
from flask_utils.decorators import flask_pydantic_response

from src.api.models import (CreateStripeCheckoutResponse,
                            CreateStripeManageSubscriptionResponse,
                            Status404, Status400)
from src.api.utils.stripe_utils import (handle_checkout_session_completed,
                                        handle_invoice_payment_failed,
                                        handle_invoice_payment_succeeded,
                                        handle_subscription_deleted,
                                        handle_subscription_updated,
                                        is_valid_product_id)
from src.consts import PLATFORM_URL, STRIPE_BLITZY_PRO_PRICE_ID
from src.middleware.decorators import get_user_info
from src.service.subscription_service import get_subscription_info_by_user_id

stripe_lib.api_key = os.getenv("STRIPE_API_KEY")
endpoint_secret = os.getenv("STRIPE_ENDPOINT_SECRET")
stripe_bp = Blueprint("stripe", __name__, url_prefix="/v1/stripe")


def validate_origin(origin):
    """
    Validate if the origin matches the configured PLATFORM_URL.
    Returns the origin if valid, raises ValueError if invalid.
    """
    if not origin:
        raise ValueError("Origin header is missing")

    if origin != PLATFORM_URL:
        raise ValueError(f"Origin {origin} is not allowed")

    return origin


@stripe_bp.route("/webhook", methods=["POST"])
def webhook():
    payload = request.data
    sig_header = request.headers["STRIPE_SIGNATURE"]

    try:
        event = stripe_lib.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
    except ValueError as e:
        logger.error(f"value error during webhook validation: {e}")
        return jsonify({"error": str(e)}), 400
    except stripe_lib.SignatureVerificationError as e:
        logger.error(f"signature error during webhook validation: {e}")
        return jsonify({"error": str(e)}), 400

    try:
        if not is_valid_product_id(event):
            return jsonify(success=True), 200

        if event["type"] == "checkout.session.completed":
            handle_checkout_session_completed(event)
        elif event["type"] == "customer.subscription.updated":
            handle_subscription_updated(event)
        elif event["type"] == "customer.subscription.deleted":
            handle_subscription_deleted(event)
        elif event["type"] == "invoice.payment_succeeded":
            handle_invoice_payment_succeeded(event)
        elif event["type"] == "invoice.payment_failed":
            handle_invoice_payment_failed(event)

        return jsonify(success=True), 200

    except Exception as e:
        logger.warning(f"Error processing Stripe webhook: {e} with event payload {event}",
                       traceback=traceback.format_exc())
        return jsonify(success=True), 200


@stripe_bp.route("/create-checkout-session", methods=["POST"])
@get_user_info
@flask_pydantic_response
def create_checkout_session(user_info):
    """
    Create a Stripe Checkout Session for Blitzy PRO with success/cancel URLs.
    Only shows trial offer for users from US or with no country code.
    """
    origin = validate_origin(request.headers.get("Origin"))

    # Extract project_id from request if provided
    data = request.json or {}
    project_id = data.get("project_id")
    if project_id:
        base_url = f"{origin}/workspace/project/{project_id}/status"
    else:
        base_url = f"{origin}/workspace"

    success_params = {
        "session_id": "{CHECKOUT_SESSION_ID}",
    }
    success_query_string = urlencode(success_params).replace("%7B", "{").replace("%7D", "}")
    success_url = f"{base_url}?{success_query_string}"
    cancel_url = base_url

    try:
        # Base checkout session parameters
        checkout_params: stripe_lib.checkout.Session.CreateParams = {
            "client_reference_id": user_info["id"],
            "customer_email": user_info["email"],
            "line_items": [
                {
                    "price": STRIPE_BLITZY_PRO_PRICE_ID,
                    "quantity": 1,
                }
            ],
            "mode": "subscription",
            "success_url": success_url,
            "cancel_url": cancel_url,
            "automatic_tax": {
                "enabled": True
            },
            "consent_collection": {
                "terms_of_service": "required"
            }
        }

        checkout_session = stripe_lib.checkout.Session.create(**checkout_params)
        return CreateStripeCheckoutResponse(url=checkout_session.url), 201

    except stripe_lib.error.StripeError as e:
        logger.error(f"stripe error: {e}")
        return Status400(message=str(e)), 400
    except KeyError as e:
        logger.error(f"key error: {e}")
        return Status400(message=f"Missing parameter: {str(e)}"), 400


@stripe_bp.route("/manage-subscription", methods=["POST"])
@get_user_info
@flask_pydantic_response
def create_portal_session(user_info):
    """
    Create a Stripe customer portal session for subscription management.
    """
    try:
        origin = validate_origin(request.headers.get('Origin'))
        return_url = f"{origin}/workspace/settings/plan"

        # Get the subscription info for the user
        subscription = get_subscription_info_by_user_id(user_info["id"])

        if not subscription:
            logger.warning(f"No subscription found for user {user_info['id']}")
            return Status404(message="No subscription found for this user"), 404

        if not subscription.stripe_customer_id:
            logger.warning(f"No Stripe customer ID found for subscription {subscription.id}")
            return Status404(message="No Stripe customer ID found"), 404

        # Create a billing portal session
        portal_session = stripe_lib.billing_portal.Session.create(
            customer=subscription.stripe_customer_id,
            return_url=return_url
        )

        return CreateStripeManageSubscriptionResponse(url=portal_session.url), 200
    except stripe_lib.error.StripeError as e:
        return Status400(message=str(e)), 400
