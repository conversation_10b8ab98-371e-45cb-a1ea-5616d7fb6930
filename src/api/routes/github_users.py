"""
GitHub user-related API endpoints for archie-github-handler.

This module contains GitHub-specific user operations that directly implement
GitHub functionality without service type detection.
"""

from blitzy_utils.logger import logger
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response, validate_request

from threading import <PERSON>
from datetime import datetime, timedelta, timezone
from typing import Dict, Tu<PERSON>, Optional

from github import GithubIntegration
from src.api.models import (BranchList, DefaultBranchOutput,
                            GithubOrgBasicList, PRActionInput, RepositoryList)
from src.api.routes.users import (map_org_list_to_pydantic,
                                  map_repo_list_to_pydantic)
from src.api.utils.github_utils import (get_branches_by_repo_id,
                                        get_installation_id_for_account,
                                        get_orgs_by_installations,
                                        get_repo_by_id,
                                        get_repos_by_installation_id)
from src.consts import GITHUB_APP_ID, GITHUB_PRIVATE_KEY
from src.error.errors import GithubB<PERSON><PERSON>rror, ResourceNotFound
from src.github.github_app_service import GithubAppService


from src.service.github_installation_access_service import (
    get_active_github_installation_by_repo_id,
    get_only_github_installations_by_user)
from src.service.user_service import get_user_by_id

# Create GitHub users blueprint
github_users_bp = Blueprint("github_users", __name__, url_prefix="/v1/github/users")

ORG_REPOS_CACHE: Dict[str, Tuple[any, datetime]] = {}
CACHE_LOCK = Lock()
CACHE_DURATION_HOURS = 1  # 1 hour

def validate_repo_belongs_to_account(installation_id: str, account_name: str) -> bool:
    """
    Validate that the installation belongs to the specified account name.

    Args:
        installation_id: The GitHub installation ID
        account_name: The expected account/organization name

    Returns:
        bool: True if the installation belongs to the account, False otherwise
    """
    try:
        git_integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)
        installation = git_integration.get_app_installation(installation_id)
        return installation.account.login.lower() == account_name.lower()
    except Exception as e:
        logger.warning(f"Failed to validate installation {installation_id} for account {account_name}: {str(e)}")
        return False


@github_users_bp.route("/<user_id>/organizations", methods=["GET"])
@flask_pydantic_response
def get_github_user_organizations(user_id: str):
    """
    Get GitHub organizations for a specific user.

    This endpoint only handles GitHub organizations.
    """
    try:
        user_info = get_user_by_id(user_id)
        if not user_info:
            raise ResourceNotFound(f"User with id {user_id} not found")

        # Since this is /v1/github/* endpoint, we know it's GitHub
        # No need to check installation service type
        installation_list = get_only_github_installations_by_user(user_id)
        if not installation_list:
            logger.warning(f"No installations found for user {user_id}")
            # Return empty organizations list instead of raising exception
            return GithubOrgBasicList(results=[]), 200

        # Get GitHub organizations
        org_list = get_orgs_by_installations(installation_list)

        response = map_org_list_to_pydantic(org_list)
        return response, 200
    except Exception as e:
        logger.error(f"Error getting GitHub organizations for user {user_id}: {str(e)}")
        # Return empty organizations list instead of raising exception
        return GithubOrgBasicList(results=[]), 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories", methods=["GET"])
@flask_pydantic_response
def get_github_user_repositories(user_id: str, account_name: str):
    """
    Get GitHub repositories for a specific user and organization.

    This endpoint only handles GitHub repositories with 1-hour caching per organization.
    """
    try:
        # Check cache first
        cached_repos = get_cached_repos(account_name)
        if cached_repos is not None:
            # Return cached data without hitting GitHub API
            response = map_repo_list_to_pydantic([cached_repos])
            return response, 200

        # Cache miss - proceed with API call
        logger.info(f"Cache miss for organization {account_name}, making API call")
        
        user_info = get_user_by_id(user_id)
        if not user_info:
            raise ResourceNotFound(f"User with id {user_id} not found")

        # Since this is /v1/github/* endpoint, we know it's GitHub
        # No need to check installation service type
        installations = get_only_github_installations_by_user(user_id)
        if not installations:
            logger.warning(f"No installations found for user {user_id}")
            # Return empty repositories list instead of raising exception
            return RepositoryList(results=[]), 200

        # Filter installations to find the one that matches the account_name
        installation_ids = [inst.installation_id for inst in installations]
        matching_installation_id = get_installation_id_for_account(installation_ids, account_name)

        if not matching_installation_id:
            logger.warning(f"No installation found for account {account_name} for user {user_id}")
            # Return empty repositories list instead of raising exception
            return RepositoryList(results=[]), 200

        # Get GitHub repositories for the specific organization
        repos = get_repos_by_installation_id(matching_installation_id)
        if not repos:
            raise GithubBaseError(
                f"Something is wrong we get `None` for repos for installation_id {matching_installation_id}. check logs"
            )
        
        if repos  and len(repos.get("repositories", {})) > 0:
            # Cache the repos data before returning
            set_cached_repos(account_name, repos)
        
        response = map_repo_list_to_pydantic([repos])
        return response, 200
        
    except Exception as e:
        logger.error(
            f"Error getting GitHub repositories for user {user_id}, account {account_name}: {str(e)}"
        )
        # Return empty repositories list instead of raising exception
        return RepositoryList(results=[]), 200

@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>/branches", methods=["GET"])
@flask_pydantic_response
def get_github_user_branches(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub branches for a specific user, organization, and repository.

    This endpoint only handles GitHub branches.
    """
    try:
        user_info = get_user_by_id(user_id)
        if not user_info:
            raise ResourceNotFound(f"User with id {user_id} not found")

        # Get active installation for this repository
        installation = get_active_github_installation_by_repo_id(repo_id)
        if not installation:
            raise ResourceNotFound(
                f"No active installation found for GitHub repository {repo_id}"
            )

        # Validate that the repository belongs to the specified account
        if not validate_repo_belongs_to_account(installation.installation_id, account_name):
            raise ResourceNotFound(f"Repository {repo_id} does not belong to account {account_name}")

        logger.debug(f"Active installation: {installation}")
        # Get GitHub branches using installation_id
        branches_data = get_branches_by_repo_id(installation.installation_id, repo_id)
        if not branches_data or not branches_data.get("branches"):
            logger.warning(f"No branches found for repo {repo_id}")
            # Return empty branches list instead of raising exception
            return BranchList(results=[]), 200
        logger.debug(f"Branches data: {branches_data}")
        # Extract the branches list from the response
        branches_list = branches_data["branches"]
        response = BranchList(results=branches_list)
        return response, 200
    except Exception as e:
        logger.error(
            f"Error getting GitHub branches for user {user_id}, account {account_name}, repo {repo_id}: {str(e)}"
        )
        # Return empty branches list instead of raising exception
        return BranchList(results=[]), 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>", methods=["GET"])
@flask_pydantic_response
def get_github_user_repository(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub repository details for a specific user, organization, and repository.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Validate that the repository belongs to the specified account
    if not validate_repo_belongs_to_account(installation.installation_id, account_name):
        raise ResourceNotFound(f"Repository {repo_id} does not belong to account {account_name}")

    # Get GitHub repository using installation_id
    repo = get_repo_by_id(installation.installation_id, repo_id)

    return repo, 200


@github_users_bp.route("/<user_id>/organizations/<account_name>/repositories/<repo_id>/default/branch", methods=["GET"])
@flask_pydantic_response
def get_github_user_default_branch(user_id: str, account_name: str, repo_id: str):
    """
    Get GitHub default branch for a specific user, organization, and repository.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Validate that the repository belongs to the specified account
    if not validate_repo_belongs_to_account(installation.installation_id, account_name):
        raise ResourceNotFound(f"Repository {repo_id} does not belong to account {account_name}")

    # Get default branch using GitHub service
    github_service = GithubAppService()
    default_branch = github_service.get_default_branch(
        repo_id, installation.installation_id
    )

    response = DefaultBranchOutput(default_branch=default_branch)
    return response, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/branch/<branch_name>/head/commit",
    methods=["GET"],
)
@flask_pydantic_response
def get_github_user_branch_head_commit(user_id: str, account_name: str, repo_id: str, branch_name: str):
    """
    Get GitHub branch head commit for a specific user, organization, repository, and branch.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Validate that the repository belongs to the specified account
    if not validate_repo_belongs_to_account(installation.installation_id, account_name):
        raise ResourceNotFound(f"Repository {repo_id} does not belong to account {account_name}")

    # Get branch head commit using GitHub service
    github_service = GithubAppService()
    commit = github_service.get_branch_head_commit(
        repo_id, installation.installation_id, branch_name
    )

    return commit, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/pr/<pr_number>/action",
    methods=["POST"],
)
@validate_request(PRActionInput)
@flask_pydantic_response
def post_github_user_pr_action(user_id: str, account_name: str, repo_id: str, pr_number: str, payload: PRActionInput):
    """
    Perform GitHub PR action for a specific user, organization, repository, and PR.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Validate that the repository belongs to the specified account
    if not validate_repo_belongs_to_account(installation.installation_id, account_name):
        raise ResourceNotFound(f"Repository {repo_id} does not belong to account {account_name}")

    # Perform PR action using GitHub service
    github_service = GithubAppService()
    result = github_service.manage_pull_request(
        user_id,
        int(pr_number),
        payload.action.value,
        installation_id=installation.installation_id,
        repo_id=repo_id,
    )

    return result, 200


@github_users_bp.route(
    "/<user_id>/organizations/<account_name>/repositories/<repo_id>/pr/<pr_number>/status",
    methods=["GET"],
)
@flask_pydantic_response
def get_github_user_pr_status(user_id: str, account_name: str, repo_id: str, pr_number: str):
    """
    Get GitHub PR status for a specific user, organization, repository, and PR.

    This endpoint only handles GitHub repositories.
    """
    user_info = get_user_by_id(user_id)
    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    # Get active installation for this repository
    installation = get_active_github_installation_by_repo_id(repo_id)
    if not installation:
        raise ResourceNotFound(f"No active installation found for repository {repo_id}")

    # Validate that the repository belongs to the specified account
    if not validate_repo_belongs_to_account(installation.installation_id, account_name):
        raise ResourceNotFound(f"Repository {repo_id} does not belong to account {account_name}")

    # Get PR status using GitHub service
    github_service = GithubAppService()
    status = github_service.get_pr_status_by_repo_id_and_pr_number(
        repo_id, int(pr_number), installation.installation_id
    )

    return status, 200


def get_cached_repos(org_name: str) -> Optional[any]:
    """
    Get cached repositories for an organization if cache is valid.
    
    Args:
        org_name: The organization name
        
    Returns:
        Cached repos data if valid, None if cache is empty or expired
    """
    with CACHE_LOCK:
        if org_name not in ORG_REPOS_CACHE:
            return None
            
        repos_data, expiry_time = ORG_REPOS_CACHE[org_name]
        current_time = datetime.now(timezone.utc)
        
        # Check if cache is still valid
        if current_time < expiry_time:
            logger.info(f"Cache hit for organization {org_name} (expires at {expiry_time})")
            return repos_data
        else:
            # Cache expired, remove it
            logger.info(f"Cache expired for organization {org_name} (expired at {expiry_time}), removing from cache")
            del ORG_REPOS_CACHE[org_name]
            return None

def set_cached_repos(org_name: str, repos_data: any) -> None:
    """
    Cache repositories data for an organization.
    
    Args:
        org_name: The organization name
        repos_data: The repositories data to cache
    """
    current_time = datetime.now(timezone.utc)
    expiry_time = current_time + timedelta(hours=CACHE_DURATION_HOURS)
    with CACHE_LOCK:
        ORG_REPOS_CACHE[org_name] = (repos_data, expiry_time)
        logger.info(f"Cached repositories for organization {org_name}, expires at {expiry_time}")