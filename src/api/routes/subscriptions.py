from blitzy_utils.logger import logger
from flask import Blueprint
from flask_utils.decorators import flask_pydantic_response
from sqlalchemy import Row

from src.api.models import UserPlan
from src.error.errors import ResourceNotFound
from src.service.subscriptions_service import get_subscription_plan_by_user_id
from src.service.users_service import get_user_by_id

subscription_bp = Blueprint("subscription", __name__, url_prefix="/v1/subscription")


@subscription_bp.route("/user/<user_id>/plan", methods=["GET"])
@flask_pydantic_response
def get_user_subscriptions(user_id: str):
    user_info = get_user_by_id(user_id)

    if not user_info:
        raise ResourceNotFound(f"User with id {user_id} not found")

    logger.info(f"User with id {user_id} found. Fetching subscription plan...")
    subscription_plan = get_subscription_plan_by_user_id(user_info.id)
    model_response = map_subscription_plan_info_to_model(subscription_plan)
    return model_response, 200


def map_subscription_plan_info_to_model(subscription_plan: Row):
    user_plan = UserPlan(planName=subscription_plan[0])
    return user_plan
