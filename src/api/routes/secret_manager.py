from flask import Blueprint, request
from flask_utils.decorators import flask_pydantic_response, validate_request

from src.api.models import SecretInput, SecretsOutput, Status200
from src.error.errors import UserNotFoundError
from src.gcp.secrets_handler import SecretManagerOperations
from src.service.user_service import get_user_by_id, mark_user_verified
from src.utils.logging import logger

secret_bp = Blueprint("secret", __name__, url_prefix="/secret")


@secret_bp.route("/<user_id>", methods=["GET"])
@flask_pydantic_response
def get_secret(user_id):
    logger.info(f"Fetching details of user {user_id}")
    user_info = get_user_by_id(user_id)
    if not user_info:
        logger.error(f"User with ID {user_id} not found")
        raise UserNotFoundError(f"User with ID {user_id} not found")

    version = request.args.get("version", "latest")
    logger.info(f"Fetching secret for user {user_id} with version {version}")
    secret = get_secret_value(user_id, version)
    response = SecretsOutput()
    response.accessToken = secret.get("access_token")
    response.code = secret.get("code")
    response.installationID = secret.get("installation_id")
    response.setupAction = secret.get("setup_action")
    return response, 200


@secret_bp.route("", methods=["POST"])
@validate_request(SecretInput)
@flask_pydantic_response
def post_secret(payload: SecretInput):
    logger.info(f"Fetching details of user {payload.userId}, payload: {payload}")
    user_info = get_user_by_id(payload.userId)
    if not user_info:
        logger.error(f"User with ID {payload.userId} not found")
        raise UserNotFoundError(f"User with ID {payload.userId} not found")
    logger.info(f"Creating/Updating secret for user {payload.userId}")
    result = create_or_update_secret(payload.userId, payload.accessToken, payload.code,
                                     payload.installationID, payload.setupAction)
    logger.info(f"Secret {result['operation']} successfully for user {payload.userId}")
    return Status200(message=f"Secret {result['operation']} successfully"), 200


def create_or_update_secret(user_id: str, access_token: str, code: str, installation_id: str, setup_action: str):
    secret_data = {
        "access_token": access_token,
        "code": code,
        "installation_id": installation_id,
        "setup_action": setup_action
    }

    secret_manager = SecretManagerOperations()
    response = secret_manager.create_or_update_user_secret(
        user_id=user_id,
        secret_data=secret_data
    )
    logger.info(f"Marking user {user_id} as verified")
    mark_user_verified(user_id)
    return response


def get_secret_value(user_id: str, version: str):
    secret_manager = SecretManagerOperations()
    return secret_manager.get_user_secret(user_id, version_id=version)
