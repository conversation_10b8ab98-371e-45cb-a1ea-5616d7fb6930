from blitzy_utils.common import blitzy_exponential_retry_service
from blitzy_utils.github import logger
from flask import Blueprint, request
from flask_utils.decorators import flask_pydantic_response, validate_request

from src.api.models import (SecretInput, SecretsOutput, Status200,
                            UserSecretInput)
from src.api.utils.secret_manager_utils import (create_or_update_github_secret,
                                                create_or_update_secret)
from src.error.errors import ResourceNotFound
from src.gcp.secrets_handler import SecretManagerOperations
from src.github.github_app_connection import GithubAppConnection
from src.service.user_service import get_user_by_id

secret_bp = Blueprint("secret", __name__, url_prefix="/secret")

"""
Bear with me on this one. I know there are duplicate APIs here, who's working is almost identical.
These APIs play crucial role in the pending github installation workflow.
It's a bit messy, but may be problem to be solved later.
Considering time I have simply migrated these APIs from secret-manager.
"""


# TODO (Chaitanya): Fix pending github integration workflow and reduce number of APIs.

@secret_bp.route("/user/<user_id>", methods=["GET"])
@flask_pydantic_response
def get_user_secret(user_id):
    logger.info(f"Fetching details of user {user_id}")
    user_info = get_user_by_id(user_id)
    if not user_info:
        logger.error(f"User with ID {user_id} not found")
        raise ResourceNotFound(f"User with ID {user_id} not found")

    version = request.args.get("version", "latest")
    logger.info(f"Fetching secret for user {user_id} with version {version}")
    secret = get_secret_value(user_id, version)
    response = SecretsOutput()
    response.accessToken = secret.get("access_token")
    response.code = secret.get("code")
    response.installationID = secret.get("installation_id")
    response.setupAction = secret.get("setup_action")
    return response, 200


@secret_bp.route("/user", methods=["POST"])
@validate_request(UserSecretInput)
@flask_pydantic_response
def post_user_secret(payload: UserSecretInput):
    logger.info(f"Fetching details of user {payload.userId}, payload: {payload}")
    user_info = get_user_by_id(payload.userId)
    if not user_info:
        logger.error(f"User with ID {payload.userId} not found")
        raise ResourceNotFound(f"User with ID {payload.userId} not found")
    logger.info(f"Creating/Updating secret for user {payload.userId}")
    result = create_or_update_secret(payload.userId, payload.accessToken, payload.code,
                                     payload.installationID, payload.setupAction)
    logger.info(f"Secret {result['operation']} successfully for user {payload.userId}")
    return Status200(message=f"Secret {result['operation']} successfully"), 200


def get_secret_value(user_id: str, version: str):
    secret_manager = SecretManagerOperations()
    return secret_manager.get_user_secret(user_id, version_id=version)


@secret_bp.route("/<installation_id>", methods=["GET"])
@flask_pydantic_response
def get_secret(installation_id):
    version = request.args.get("version", "latest")
    logger.info(f"Fetching secret for installation {installation_id} with version {version}")
    secret = get_github_secret_value(installation_id, version)
    response = SecretsOutput(
        accessToken=secret.get("access_token"),
        code=secret.get("code"),
        installationID=secret.get("installation_id"),
        setupAction=secret.get("setup_action")
    )
    return response, 200


@secret_bp.route("", methods=["POST"])
@validate_request(SecretInput)
@flask_pydantic_response
def post_secret(payload: SecretInput):
    logger.info(f"Creating/Updating secret for github installation {payload.installationID}")
    result = create_or_update_github_secret(payload.accessToken, payload.code,
                                            payload.installationID, payload.setupAction)
    logger.info(f"Secret {result['operation']} successfully for github installation {payload.installationID}")
    return Status200(message=f"Secret {result['operation']} successfully"), 200


def get_github_secret_value(installation_id: str, version: str):
    secret_manager = SecretManagerOperations()
    return secret_manager.get_github_secret(installation_id, version_id=version)


def get_github_secret_value_with_access_token(installation_id: str, version: str):
    """
    Get GitHub secret with access token using centralized GitHub connection.
    """
    secret_manager = SecretManagerOperations()
    secret = secret_manager.get_github_secret(installation_id, version_id=version)
    github_connection = GithubAppConnection()
    access_token = github_connection.get_access_token(installation_id)
    secret["access_token"] = access_token
    return secret


@blitzy_exponential_retry_service()
def get_access_token(installation_id: str) -> str:
    """
    Retrieve an access token for the specified installation ID with retry.

    :param installation_id: The installation ID for which the access token
        is to be retrieved
    :type installation_id: str
    :return: The access token corresponding to the given installation ID
    :rtype: str
    """
    github_app_connection = GithubAppConnection()
    access_token = github_app_connection.get_access_token(installation_id)
    return access_token


def get_azure_secret_value(tenant_id: str, version: str):
    secret_manager = SecretManagerOperations()
    return secret_manager.get_azure_secret(tenant_id, version_id=version)
