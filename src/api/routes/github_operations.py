import traceback
import requests

from blitzy_utils.sercert_manager import get_github_access_token
from flask import Blueprint, request
from flask_utils.decorators import flask_pydantic_response
from github import GithubEx<PERSON>, Auth, Github

from src.api.models import Status200, Status500
from src.api.routes.secret_manager import get_secret_value
from src.consts import GITHUB_APP_ID, GITHUB_PRIVATE_KEY, GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET
from src.error.errors import (InstallationIDNotFoundError,
                              InvalidGithubInstallationIDError,
                              UserNotFoundError)
from src.service.user_service import get_user_by_id, mark_user_verified
from src.utils.logging import logger

github_bp = Blueprint("github", __name__, url_prefix="/github")


@github_bp.route("/verify/<user_id>", methods=["GET"])
@flask_pydantic_response
def github_verify(user_id: str):
    logger.info(f"Fetching details of user {user_id}")
    user_info = get_user_by_id(user_id)
    if not user_info:
        logger.error(f"User with ID {user_id} not found")
        raise UserNotFoundError(f"User with ID {user_id} not found")

    version = request.args.get("version", "latest")
    logger.info(f"Fetching secret for user {user_id} with version {version}")
    response = get_secret_value(user_id, version)
    installation_id = response.get("installation_id")
    if not installation_id:
        logger.error(f"Installation id for user {user_id} not found")
        raise InstallationIDNotFoundError(f"Installation id for user {user_id} not found")

    setup_action: str = response.get("setup_action")
    if setup_action.lower() != "install":
        logger.error(f"Installation action for user {user_id} found to be `{setup_action}`")
        raise InstallationIDNotFoundError(f"Installation action for user {user_id} not found")

    # if not verify_app_installation(int(installation_id)):
    #     raise InvalidGithubInstallationIDError("Invalid installation ID")

    code = response.get("code")
    print(f'Fetching access token for user {user_id}')
    access_token = get_github_access_token(GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET, code)
    if not access_token:
        raise InvalidGithubInstallationIDError("Invalid access token")

    logger.info(f"Marking user {user_id} as verified")
    try:
        mark_user_verified(user_id)
        return Status200(message=access_token), 200
    except Exception as e:
        traceback.print_exc()
        logger.error(f"Error marking user {user_id} as verified: {str(e)}")
        return Status500(message="Something went wrong. Please try again"), 500


def verify_app_installation(installation_id: int) -> bool:
    """Verify if an installation ID is valid for your app"""
    try:
        auth = Auth.AppAuth(GITHUB_APP_ID, GITHUB_PRIVATE_KEY).get_installation_auth(
            installation_id=installation_id)
        g = Github(auth=auth)
        repos = g.get_repos()
        next(iter(repos), None)
        return True
    except GithubException as e:
        logger.error(f"GitHub API error: Status={e.status}, Data={e.data}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False
    finally:
        g.close()


def get_user_access_token(code: str):
    """Exchange the OAuth code for a user access token."""

    params = {
        'client_id': GITHUB_CLIENT_ID,
        'client_secret': GITHUB_CLIENT_SECRET,
        'code': code,
    }

    headers = {
        'Accept': 'application/json',
    }

    response = requests.post(
        'https://github.com/login/oauth/access_token',
        params=params,
        headers=headers
    )

    if response.status_code == 200:
        return response.json().get('access_token')
    else:
        print(f"Failed to get user token. Status code: {response.status_code}")
        return None
