# Admin Service Routes
from flask import Blueprint, request, jsonify
from flask_utils.decorators import flask_pydantic_response, validate_request
import http

from src.api.models import (
    EntityType,
    ErrorResponse,
    UpdateSubscriptionBenefitsInput,
)
from src.api.utils.mocks import (
    generate_mock_usage_data,
    generate_mock_subscription_status,
    generate_mock_benefits_response,
    generate_mock_validation_result
)

admin_subscription_bp = Blueprint('admin_subscription', __name__, url_prefix="/v1/admin/subscription")

def not_implemented_response(message, **kwargs):
    response = {
        "message": message,
        "status": "not_implemented",
    }
    response.update(kwargs)
    return jsonify(response), http.HTTPStatus.NOT_IMPLEMENTED

# Helper function for error responses
def create_error_response(status_code: int, error: str, message: str):
    """Create standardized error response"""
    error_response = ErrorResponse(
        message=error,
        details={"details": message},
        code=str(status_code)
    )
    return jsonify(error_response.model_dump()), status_code

# User Subscription Admin Endpoints
@admin_subscription_bp.route('/user/<string:user_id>/status', methods=['GET'])
@flask_pydantic_response
def get_subscription_status(user_id: str):
    """Mock endpoint for user subscription status"""
    try:
        return generate_mock_subscription_status()
    except Exception as e:
        return create_error_response(500, "Internal Server Error", str(e))


@admin_subscription_bp.route('/<string:entity_type>/<string:entity_id>/usage', methods=['GET'])
@flask_pydantic_response
def get_subscription_usage(entity_type: str, entity_id: str):
    """Mock endpoint for user subscription usage"""
    try:
        # Validate entity_type using Pydantic enum
        try:
            entity_type_enum = EntityType(entity_type.upper())
        except ValueError:
            return create_error_response(400, "Bad Request", f"Invalid entity type: {entity_type}")

        return generate_mock_usage_data(entity_id, entity_type_enum)
    except Exception as e:
        return create_error_response(500, "Internal Server Error", str(e))


# Validation Endpoints
@admin_subscription_bp.route('/user/<string:user_id>/tech-spec/validate', methods=['GET'])
@flask_pydantic_response
def admin_validate_tech_spec_subscription(user_id: str):
    """Mock endpoint for tech-spec validation"""
    try:
        # Mock: Allow tech-spec for most cases, deny for specific test case
        allowed = user_id != "blocked_entity_123"
        return generate_mock_validation_result(EntityType.USER, user_id, allowed)
    except Exception as e:
        return create_error_response(500, "Internal Server Error", str(e))


@admin_subscription_bp.route('/user/<string:user_id>/code-gen/validate', methods=['GET'])
@flask_pydantic_response
def validate_code_gen_usage(user_id: str):
    """Mock endpoint for code-gen validation"""
    try:
        # Mock: Allow code-gen for most cases, deny for specific test case
        allowed = user_id != "blocked_entity_456"
        validation_result = generate_mock_validation_result(EntityType.USER, user_id, allowed)
        # Adjust usage numbers for code-gen context
        validation_result.current_usage = 75 if allowed else 105
        return validation_result
    except Exception as e:
        return create_error_response(500, "Internal Server Error", str(e))


@admin_subscription_bp.route('/user/<string:user_id>/benefits', methods=['PUT'])
@validate_request(UpdateSubscriptionBenefitsInput)
@flask_pydantic_response
def update_subscription_benefits(user_id: str, payload: UpdateSubscriptionBenefitsInput):
    """Mock endpoint for updating subscription benefits"""
    try:
        # Generate response
        benefits_response = generate_mock_benefits_response(payload.company_id)

        # Override with provided benefits
        benefits_response.benefits = payload.benefits

        return benefits_response
    except Exception as e:
        return create_error_response(500, "Internal Server Error", str(e))

