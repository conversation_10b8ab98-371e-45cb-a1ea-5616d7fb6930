from typing import Any, Dict

from blitzy_utils.logger import logger
from flask import Blueprint, Response, request
from flask_utils.decorators import flask_pydantic_response

from src.api.models import (AttachmentMetadataOutput, BatchUploadResponse,
                            Status400, Status404)
from src.middleware.decorators import get_user_info
from src.service.attachment_service import (download_attachment_from_gcs,
                                            get_attachment_by_id,
                                            process_batch_upload_async)
from src.service.project_service import get_project_by_user_id

attachments_bp = Blueprint("attachments", __name__, url_prefix="/<project_id>/attachments")


@attachments_bp.route("/batch-upload", methods=["POST"])
@get_user_info
@flask_pydantic_response
async def batch_upload_attachments(user_info: Dict[str, Any], project_id: str):
    """
    Upload multiple attachments to a project.

    Endpoint: POST /v1/project/<projectId>/attachments/batch-upload
    Content-Type: multipart/form-data

    Form fields:
    - files: Multiple file uploads
    - techSpecId (optional): Tech spec ID to associate attachments with
    - userDescriptions (optional): Array of descriptions for each file

    Returns:
    - 201: List of successfully uploaded attachments
    - 400: Invalid request
    - 404: Project not found
    """
    logger.info(f"Batch upload request for project {project_id} by user {user_info['id']}")

    # Verify project exists and user has access
    project = get_project_by_user_id(user_info["id"], project_id)
    if not project:
        logger.warning(f"Project {project_id} not found for user {user_info['id']}")
        return Status404(message=f"Project {project_id} not found"), 404

    # Get files from request
    files = request.files.getlist('files')
    if not files or len(files) == 0:
        logger.warning("No files provided in batch upload request")
        return Status400(message="No files provided for upload"), 400

    # Filter out empty files
    valid_files = []
    for file in files:
        if file and file.filename and file.filename.strip():
            valid_files.append(file)

    if not valid_files:
        logger.warning("No valid files provided in batch upload request")
        return Status400(message="No valid files provided for upload"), 400

    # Get optional parameters
    tech_spec_id = request.form.get('techSpecId')
    user_descriptions_json = request.form.get('userDescriptions')

    # Parse user descriptions if provided (expecting JSON array string)
    user_descriptions = None
    if user_descriptions_json:
        try:
            import json
            user_descriptions_raw = json.loads(user_descriptions_json)
            if isinstance(user_descriptions_raw, list):
                # Filter out empty descriptions
                user_descriptions = [desc.strip() if desc and desc.strip() else None for desc in user_descriptions_raw]
        except (json.JSONDecodeError, TypeError) as e:
            logger.warning(f"Invalid JSON format for userDescriptions: {e}")
            # Fallback: treat as single description
            user_descriptions = [user_descriptions_json.strip() if user_descriptions_json.strip() else None]

    logger.info(f"Processing {len(valid_files)} files for project {project_id}")
    if tech_spec_id:
        logger.info(f"Associating attachments with tech spec {tech_spec_id}")

    try:
        # Process the batch upload using async version
        result = await process_batch_upload_async(
            files=valid_files,
            project_id=project_id,
            uploaded_by_user_id=user_info["id"],
            company_id=user_info.get("company_id"),  # Don't default to empty string
            tech_spec_id=tech_spec_id,
            user_descriptions=user_descriptions
        )

        # Convert results to response format
        attachment_outputs = []
        for attachment_data in result["results"]:
            # attachment_data is an AttachmentMetadata object, not a dictionary
            attachment_output = AttachmentMetadataOutput(
                attachmentId=attachment_data.id,
                fileName=attachment_data.file_name,
                mimeType=attachment_data.mime_type,
                fileSize=attachment_data.file_size,
                uploadTimestamp=int(attachment_data.upload_timestamp.timestamp()),  # Convert to Unix timestamp
                userDescription=attachment_data.user_description,
                status=attachment_data.status,  # Already a string value
                gcsPath=attachment_data.gcs_path
            )
            attachment_outputs.append(attachment_output)

        # Prepare response
        response = BatchUploadResponse(
            results=attachment_outputs,
            errors=result.get("errors")
        )

        # Determine response status
        if result.get("errors"):
            # Partial success - some files failed
            logger.warning(f"Batch upload completed with {len(result['errors'])} errors")
            return response, 207  # Multi-Status
        else:
            # Complete success
            logger.info(f"Batch upload completed successfully for {len(attachment_outputs)} files")
            return response, 201

    except Exception as e:
        logger.error(f"Error processing batch upload for project {project_id}: {str(e)}")
        return Status400(message=f"Error processing upload: {str(e)}"), 400


@attachments_bp.route("", methods=["GET"])
@get_user_info
@flask_pydantic_response
def get_project_attachments(user_info: Dict[str, Any], project_id: str):
    """
    Get all attachments for a project.

    Endpoint: GET /v1/project/<projectId>/attachments

    Query parameters:
    - techSpecId (optional): Filter by tech spec ID

    Returns:
    - 200: List of attachments
    - 404: Project not found
    """
    logger.info(f"Get attachments request for project {project_id} by user {user_info['id']}")

    # Verify project exists and user has access
    project = get_project_by_user_id(user_info["id"], project_id)
    if not project:
        logger.warning(f"Project {project_id} not found for user {user_info['id']}")
        return Status404(message=f"Project {project_id} not found"), 404

    # Get optional tech spec filter
    tech_spec_id = request.args.get('techSpecId')

    try:
        from src.service.attachment_service import \
            get_attachments_by_project_id

        # Get attachments
        attachments = get_attachments_by_project_id(
            project_id=project_id,
            tech_spec_id=tech_spec_id
        )

        # Convert to response format
        attachment_outputs = []
        for attachment in attachments:
            attachment_output = AttachmentMetadataOutput(
                attachmentId=attachment.id,
                fileName=attachment.file_name,
                mimeType=attachment.mime_type,
                fileSize=attachment.file_size,
                uploadTimestamp=int(attachment.upload_timestamp.timestamp()),  # Convert to Unix timestamp
                userDescription=attachment.user_description,
                status=attachment.status,  # Already a string value
                gcsPath=attachment.gcs_path
            )
            attachment_outputs.append(attachment_output)

        logger.info(f"Retrieved {len(attachment_outputs)} attachments for project {project_id}")
        return attachment_outputs, 200

    except Exception as e:
        logger.error(f"Error retrieving attachments for project {project_id}: {str(e)}")
        return Status400(message=f"Error retrieving attachments: {str(e)}"), 400


@attachments_bp.route("/<attachment_id>", methods=["DELETE"])
@get_user_info
@flask_pydantic_response
def delete_attachment(user_info: Dict[str, Any], project_id: str, attachment_id: str):
    """
    Delete an attachment.

    Endpoint: DELETE /v1/project/<projectId>/attachments/<attachmentId>

    Returns:
    - 200: Attachment deleted successfully
    - 404: Project or attachment not found
    """
    logger.info(f"Delete attachment {attachment_id} for project {project_id} by user {user_info['id']}")

    # Verify project exists and user has access
    project = get_project_by_user_id(user_info["id"], project_id)
    if not project:
        logger.warning(f"Project {project_id} not found for user {user_info['id']}")
        return Status404(message=f"Project {project_id} not found"), 404

    try:
        from src.service.attachment_service import \
            delete_attachment as delete_attachment_service

        # Delete the attachment
        success = delete_attachment_service(attachment_id)

        if success:
            logger.info(f"Attachment {attachment_id} deleted successfully")
            return {"message": "Attachment deleted successfully"}, 200
        else:
            logger.warning(f"Attachment {attachment_id} not found")
            return Status404(message=f"Attachment {attachment_id} not found"), 404

    except Exception as e:
        logger.error(f"Error deleting attachment {attachment_id}: {str(e)}")
        return Status400(message=f"Error deleting attachment: {str(e)}"), 400


@attachments_bp.route("/<attachment_id>/download", methods=["GET"])
@get_user_info
def download_attachment(user_info: Dict[str, Any], project_id: str, attachment_id: str):
    """
    Download an attachment file.

    Endpoint: GET /v1/project/<projectId>/attachments/<attachmentId>/download

    Returns:
    - 200: File content as binary response
    - 404: Project or attachment not found
    """
    logger.info(f"Download attachment {attachment_id} for project {project_id} by user {user_info['id']}")

    # Verify project exists and user has access
    project = get_project_by_user_id(user_info["id"], project_id)
    if not project:
        logger.warning(f"Project {project_id} not found for user {user_info['id']}")
        return Status404(message=f"Project {project_id} not found"), 404

    try:
        # Get attachment metadata
        attachment = get_attachment_by_id(attachment_id)

        if not attachment:
            logger.warning(f"Attachment {attachment_id} not found")
            return Status404(message=f"Attachment {attachment_id} not found"), 404

        # Verify attachment belongs to the project
        if attachment.project_id != project_id:
            logger.warning(f"Attachment {attachment_id} does not belong to project {project_id}")
            return Status404(message=f"Attachment {attachment_id} not found"), 404

        # Download file from GCS using admin service
        file_data = download_attachment_from_gcs(
            gcs_path=attachment.gcs_path,
            company_id=user_info.get("company_id")  # Don't default to empty string
        )

        # Create response with file data
        response = Response(
            file_data,
            mimetype=attachment.mime_type,
            headers={
                'Content-Disposition': f'attachment; filename="{attachment.file_name}"',
                'Content-Length': str(len(file_data))
            }
        )

        logger.info(f"Successfully downloaded attachment {attachment_id}")
        return response

    except Exception as e:
        logger.error(f"Error downloading attachment {attachment_id}: {str(e)}")
        return Status400(message=f"Error downloading attachment: {str(e)}"), 400
