from typing import Any, Dict, Optional

from common_models.db_client import get_db_session
from common_models.models import User
from sqlalchemy.orm import Session, joinedload

from src.error.errors import UserUpdateError
from src.utils.logging import logger


def get_user_by_id(user_id: str, session: Optional[Session] = None) -> Optional[User]:
    """
    Get user information by ID.
    :param user_id: User ID.
    :param session: Session if any.
    :return: User if found.
    """
    with get_db_session(session) as session:
        user = (session.query(User).options(joinedload(User.subscription)).filter(User.id == user_id).first())

        if user and not session:
            session.expunge(user)
        return user


def update_user_by_id(user_id: str, update_payload: Dict[Any, Any], session: Optional[Session] = None):
    """
    Update user information by ID.
    :param user_id: User ID.
    :param update_payload: Update payload.
    :param session: Session if any.
    """
    with get_db_session(session) as session:
        user_updated = session.query(User).filter(
            User.id == user_id,
        ).update(update_payload)

        if not user_updated:
            logger.error(f"Failed to update user {user_id}")
            raise UserUpdateError(f"Failed to update user information for ID {user_id}")

        session.commit()


def mark_user_verified(user_id):
    update_payload = {
        User.is_github_authenticated: True,
    }

    update_user_by_id(user_id, update_payload)
