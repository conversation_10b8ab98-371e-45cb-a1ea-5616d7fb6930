from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient


def get_access_token_by_repo_id(repo_id: str):
    """
    Functions fetches from archie_github_handler service the access token for the repo.
    It supports both ADO and GitHub.
    :param repo_id:
    :return:
    """
    with ServiceClient() as client:
        logger.debug(f"Will send a call to get access token for repo_id {repo_id}")
        response = client.get(
            "github",
            f"/v1/github/repositories/{repo_id}/secret",
        )
        response.raise_for_status()
        parsed_response = response.json()
        logger.debug(
            f"Got a response with keys {parsed_response.keys()}, "
            f"installation id is {parsed_response["installationID"]}, and svc type is {parsed_response['scvType']}"
        )
        return parsed_response["accessToken"]
