from datetime import UTC, datetime
from typing import Optional, Tuple

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (AccessRole, AccessType, GithubInstallation,
                                  GitHubInstallationAccess,
                                  GithubInstallationStatus,
                                  GithubInstallationType, 
                                  TeamMember,
                                  TeamMemberRole,
                                  VersionControlSystem)
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm import Session
from tenacity import (retry, retry_if_exception_type, stop_after_attempt,
                      wait_exponential)
from datetime import datetime, timezone
from src.service.github_installation_access_service import \
    create_github_installation_access_record_for_user
from src.gcp.secrets_handler import SecretManagerOperations

def determine_version_control_system(repo_id: str) -> VersionControlSystem:
    """
    Determine the version control system based on repository ID format.

    Azure DevOps uses UUIDs (e.g., "e2e0696f-9d4c-4f3d-aefc-dfa3de642276")
    GitHub uses numeric IDs (e.g., "957303073")

    Args:
        repo_id (str): The repository identifier

    Returns:
        VersionControlSystem: Either AZURE_DEVOPS or GITHUB

    Raises:
        ValueError: If the repo_id format doesn't match either system
    """
    import re

    logger.info(f"Determining version control system for repo_id: {repo_id}")

    # UUID pattern for Azure DevOps (8-4-4-4-12 hexadecimal format)
    uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'

    # Numeric pattern for GitHub (digits only)
    numeric_pattern = r'^\d+$'

    repo_id_lower = repo_id.lower().strip()
    logger.debug(f"Normalized repo_id for pattern matching: {repo_id_lower}")

    if re.match(uuid_pattern, repo_id_lower):
        logger.info(f"Repo ID {repo_id} matches UUID pattern - determined as Azure DevOps")
        return VersionControlSystem.AZURE_DEVOPS
    elif re.match(numeric_pattern, repo_id_lower):
        logger.info(f"Repo ID {repo_id} matches numeric pattern - determined as GitHub")
        return VersionControlSystem.GITHUB
    else:
        logger.error(f"Unknown repository ID format: {repo_id} (normalized: {repo_id_lower})")
        raise ValueError(f"Unknown repository ID format: {repo_id}")


def create_new_git_installation(
        user_id: str, status: GithubInstallationStatus, installation_id: str,
        target_id: str, target_name: str, svc_type: VersionControlSystem, metadata: dict
) -> str:
    """
    Create or update a Git installation for a given user with the provided status and type(azure or github).
    Also creates a record for the user's access to the installation.

    :param user_id: Unique identifier for the user.
    :param status: Current status of the GitHub installation.
    :param installation_id: The installation id.
    :param target_id: The id of organization or user installation was performed for
    :param target_name: The name of organization or user installation was performed for
    :param svc_type: The type of service(azure or github) for which the installation was performed.
    :param metadata: Additional metadata to be saved with the installation, such as user_info.
    :return: The created or updated Git installation id.
    """
    with get_db_session() as session:
        try:
            installation = create_new_git_installation_records(
                user_id=user_id, status=status, installation_id=installation_id,
                target_name=target_name, target_id=target_id, installation_type=GithubInstallationType.USER,
                svc_type=svc_type, metadata=metadata, session=session)
            session.commit()
            return installation.id
        except IntegrityError as e:
            session.rollback()
            raise e
        except SQLAlchemyError as e:
            session.rollback()
            raise e
        except Exception as e:
            # Handle any errors that occurred during commit
            session.rollback()
            raise e


@retry(
    retry=retry_if_exception_type(IntegrityError),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
def create_new_git_installation_records(
    user_id: str, status: GithubInstallationStatus, installation_id: str,
    target_name: str, target_id: str, installation_type: GithubInstallationType,
    svc_type: VersionControlSystem,
    metadata: dict, session: Session,
) -> GithubInstallation:
    """
    Creates a new GithubInstallation and GitHubInstallationAccess records in the database if they don't already exist.
    """
    active_installation = get_active_git_installation_by_id_and_user_id(installation_id, user_id, session)

    if active_installation:
        logger.info(
            f"We have already existing active github installation {active_installation.id} record "
            f"for user {user_id}, will not create new record."
        )
    else:
        logger.info(f"Creating new github installation record for user {user_id}")
        active_installation = GithubInstallation(
            installation_id=installation_id,
            installation_metadata=metadata,
            installation_type=installation_type,
            status=status,
            target_name=target_name,
            target_id=target_id,
            user_id=user_id,
            svc_type=svc_type,
        )

        status_check(active_installation, user_id)
        session.add(active_installation)
        session.flush()

        create_github_installation_access_record_for_user(active_installation.id, user_id, session)
        logger.info(f"Created github installation access record for user {user_id}")
    return active_installation


def status_check(installation: GithubInstallation, user_id: str):
    """
    Check the status of a given GithubInstallation and update its installed_at attribute if
    the installation is active.

    :param installation: The GithubInstallation instance whose status is to be checked.
    :param user_id: The user id of the user who installed the app.
    """
    if installation.status == GithubInstallationStatus.ACTIVE:
        installation.installed_at = datetime.now(UTC)
    elif installation.status == GithubInstallationStatus.PENDING:
        installation.requires_approval = True
        installation.requested_by = user_id
        installation.requested_at = datetime.now(UTC)


def create_git_installation_access_record(github_installation: GithubInstallation,
                                          user_id: str, session: Optional[Session] = None):
    """
    Creates a new record for a pending GitHub installation request in the database using payload and
    github_installation object.

    :param github_installation: Represents the GitHub installation details required to populate
        the `requested_by` field of the pending installation record.
    :param user_id: User id.
    :param session: Session if any.
    """
    try:
        github_installation_access = GitHubInstallationAccess(
            integration_id=github_installation.id,
            entity_id=user_id,
            access_type=AccessType.USER,
            role=AccessRole.OWNER,
            is_owner=True
        )
        session.add(github_installation_access)
        session.flush()
    except IntegrityError:
        logger.info("Record for pending installation already exists. No need to create new record.")


def get_active_git_installation_by_id_and_user_id(
        installation_id: str,
        user_id: str,
        session: Session) -> Optional[GithubInstallation]:
    installation = (
        session.query(GithubInstallation)
        .filter(
            GithubInstallation.installation_id == installation_id,
            GithubInstallation.user_id == user_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
        ).first()
    )
    return installation


def get_active_git_integration_by_installation_and_svc_type(
        installation_id: str,
        svc_type: VersionControlSystem,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get the latest active git installation info by installation id and svc_type.
    :param installation_id: which is tenant_id for azure.
    :param svc_type: Version control system we want to get installation info for, e.g. GitHub, GitLab, Bitbucket...
    :param session: Session if any.
    :return: GithubInstallation model if found or None.
    """
    with get_db_session(session) as session:
        github_installation = (session.query(GithubInstallation)
                                      .filter(GithubInstallation.installation_id == installation_id,
                                              GithubInstallation.svc_type == svc_type,
                                              GithubInstallation.status == GithubInstallationStatus.ACTIVE)
                                      .order_by(GithubInstallation.created_at.desc())
                                      .first())
        if not github_installation:
            return None

        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation


def get_active_git_integration_by_installation_and_svc_type(
        installation_id: str,
        svc_type: VersionControlSystem,
        session: Optional[Session] = None
) -> Optional[GithubInstallation]:
    """
    Get the latest active git installation info by installation id and svc_type.
    :param installation_id: which is tenant_id for azure.
    :param svc_type: Version control system we want to get installation info for, e.g. GitHub, GitLab, Bitbucket...
    :param session: Session if any.
    :return: GithubInstallation model if found or None.
    """
    with get_db_session(session) as session:
        github_installation = (session.query(GithubInstallation)
                                      .filter(GithubInstallation.installation_id == installation_id,
                                              GithubInstallation.svc_type == svc_type,
                                              GithubInstallation.status == GithubInstallationStatus.ACTIVE)
                                      .order_by(GithubInstallation.created_at.desc())
                                      .first())
        if not github_installation:
            return None

        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation


def get_active_installation_by_target_id(target_id: str, session: Optional[Session] = None) -> Optional[
    GithubInstallation]:
    """
    Fetches the active GitHub installation associated with the given target ID.

    :param target_id: The unique identifier for the target whose active GitHub installation needs to be retrieved.
    :param session: An optional database session object. If not provided, a new session is created temporarily.
    :return: An active GitHubInstallation object for the given target ID or None if no active installation is found.
    """
    with get_db_session(session) as session:
        installation = (session.query(GithubInstallation)
                        .filter(GithubInstallation.target_id == target_id,
                                GithubInstallation.status == GithubInstallationStatus.ACTIVE)
                        .first())
        if installation and not session:
            session.expunge(installation)
        return installation
    

def deactivate_azure_devops_installation_by_user_id(
    user_id: str,
    session: Optional[Session] = None
) -> bool:
    """
    Deactivate Azure DevOps installation for a user.

    Users can only disconnect their own installations.

    :param user_id: User whose installation should be deactivated
    :param session: Optional database session
    :return: True if installation found and deactivated, False if not found
    """
    logger.info(f"Deactivating Azure DevOps installation for user {user_id}")

    with get_db_session(session) as db_session:
        # Get installation details
        installation_info = _get_user_azure_installation(db_session, user_id)
        if not installation_info:
            logger.warning(f"No active Azure DevOps installation found for user {user_id}")
            return False
        
        installation_id, installation_uuid, is_owner = installation_info
        
        # Perform database operations
        success = _deactivate_installation_in_db(db_session, installation_id, installation_uuid, user_id, is_owner)
        if not success:
            return False
        
        # Handle secret cleanup if user is owner
        if is_owner:
            _cleanup_azure_secret(installation_uuid)
        
        logger.info(f"Successfully processed installation {installation_uuid}")
        return True


def _get_user_azure_installation(db_session: Session, user_id: str) -> Optional[Tuple[str, str, bool]]:
    """
    Get user's Azure DevOps installation details.
    
    :return: Tuple of (installation_id, installation_uuid, is_owner) or None if not found
    """
    active_installation = (
        db_session.query(GithubInstallation)
        .join(GitHubInstallationAccess, GithubInstallation.id == GitHubInstallationAccess.integration_id)
        .filter(
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.AZURE_DEVOPS,
            GitHubInstallationAccess.entity_id == user_id
        )
        .first()
    )
    
    if not active_installation:
        return None
    
    installation_id = active_installation.id
    installation_uuid = active_installation.installation_id
    is_owner = active_installation.user_id == user_id
    
    return installation_id, installation_uuid, is_owner


def _deactivate_installation_in_db(db_session: Session, installation_id: str, installation_uuid: str, user_id: str, is_owner: bool) -> bool:
    """
    Perform database operations to deactivate installation.
    
    :return: True if successful, False if failed
    """
    # Only update installation status if user is the owner
    updated_rows = 0
    if is_owner:
        current_time = datetime.now(timezone.utc)
        updated_rows = db_session.query(GithubInstallation).filter(
            GithubInstallation.id == installation_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.AZURE_DEVOPS,
            GithubInstallation.user_id == user_id
        ).update({
            GithubInstallation.status: GithubInstallationStatus.UNINSTALLED,
            GithubInstallation.uninstalled_at: current_time
        }, synchronize_session=False)
        
        if updated_rows == 0:
            logger.warning(f"Installation {installation_id} was not updated - may already be deactivated or user not owner")
            return False
    
    # Always delete the user's access record
    deleted_access_rows = db_session.query(GitHubInstallationAccess).filter(
        GitHubInstallationAccess.integration_id == installation_id,
        GitHubInstallationAccess.entity_id == user_id
    ).delete(synchronize_session=False)
    
    db_session.commit()
    
    if is_owner:
        logger.info(f"Updated {updated_rows} installation(s), deleted {deleted_access_rows} access record(s)")
    else:
        logger.info(f"Removed user access - deleted {deleted_access_rows} access record(s)")
    
    return True


def _cleanup_azure_secret(installation_uuid: str) -> None:
    """
    Clean up Azure secret for installation.
    
    :param installation_uuid: Installation UUID to clean up secret for
    """
    try:
        secret_manager = SecretManagerOperations()
        secret_deleted = secret_manager.delete_secret('azure', installation_uuid)
        
        if secret_deleted:
            logger.info(f"Successfully deleted Azure secret for installation {installation_uuid}")
        else:
            logger.warning(f"Failed to delete Azure secret for installation {installation_uuid}")
            
    except Exception as e:
        logger.error(f"Error deleting Azure secret for installation {installation_uuid}: {str(e)}")
        # Don't propagate the error - secret cleanup is non-critical


def deactivate_azure_devops_installation_by_team_id(
    user_id: str,
    team_id: str,
    session: Optional[Session] = None
) -> bool:
    """
    Deactivate Azure DevOps installation for a specific team.

    Users can only disconnect teams they have access to.

    :param user_id: User making the request
    :param team_id: Team whose installation should be deactivated
    :param session: Optional database session
    :return: True if installation found and deactivated, False if not found
    """
    logger.info(f"Deactivating Azure DevOps installation for team {team_id} by user {user_id}")

    with get_db_session(session) as db_session:
        # Get installation details for the team
        installation_info = _get_team_azure_installation(db_session, user_id, team_id)
        if not installation_info:
            logger.warning(f"No active Azure DevOps installation found for team {team_id}")
            return False
        
        installation_id, installation_uuid, is_owner = installation_info
        
        # Perform database operations
        success = _deactivate_team_installation_in_db(db_session, installation_id, installation_uuid, team_id, is_owner)
        if not success:
            return False
        
        # Handle secret cleanup if user is owner
        if is_owner:
            _cleanup_azure_secret(installation_uuid)
        
        logger.info(f"Successfully processed team installation {installation_uuid} for team {team_id}")
        return True


def _get_team_azure_installation(db_session: Session, user_id: str, team_id: str) -> Optional[Tuple[str, str, bool]]:
    """
    Get team's Azure DevOps installation details.
    
    :param user_id: User making the request (for access validation)
    :param team_id: Team ID to find installation for
    :return: Tuple of (installation_id, installation_uuid, is_owner) or None if not found
    """
    active_installation = (
        db_session.query(GithubInstallation)
        .join(GitHubInstallationAccess, GithubInstallation.id == GitHubInstallationAccess.integration_id)
        .filter(
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.AZURE_DEVOPS,
             GitHubInstallationAccess.entity_id == team_id  # Search by team_id instead of user_id
        )
        .first()
    )
    
    if not active_installation:
        return None
    
    # Verify user is a member of this team first
    user_team_access = (
        db_session.query(TeamMember)
        .filter(
            TeamMember.user_id == user_id,
            TeamMember.team_id == team_id
        )
        .first()
    )
    
    if not user_team_access:
        logger.warning(f"User {user_id} is not a member of team {team_id}")
        return None
    
    # Log the user's actual role and what we're comparing against
    user_role = user_team_access.role
    required_roles = [TeamMemberRole.ADMIN, TeamMemberRole.OWNER]
    logger.info(f"User {user_id} has role: {user_role}, required roles: {required_roles}")
    
    # Check if user has admin privileges using if statement
    # Use SUPER_ADMIN instead of OWNER to match database values
    has_admin_privileges = user_role in [TeamMemberRole.ADMIN, TeamMemberRole.OWNER]
    
    if not has_admin_privileges:
        logger.warning(f"User {user_id} does not have admin privileges in team {team_id}. User role: {user_role}, required: ADMIN or SUPER_ADMIN")
        return None
    
    installation_id = active_installation.id
    installation_uuid = active_installation.installation_id
    is_owner = active_installation.user_id == user_id
    
    return installation_id, installation_uuid, is_owner


def _deactivate_team_installation_in_db(db_session: Session, installation_id: str, installation_uuid: str, team_id: str, is_owner: bool) -> bool:
    """
    Perform database operations to deactivate team installation.
    
    :return: True if successful, False if failed
    """
    # Only update installation status if user is the owner
    updated_rows = 0
    if is_owner:
        current_time = datetime.now(timezone.utc)
        updated_rows = db_session.query(GithubInstallation).filter(
            GithubInstallation.id == installation_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == VersionControlSystem.AZURE_DEVOPS
        ).update({
            GithubInstallation.status: GithubInstallationStatus.UNINSTALLED,
            GithubInstallation.uninstalled_at: current_time
        }, synchronize_session=False)
        
        if updated_rows == 0:
            logger.warning(f"Installation {installation_id} was not updated - may already be deactivated")
            return False
    
    # Delete the team's access record
    deleted_access_rows = db_session.query(GitHubInstallationAccess).filter(
        GitHubInstallationAccess.integration_id == installation_id,
        GitHubInstallationAccess.entity_id == team_id
    ).delete(synchronize_session=False)
    
    db_session.commit()
    
    if is_owner:
        logger.info(f"Updated {updated_rows} installation(s), deleted {deleted_access_rows} team access record(s)")
    else:
        logger.info(f"Removed team access - deleted {deleted_access_rows} access record(s)")
    
    return True