import os
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from blitzy_utils.logger import logger
from blitzy_utils.service_client import ServiceClient
from common_models.db_client import get_db_session
from common_models.models import AttachmentMetadata, AttachmentStatus
from sqlalchemy.orm import Session
from werkzeug.datastructures import FileStorage


async def upload_binary_to_admin_service_async(file_data: bytes, file_path: str,
                                               company_id: str, content_type: str) -> str:
    """
    Async upload binary file to GCS using the admin service.

    :param file_data: Binary file data
    :param file_path: File path in GCS
    :param company_id: Company ID for bucket naming
    :param content_type: MIME type of the file
    :return: GCS path of the uploaded file
    :raises Exception: If admin service is not available or upload fails
    """
    admin_url = os.environ.get("SERVICE_URL_ADMIN")

    if not admin_url:
        raise Exception("Admin service URL not configured. Cannot upload file.")

    logger.info(f"Async uploading binary file to admin service: {file_path} for company {company_id}")

    async with ServiceClient() as client:
        response = await client.async_post_binary(
            service_name="admin",
            endpoint="/v1/storage/upload-async",
            data=file_data,
            params={
                'file_path': file_path,
                'company_id': company_id
            },
            headers={
                'Content-Type': content_type
            },
            timeout=600  # 10 minute timeout for large files
        )

        response.raise_for_status()
        result = response.json()

        file_path_from_admin = result.get('filePath')

        # Admin service returns bucket-name/file/path format, convert to full GCS path
        if file_path_from_admin and not file_path_from_admin.startswith('gs://'):
            gcs_path = f"gs://{file_path_from_admin}"
        else:
            gcs_path = file_path_from_admin

        logger.info(f"Successfully async uploaded binary file via admin service to: {gcs_path}")

        return gcs_path


def save_attachment_metadata(attachment: AttachmentMetadata, session: Optional[Session] = None) -> AttachmentMetadata:
    """
    Save attachment metadata to database.
    :param attachment: AttachmentMetadata object to save.
    :param session: Session if any.
    :return: AttachmentMetadata object.
    """
    try:
        logger.info(f"Attempting to save attachment metadata with ID: {attachment.id}")
        logger.debug(f"Attachment details: project_id={attachment.project_id}, "
                     f"file_name={attachment.file_name}, gcs_path={attachment.gcs_path}")

        # If we're given an existing session, use it (don't commit within transaction)
        if session is not None:
            session.add(attachment)
            session.flush()
            logger.info(f"✅ Added attachment metadata to existing transaction with ID: {attachment.id}")
            return attachment

        # If no session provided, create new one and commit immediately
        with get_db_session(session) as new_session:
            new_session.add(attachment)
            new_session.commit()  # Explicitly commit the transaction
            logger.info(f"✅ Successfully saved attachment metadata with ID: {attachment.id}")
            return attachment

    except Exception as e:
        logger.error(f"❌ Error saving attachment metadata with ID {attachment.id}: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        raise


def get_attachments_by_project_id(project_id: str, tech_spec_id: Optional[str] = None,
                                  session: Optional[Session] = None) -> List[AttachmentMetadata]:
    """
    Get all attachments for a project, optionally filtered by tech spec ID.
    :param project_id: Project ID.
    :param tech_spec_id: Optional tech spec ID to filter by.
    :param session: Session if any.
    :return: List of AttachmentMetadata objects.
    """
    with get_db_session(session) as session:
        query = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.project_id == project_id,
            AttachmentMetadata.deleted_at.is_(None)
        )

        if tech_spec_id:
            query = query.filter(AttachmentMetadata.tech_spec_id == tech_spec_id)

        return query.all()


def get_attachment_by_id(attachment_id: str, session: Optional[Session] = None) -> Optional[AttachmentMetadata]:
    """
    Get attachment by ID.
    :param attachment_id: Attachment ID.
    :param session: Session if any.
    :return: AttachmentMetadata object if found.
    """
    try:
        logger.info(f"Attempting to retrieve attachment with ID: {attachment_id}")

        with get_db_session(session) as session:
            attachment = session.query(AttachmentMetadata).filter(
                AttachmentMetadata.id == attachment_id,
                AttachmentMetadata.deleted_at.is_(None)
            ).first()

            if attachment:
                logger.info(f"✅ Found attachment with ID: {attachment_id}")
            else:
                logger.warning(f"❌ No attachment found with ID: {attachment_id}")

            return attachment

    except Exception as e:
        logger.error(f"❌ Error retrieving attachment with ID {attachment_id}: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        raise


async def upload_file_to_gcs_async(file: FileStorage, company_id: str, project_id: str,
                                   attachment_id: str) -> str:
    """
    Async upload a binary file to Google Cloud Storage using the admin service.
    Follows the new bucket structure: company_{company_id}/attachments_{project_id}/

    :param file: File object to upload.
    :param company_id: Company ID for bucket naming.
    :param project_id: Project ID for organizing files.
    :param attachment_id: Attachment ID for unique file naming.
    :return: GCS path of the uploaded file.
    """
    # Create file path following the new structure:
    # company_{company_id}/attachments_{project_id}/{attachment_id}.{extension}
    file_extension = file.filename.split('.')[-1] if '.' in file.filename else ''
    gcs_file_name = f"{attachment_id}.{file_extension}" if file_extension else attachment_id

    # New bucket structure: company_{company_id}/attachments_{project_id}/
    if company_id:
        file_path = f"company_{company_id}/attachments_{project_id}/{gcs_file_name}"
    else:
        # For default company (empty company_id)
        file_path = f"attachments_{project_id}/{gcs_file_name}"

    # Reset file pointer to beginning and read file data
    file.seek(0)
    file_data = file.read()

    try:
        logger.info(f"Async uploading binary file {file.filename} ({len(file_data)} bytes)"
                    f" to {file_path} for company {company_id}")

        # Upload using admin service async
        gcs_path = await upload_binary_to_admin_service_async(
            file_data=file_data,
            file_path=file_path,
            company_id=company_id,
            content_type=file.content_type or 'application/octet-stream'
        )

        logger.info(f"Binary file async uploaded successfully to {gcs_path}")

        # Reset file pointer after upload for any subsequent operations
        file.seek(0)

        return gcs_path

    except Exception as e:
        logger.error(f"Failed to async upload file {file.filename} to GCS: {str(e)}")
        raise Exception(f"Async file upload failed: {str(e)}")


def download_attachment_from_gcs(gcs_path: str, company_id: str) -> bytes:
    """
    Download an attachment file from GCS using the admin service.

    :param gcs_path: Full GCS path of the file
    :param company_id: Company ID for bucket access
    :return: Binary file data
    :raises Exception: If admin service is not available or download fails
    """
    admin_url = os.environ.get("SERVICE_URL_ADMIN")

    if not admin_url:
        raise Exception("Admin service URL not configured. Cannot download file.")

    # Extract file path from GCS path (remove gs://bucket-name/ prefix)
    if gcs_path.startswith('gs://'):
        # Parse GCS path: gs://bucket-name/file/path
        parts = gcs_path.replace('gs://', '').split('/', 1)
        if len(parts) > 1:
            file_path = parts[1]
        else:
            raise Exception(f"Invalid GCS path format: {gcs_path}")
    else:
        file_path = gcs_path

    params = {
        'file_path': file_path,
        'company_id': company_id
    }

    logger.info(f"Downloading file from admin service: {file_path} for company {company_id}")

    with ServiceClient() as client:
        response = client.get(
            service_name="admin",
            endpoint="/v1/storage/download",
            params=params,
            timeout=300  # 5 minute timeout
        )

        response.raise_for_status()

        logger.info(f"Successfully downloaded file via admin service: {file_path}")
        return response.content


def create_attachment_metadata(file: FileStorage, project_id: str, uploaded_by_user_id: str,
                               tech_spec_id: Optional[str] = None,
                               user_description: Optional[str] = None) -> AttachmentMetadata:
    """
    Create AttachmentMetadata object from file upload.
    :param file: File object.
    :param project_id: Project ID.
    :param uploaded_by_user_id: User ID who uploaded the file.
    :param tech_spec_id: Optional tech spec ID.
    :param user_description: Optional user description.
    :return: AttachmentMetadata object.
    """
    attachment_id = str(uuid.uuid4())

    attachment = AttachmentMetadata(
        id=attachment_id,
        project_id=project_id,
        tech_spec_id=tech_spec_id,
        file_name=file.filename,
        mime_type=file.content_type or 'application/octet-stream',
        file_size=len(file.read()),  # Get file size
        gcs_path="",  # Will be set after upload
        uploaded_by_user_id=uploaded_by_user_id,
        upload_timestamp=datetime.now(timezone.utc),
        user_description=user_description,
        status=AttachmentStatus.UPLOADED.value,
        version="1.0"
    )

    # Reset file pointer after reading size
    file.seek(0)

    return attachment


async def upload_single_file_async(file_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Async upload a single file with isolated database connection.
    This function is designed to be run concurrently with other file uploads using asyncio.

    :param file_data: Dictionary containing all data needed for upload
    :return: Dictionary with result or error information
    """
    file = file_data['file']
    project_id = file_data['project_id']
    uploaded_by_user_id = file_data['uploaded_by_user_id']
    company_id = file_data['company_id']
    tech_spec_id = file_data.get('tech_spec_id')
    user_description = file_data.get('user_description')
    file_index = file_data['file_index']

    logger.info(f"[Async] Processing file {file_index}: {file.filename}")

    try:
        # Create isolated database session for this async task
        with get_db_session() as session:
            # Create attachment metadata
            attachment_data = create_attachment_metadata(
                file=file,
                project_id=project_id,
                uploaded_by_user_id=uploaded_by_user_id,
                tech_spec_id=tech_spec_id,
                user_description=user_description
            )

            logger.info(f"[Async] Created metadata for file {file_index}: {attachment_data.id}")

            try:
                # Upload file to GCS using async method
                gcs_path = await upload_file_to_gcs_async(
                    file=file,
                    company_id=company_id,
                    project_id=project_id,
                    attachment_id=attachment_data.id
                )

                # Update attachment with GCS path
                attachment_data.gcs_path = gcs_path
                attachment_data.status = AttachmentStatus.UPLOADED.value

                # Save to database
                save_attachment_metadata(attachment_data, session)

                # Explicitly commit the transaction to ensure data persists
                try:
                    session.commit()
                    logger.info(f"[Async] ✅ Committed transaction for attachment: {attachment_data.id}")
                except Exception as commit_error:
                    logger.error(f"[Async] ❌ Failed to commit transaction for {attachment_data.id}: {commit_error}")
                    session.rollback()
                    raise

                logger.info(f"[Async] Successfully processed file {file_index}: {file.filename} -> {gcs_path}")

                return {
                    'success': True,
                    'attachment_data': attachment_data,
                    'file_name': file.filename,
                    'file_index': file_index
                }

            except Exception as upload_error:
                # Mark as failed in database
                attachment_data.status = AttachmentStatus.FAILED.value
                attachment_data.error_message = str(upload_error)
                save_attachment_metadata(attachment_data, session)

                # Explicitly commit the transaction even for failed uploads
                try:
                    session.commit()
                    logger.info(f"[Async] ✅ Committed failed attachment transaction: {attachment_data.id}")
                except Exception as commit_error:
                    logger.error(f"[Async] ❌ Failed to commit failed attachment {attachment_data.id}: {commit_error}")
                    session.rollback()

                error_msg = f"Failed to async upload file {file.filename}: {str(upload_error)}"
                logger.error(f"[Async] {error_msg}")

                return {
                    'success': False,
                    'error': error_msg,
                    'file_name': file.filename,
                    'file_index': file_index
                }

    except Exception as e:
        error_msg = f"Failed to process file {file.filename}: {str(e)}"
        logger.error(f"[Async] {error_msg}")

        return {
            'success': False,
            'error': error_msg,
            'file_name': file.filename,
            'file_index': file_index
        }


async def process_batch_upload_async(files: List[FileStorage], project_id: str, uploaded_by_user_id: str,
                                     company_id: str, tech_spec_id: Optional[str] = None,
                                     user_descriptions: Optional[List[str]] = None,
                                     session: Optional[Session] = None) -> Dict[str, Any]:
    """
    Process batch upload of multiple files using async/await for better performance.
    Uses asyncio.gather() for concurrent uploads instead of ThreadPoolExecutor.

    :param files: List of file objects to upload.
    :param project_id: Project ID.
    :param uploaded_by_user_id: User ID who uploaded the files.
    :param company_id: Company ID for bucket naming.
    :param tech_spec_id: Optional tech spec ID.
    :param user_descriptions: Optional list of user descriptions for each file.
    :param session: Session if any (ignored in async processing).
    :return: Dictionary with results and errors.
    """
    import asyncio

    results = []
    errors = []

    logger.info(f"🚀 Starting async batch upload of {len(files)} files for project {project_id}")

    # Prepare file data for async processing
    file_data_list = []
    for i, file in enumerate(files):
        user_description = None
        if user_descriptions and i < len(user_descriptions):
            user_description = user_descriptions[i]

        file_data = {
            'file': file,
            'project_id': project_id,
            'uploaded_by_user_id': uploaded_by_user_id,
            'company_id': company_id,
            'tech_spec_id': tech_spec_id,
            'user_description': user_description,
            'file_index': i + 1
        }
        file_data_list.append(file_data)

    # Process files concurrently using asyncio.gather()
    logger.info(f"📊 Using async concurrency for {len(files)} files")

    try:
        # Create async tasks for all files
        tasks = [upload_single_file_async(file_data) for file_data in file_data_list]

        # Execute all tasks concurrently
        task_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, result in enumerate(task_results):
            file_data = file_data_list[i]

            if isinstance(result, Exception):
                # Handle exceptions from async tasks
                error_msg = f"Async upload failed for file {file_data['file'].filename}: {str(result)}"
                logger.error(error_msg)
                errors.append({
                    "fileName": file_data['file'].filename,
                    "error": error_msg
                })
            elif result and result.get('success'):
                results.append(result['attachment_data'])
                logger.info(f"✅ Completed async file {result['file_index']}: {result['file_name']}")
            else:
                errors.append({
                    "fileName": result.get('file_name', file_data['file'].filename),
                    "error": result.get('error', 'Unknown error')
                })
                logger.error(f"❌ Failed async file {result.get('file_index', i+1)}:"
                             f" {result.get('file_name', file_data['file'].filename)}")

    except Exception as e:
        logger.error(f"Unexpected error in async batch upload: {str(e)}")
        # If gather fails completely, add errors for all files
        for file_data in file_data_list:
            errors.append({
                "fileName": file_data['file'].filename,
                "error": f"Batch upload failed: {str(e)}"
            })

    # Log final summary
    logger.info(f"🎯 Async batch upload completed with {len(results)} successful uploads")

    # Log summary
    total_files = len(files)
    successful_uploads = len(results)
    failed_uploads = len(errors)

    if failed_uploads > 0:
        logger.warning(f"Async batch upload completed with {failed_uploads} errors")
        logger.info(f"Async upload summary: {successful_uploads}/{total_files} files uploaded successfully")
    else:
        logger.info(f"Async batch upload completed successfully: {successful_uploads}/{total_files} files uploaded")

    return {
        "results": results,
        "errors": errors if errors else None
    }


def delete_attachment(attachment_id: str, session: Optional[Session] = None) -> bool:
    """
    Soft delete an attachment by setting deleted_at timestamp.
    :param attachment_id: Attachment ID to delete.
    :param session: Session if any.
    :return: True if deleted successfully, False otherwise.
    """
    with get_db_session(session) as session:
        attachment = session.query(AttachmentMetadata).filter(
            AttachmentMetadata.id == attachment_id,
            AttachmentMetadata.deleted_at.is_(None)
        ).first()

        if not attachment:
            return False

        attachment.deleted_at = datetime.now(timezone.utc)
        session.flush()
        logger.info(f"Attachment {attachment_id} marked as deleted")
        return True
