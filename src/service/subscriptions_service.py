from typing import Optional

from common_models.db_client import get_db_session
from common_models.models import Subscription, SubscriptionType
from sqlalchemy import Row
from sqlalchemy.orm import Session


def get_subscription_plan_by_user_id(user_id: str, session: Optional[Session] = None) -> Optional[Row]:
    with get_db_session(session) as session:
        subscription_plan = session.query(Subscription.plan_name).filter(Subscription.user_id == user_id).first()

        if subscription_plan and not session:
            session.expunge(subscription_plan)

        return subscription_plan
