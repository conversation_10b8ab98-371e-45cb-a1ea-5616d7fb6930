from time import time

from blitzy_utils.common import blitzy_exponential_retry_service
from blitzy_utils.logger import logger
from cachetools import TTLCache

from github import Github, GithubIntegration
from src.consts import GITHUB_APP_ID, GITHUB_PRIVATE_KEY

# Global cache for access tokens (TTL should match GitHub's token expiration - typically 1 hour so making it 55 mins.)
token_cache = TTLCache(maxsize=100, ttl=3300)  # 55 mins TTL


class GithubAppConnection:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'integration'):  # Prevent re-initialization
            self.integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)

    @blitzy_exponential_retry_service()
    def _get_cached_access_token(self, installation_id: str) -> str:
        """
        Get cached access token or generate new one if not cached.

        :param installation_id: GitHub App installation ID
        :return: Access token string
        """
        cache_key = f"github_token_{installation_id}"

        # Check if token exists in cache
        if cache_key in token_cache:
            token_data = token_cache[cache_key]
            if (token_data['expires_at'] - 600) >  time():  # Allow 10 minutes buffer before expiration
                logger.debug(f"Using cached token for installation {installation_id}")
                return token_data['token']
            else:
                logger.debug(f"Cached token for installation {installation_id} is expired, generating new one")

        logger.info(f"Generating new token for installation {installation_id}")
        token_data = self.integration.get_access_token(int(installation_id))
        logger.info(
            f"Generated new token for installation {installation_id} with expiration at {token_data.expires_at}"
        )
        token_cache[cache_key] = {'token': token_data.token, 'expires_at': token_data.expires_at.timestamp()}
        return token_data.token

    @blitzy_exponential_retry_service()
    def get_client(self, installation_id: str) -> Github:
        """
        Retrieves a GitHub client instance using cached access token.

        :param installation_id: Identifier of the GitHub App installation
        :type installation_id: str
        :return: Configured GitHub client instance
        """
        access_token = self._get_cached_access_token(installation_id)
        return Github(access_token)

    def get_access_token(self, installation_id: str) -> str:
        """
        Generate and return a cached access token for a given installation ID.

        :param installation_id: The ID of the installation for which the access token
            is being generated.
        :return: Access token string
        """
        return self._get_cached_access_token(installation_id)

    def clear_cache(self, installation_id: str = None):
        """
        Manually clear cache for specific installation or all installations.

        :param installation_id: Specific installation to clear, or None for all
        """
        if installation_id:
            cache_key = f"github_token_{installation_id}"
            token_cache.pop(cache_key, None)
            logger.info(f"Cleared cache for installation {installation_id}")
        else:
            token_cache.clear()
            logger.info("Cleared all cached tokens")

    def get_cache_info(self):
        """
        Get information about current cache state for debugging.

        :return: Dict with cache statistics
        """
        return {
            "cache_size": len(token_cache),
            "max_size": token_cache.maxsize,
            "ttl": token_cache.ttl,
            "cached_installations": [
                key.replace("github_token_", "")
                for key in token_cache.keys()
                if key.startswith("github_token_")
            ]
        }
