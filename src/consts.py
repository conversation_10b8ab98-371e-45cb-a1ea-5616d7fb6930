import base64
import os

from blitzy_utils.consts import get_project_id


def load_private_key_from_base64(base64_key: str) -> str:
    try:
        # Decode base64 to bytes
        key_bytes = base64.b64decode(base64_key)
        # Convert bytes to string
        private_key = key_bytes.decode("utf-8")
        return private_key
    except Exception as e:
        raise ValueError(f"Failed to decode private key: {str(e)}")


GITHUB_APP_ID = os.environ["GITHUB_APP_ID"]
GITHUB_BASE64_PRIVATE_KEY = os.environ["GITHUB_PRIVATE_KEY"]
GITHUB_CLIENT_ID = os.environ["GITHUB_CLIENT_ID"]
GITHUB_CLIENT_SECRET = os.environ["GITHUB_CLIENT_SECRET"]

GITHUB_PRIVATE_KEY = load_private_key_from_base64(GITHUB_BASE64_PRIVATE_KEY)
PROJECT_ID = get_project_id()
