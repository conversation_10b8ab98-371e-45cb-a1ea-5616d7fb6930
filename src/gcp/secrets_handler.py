import json

from google.api_core import exceptions
from google.cloud import secretmanager

from src.consts import PROJECT_ID
from src.error.errors import SecretNotFoundError
from src.utils.logging import logger


class SecretManagerOperations:
    """
    A class to handle operations with Google Cloud Secret Manager.

    :param project_id: The Google Cloud project ID where secrets will be managed
    """

    def __init__(self):
        self.client = secretmanager.SecretManagerServiceClient()
        self.project_id = PROJECT_ID
        self.parent = f"projects/{PROJECT_ID}"

    def create_or_update_user_secret(self, user_id, secret_data):
        """
        Create a new secret for a user if it doesn't exist, or add a new version if it does.

        :param user_id: User identifier to create/update secret for
        :param secret_data: Dictionary containing the secret data to be stored as JSON
        :return: Dict containing operation result with keys:
                - operation: Either 'created' or 'updated'
                - secret_name: Full path of the secret
                - version_name: Full path of the version
                - state: State of the secret version
        """
        secret_id = f"user-secret-{user_id}"

        try:
            # Convert dict to JSON string
            secret_value = json.dumps(secret_data)

            try:
                # Try to create a new secret
                secret = self.client.create_secret(
                    request={
                        "parent": self.parent,
                        "secret_id": secret_id,
                        "secret": {
                            "replication": {
                                "automatic": {},
                            },
                        },
                    }
                )

                # Add the first version
                version = self.client.add_secret_version(
                    request={
                        "parent": secret.name,
                        "payload": {"data": secret_value.encode("UTF-8")},
                    }
                )

                return {
                    "operation": "created",
                    "secret_name": secret.name,
                    "version_name": version.name,
                    "state": version.state.name
                }

            except exceptions.AlreadyExists:
                # If secret exists, add a new version
                parent = f"{self.parent}/secrets/{secret_id}"
                version = self.client.add_secret_version(
                    request={
                        "parent": parent,
                        "payload": {"data": secret_value.encode("UTF-8")},
                    }
                )

                return {
                    "operation": "updated",
                    "secret_name": parent,
                    "version_name": version.name,
                    "state": version.state.name
                }

        except TypeError as e:
            raise ValueError(f"Invalid JSON data: {str(e)}")
        except Exception as e:
            raise Exception(f"Error managing secret: {str(e)}")

    def get_user_secret(self, user_id, version_id="latest"):
        """
        Get the value of a user's secret and parse it as JSON.

        :param user_id: User identifier to retrieve secret for
        :param version_id: Version of the secret (default is "latest")
        :return: Dictionary containing the decoded JSON data
        """
        secret_id = f"user-secret-{user_id}"
        try:
            name = f"{self.parent}/secrets/{secret_id}/versions/{version_id}"
            response = self.client.access_secret_version(request={"name": name})
            json_string = response.payload.data.decode("UTF-8")
            try:
                return json.loads(json_string)
            except json.JSONDecodeError as e:
                raise ValueError(f"Invalid JSON in stored secret: {str(e)}")

        except exceptions.NotFound:
            raise ValueError(f"Secret for user {user_id} not found")
        except Exception as e:
            raise Exception(f"Error accessing secret: {str(e)}")

    def delete_user_secret(self, user_id):
        """
        Delete a user's secret and all its versions.

        :param user_id: User identifier whose secret should be deleted
        :return: True if deletion was successful
        """
        secret_id = f"user-secret-{user_id}"
        try:
            name = f"{self.parent}/secrets/{secret_id}"
            self.client.delete_secret(request={"name": name})
            return True
        except exceptions.NotFound:
            logger.error(f"Secret for user {user_id} not found")
            raise SecretNotFoundError("Secret not found")
        except Exception as e:
            raise Exception(f"Error deleting secret: {str(e)}")

    def list_all_user_secrets(self):
        """
        List all user secrets in the project.

        :return: List of user secret names, each name being the full path to the secret
        :raises Exception: If there's an error listing the secrets
        """
        try:
            secrets = self.client.list_secrets(request={"parent": self.parent})
            # Filter only user secrets
            user_secrets = [
                secret.name for secret in secrets
                if secret.name.split('/')[-1].startswith('user-secret-')
            ]
            return user_secrets
        except Exception as e:
            raise Exception(f"Error listing secrets: {str(e)}")
